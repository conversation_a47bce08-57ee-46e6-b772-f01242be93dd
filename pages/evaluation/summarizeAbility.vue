<template>
	<view class="puzzleRobert">
		<image src="../../static/evaluation/sc03_bg.jpg" class='bgImg' />
		<TipsVue :trueNum="state.trial+'/9'" :continuousTrueNum="state.remainingTimes" text="已完成" text3="剩余次数" />
		<view class="puzzleRobert-zero center">
			<image class="puzzleRobert-zero-shape" mode="widthFix" :src="`${state.pref}/${state.data && state.data[state.trial === 9 ? 8 : state.trial][0].split(',')[0]}`" />
			<image class="puzzleRobert-zero-robert" mode="widthFix" :src="`${state.pref}/${state.data && state.data[state.trial === 9 ? 8 : state.trial][0].split(',')[1]}`" />
			<view class="puzzleRobert-zero-text">示例卡片</view>
		</view>
		<view class="puzzleRobert-remind">
			<view class="puzzleRobert-remind-box">
				<image src="../../static/evaluation/quickAieve-voice.png" class='puzzleRobert-remind-box-icon' mode="widthFix" @click="repeatAudio" />
				<view class="puzzleRobert-remind-box-text">{{state.trial === 0 ? '每回合最多5次翻卡机会，钥匙碎片就藏在和上面这个有相关联线索的卡片里！' : '本回合的钥匙位置可能已经更换，请重新查找！'}}</view>
			</view>
		</view>
		<view class="puzzleRobert-box" v-if="state.data">
			<template v-for="(item,index) in state.data[state.trial === 9 ? 8 : state.trial]" :key="item">
				<view v-if="index > 0 && index < 7" class='puzzleRobert-zero puzzleRobert-item center' @click="click(item,index)">
					<image v-if="state.clickArr.indexOf(item) !== -1 && state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item) !== -1" class="puzzleRobert-item-img"
						:src="state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item) === 1 ? state.keyList[state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item)] : state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item) > 10 && state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item) < 20 ? state.keyList[0] : state.keyList[2]" />
					<image v-else-if="state.clickArr.indexOf(item) !== -1 && state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(item) === -1 " class="puzzleRobert-item-img"
						src='../../static/evaluation/sc03_keype0.png' />
					<view v-else>
						<image class="puzzleRobert-zero-shape" mode="widthFix" :src="`${state.pref}/${item.split(',')[0]}`" />
						<image class="puzzleRobert-zero-robert" mode="widthFix" :src="`${state.pref}/${item.split(',')[1]}`" />
					</view>
				</view>
			</template>

		</view>
		<view class="puzzleRobert-mask" v-if="state.showPopToast===1">
			<image class='puzzleRobert-mask-img' mode="widthFix" src='../../static/evaluation/sc03_succ.png' />
		</view>
		<view class="puzzleRobert-mask" v-else-if="state.showPopToast===2">
			<image class='puzzleRobert-mask-img' mode="widthFix" src='../../static/evaluation/sc03_fail.png' />
		</view>
		<view class="puzzleRobert-mask" v-else-if="state.showPopToast === 3">
			<view class="puzzleRobert-mask-box">
				<view class="puzzleRobert-mask-box-top center">测评结束</view>
				<view class="puzzleRobert-mask-box-content">
					<view class="puzzleRobert-mask-box-content-text">测评三已结束，你帮助罗伯特找到了{{state.key}}个钥匙，生成了专注力报告</view>
					<view class="puzzleRobert-mask-box-content-btn center" @click="go">查看报告</view>
				</view>
			</view>
		</view>
		<VideoMask v-if="state.teaching" video='../../static/video/sc03_v.mp4' @event="start" text='做好准备' :videoBtn="state.videoState" />
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		navigateTo,
		showToast,
		reLaunch,
		getStorageSync
	} from "../../common/uniTool";
	import TipsVue from "../../components/Tips.vue";
	import VideoMask from "../../components/VideoMask.vue";
	import {
		getRandomPic,
		postFreeAddRec,
		postFreeAddRecTime
	} from "../../service/evaluation"
	import {
		onHide
	} from '@dcloudio/uni-app';
	import {
		reportUrl
	} from "../../common/global";
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		useHelper
	} from "../../stores/helper";
	import ws from '@/utils/websocket.js'
	const socket = useSocketStore()
	const helper = useHelper(); //websocket仓库
	const innerAudioContextRef = ref(null) //音频
	const clickAudioRef = ref(null)
	const isClickRef = ref(false) //是否可以点击
	const submitRef = ref(false)
	const state = reactive({
		trueNum: 0, //找到碎片个数
		remainingTimes: 5, //剩余次数
		data: null,
		position: '', //点击卡片位置
		trial: 0, //游戏trial
		pref: '',
		clickArr: [], //选中的数组
		showPopToast: 0, //显示提示
		key: 0,
		teaching: true,
		keyList: ['../../static/evaluation/sc03_keype1.png', '../../static/evaluation/sc03_keype1.png',
			'../../static/evaluation/sc03_keype1.png'
		],
		videoState: {
			status: true,
			img1: 'http://**************:9900/resources/quickAieve/evaluation-videoFinishBtn.png',
			img2: 'http://**************:9900/resources/quickAieve/evaluation-videoBtn.png'
		}, //教学视频自定义按钮
		answer: [] //答案
	})
	onMounted(() => {
		innerAudioContextRef.value = uni.createInnerAudioContext()
		clickAudioRef.value = uni.createInnerAudioContext()
		innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_key2.mp3'

		getRandomPic().then(res => {
			let {
				pref,
				...newData
			} = res.data;
			state.data = newData
			console.log(newData, '------------newData');
			state.pref = pref
		})
	})
	onHide(() => {
		innerAudioContextRef.value && innerAudioContextRef.value.destroy()
		clickAudioRef.value && clickAudioRef.value.destroy()
	})
	watch([() => state.clickArr, () => state.trueNum, () => state.position], ([clickArr, trueNum, position]) => {
		if (trueNum === 3) {
			state.showPopToast = 1
			state.key++
			state.answer.push(`${position},true`)
			setTimeout(() => {
				restGame()
			}, 1000);
		} else if (clickArr.length === 5) {
			state.answer.push(`${position},false`)
			state.showPopToast = 2
			setTimeout(() => {
				restGame()
			}, 1000);
		}
	})

	watch(() => state.trial, (trial) => {
		if (trial === 9) {
			innerAudioContextRef.value.stop()
			innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_end.mp3'
			innerAudioContextRef.value.play()
			state.showPopToast = 3
			addData()
			return
		}
		if (trial > 0) {
			innerAudioContextRef.value.stop()
			innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_key3.mp3'
			innerAudioContextRef.value.play()
			state.position = ''
			state.clickArr = []
			state.remainingTimes = 5
			state.trueNum = 0

		}
	})
	watch(() => state.showPopToast, (showPopToast) => {
		console.log(showPopToast, '----------state.showPopToast');
	})
	const restGame = () => {
		state.showPopToast = 0
		state.trial = state.trial + 1
	}
	const addData = () => {
		postFreeAddRec({
			screenType: 3,
			puzzleData: state.answer
		}).then(res => {
			submitRef.current = true
			showToast('成功提交');
		}).catch(() => {
			showToast('提交失败')
		})
	}
	const start = () => {
		postFreeAddRecTime({
			screenType: 3
		}).then(res => {
			console.log('提交时间完成');
		})
		state.teaching = false
		innerAudioContextRef.value.play()
		innerAudioContextRef.value.onEnded(() => {
			if (state.trial === 0) {
				isClickRef.value = true
			}
		})
	}
	onUnmounted(() => {
		console.log('组件卸载');

	})
	const go = () => {
		if (socket.socketTask) {
			socket.socketTask.closeSocket()
		}
		if (helper.helperBlu) {
			helper.helperBlu.disconnectDevice(helper.address)
		}
		let tToken = getStorageSync('ttoken');
		navigateTo(`/pages/webview/index?url=${reportUrl}#/eegReport/${tToken}/${round}/${traineeId}/SMSCODE`)
	}
	const click = (value, pos) => {
		if (!isClickRef.value) {
			showToast('请听完提示哦~')
			return
		}

		const arr = state.clickArr
		let newPosition = state.position
		const index = arr.indexOf(value)
		const index2 = newPosition.indexOf(pos - 1)
		if (index !== -1) {
			return
		}
		if (index == -1) {
			arr.push(value)
		}
		console.log(arr, '------arr');
		if (index2 == -1) {
			newPosition = `${newPosition}${pos - 1}`
		}
		state.remainingTimes--
		if (state.data[state.trial === 9 ? 8 : state.trial][7].indexOf(value) !== -1) {
			innerAudioContextRef.value.stop()
			innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_right.mp3'
			innerAudioContextRef.value.play()
			state.trueNum++
		} else {
			innerAudioContextRef.value.stop()
			innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_wrong.mp3'
			innerAudioContextRef.value.play()
		}
		state.position = newPosition
		state.clickArr = arr
	}
	const repeatAudio = () => {
		innerAudioContextRef.value.stop()
		innerAudioContextRef.value.src = 'http://**************:9900/quickAieve/sc03/sc03_key2.mp3'
		innerAudioContextRef.value.play()
	}
</script>

<style lang="scss">
	.puzzleRobert {
		width: 100%;
		height: 100vh;
		padding-top: 46rpx;
		position: relative;

		&-tips {
			width: 100%;
			padding: 0 42rpx;
			display: flex;
			padding-top: 50rpx;
			justify-content: space-between;
			margin-bottom: 60rpx;
			align-items: center;

			&-left {
				display: flex;
				align-items: center;
				height: 88rpx;
				width: 200rpx;
				position: relative;

				&-img {
					width: 100rpx;
					height: 100rpx;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: 0;
					z-index: 1;
				}

				&-num {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: 44rpx;
					width: 224rpx;
					height: 60rpx;
					line-height: 60rpx;
					background: rgba(85, 129, 152, 0.5);
					box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(255, 255, 255, 0.5),
						inset 0rpx 1rpx 11rpx 0rpx rgba(102, 157, 185, 1);
					border-radius: 40rpx;
					text-align: center;
					font-size: 38rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
					text-shadow: 0rpx 2rpx 4rpx #2a6a8a;

					&-show {
						background: rgba(110, 237, 255, 0.2);
						box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(110, 237, 255, 1),
							inset 0rpx 1rpx 11rpx 0rpx rgba(110, 237, 255, 1);
						border-radius: 40rpx;
						border: 4rpx solid #6eedff;
					}
				}
			}

			&-right {
				display: flex;
				align-items: center;
				position: relative;
				height: 88rpx;

				&-img {
					width: 100rpx;
					height: 100rpx;
					position: absolute;
					right: 118rpx;
					z-index: 1;
					top: 50%;
					transform: translateY(-50%);
				}

				&-num {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					right: 0;
					width: 160rpx;
					height: 60rpx;
					line-height: 60rpx;
					background: rgba(85, 129, 152, 0.5);
					box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(255, 255, 255, 0.5),
						inset 0rpx 1rpx 11rpx 0rpx rgba(102, 157, 185, 1);
					border-radius: 40rpx;
					font-size: 38rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
					line-height: 48rpx;
					text-shadow: 0rpx 2rpx 4rpx #2a6a8a;
				}
			}
		}

		&-remind {
			position: relative;
			width: 650rpx;
			margin: 30rpx auto;

			&-box {
				width: 100%;
				background: #336d92;
				border-radius: 16rpx;
				padding: 14rpx 6rpx;
				display: flex;
				align-items: center;

				&-icon {
					vertical-align: middle;
					margin-right: 6rpx;
					width: 28rpx;
				}

				&-text {
					font-size: 17rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #FFFFFF;
				}
			}
		}

		&-zero {
			width: 190rpx;
			height: 190rpx;
			background: #fffcf9;
			box-shadow: 0rpx 4rpx 2rpx 0rpx rgba(97, 145, 178, 1);
			border-radius: 24rpx;
			border: 3rpx solid rgba(255, 238, 214, 1);
			margin: 76rpx auto;
			position: relative;

			&-shape {
				width: 164rpx;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%)
			}

			&-robert {
				width: 120rpx;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%)
			}

			&-text {
				position: absolute;
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #ffffff;
				bottom: -50rpx;
			}
		}

		&-item {
			margin: 0;
			margin-top: 40rpx;
			position: relative;

			&-img {
				width: 200rpx;
				height: 200rpx;
				position: absolute;
			}
		}

		&-box {
			display: flex;
			flex-wrap: wrap;
			padding: 0 48rpx;
			justify-content: space-between;
		}

		&-mask {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-img {
				width: 100%;
				position: absolute;
				top: 46%;
				transform: translateY(-50%);
			}

			&-box {
				position: absolute;
				top: 33%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
				width: 650rpx;
				border-radius: 24rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				&-top {
					width: 650rpx;
					height: 104rpx;
					background: linear-gradient(263deg, #79ccff 0%, #2397ff 100%);
					box-shadow: 0rpx 5rpx 0rpx 0rpx rgba(67, 150, 223, 0.44);
					border-radius: 24rpx 24rpx 0rpx 0rpx;
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}

				&-content {
					background: #e0faf9;
					border-radius: 0 0 24rpx 24rpx;

					&-text {
						padding: 48rpx 92rpx;
						font-size: 32rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #0186f4;
						line-height: 48rpx;
					}

					&-btn {
						width: 570rpx;
						height: 88rpx;
						background: #0485f4;
						box-shadow: inset 0rpx 1rpx 16rpx 0rpx rgba(255, 255, 255, 0.5);
						border-radius: 16rpx;
						font-size: 40rpx;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: 600;
						color: #ffffff;
						margin: 0 auto 40rpx auto;
					}
				}
			}
		}
	}
</style>