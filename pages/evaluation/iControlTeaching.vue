<template>
	<view class="helpRobert">
		<image src="/static/evaluation/screenbg.jpg" class='helpRobert-bg' />
		<view class="helpRobert-teach" :style="{justifyContent:!state.finish?'center':''}">
			<view class="helpRobert-teach-text" v-if="state.finish" @click="againGame">
				<text class="iconfont">&#xe604;</text>再来一遍
			</view>
			教学模式
			<view class="helpRobert-teach-text" v-if="state.finish" @click="go">
				正式开始<text class="iconfont">&#xe60d;</text>
			</view>
		</view>

		<image v-show="showSig" class="helpRobert-sig" src="/static/evaluation/quickAieve-alarm.png" :style="{top:helper.bluData&&helper.bluData.signal===0?'210rpx':'150rpx'}" />
		<view class="helpRobert-aim center" v-if="state.timeShow">
			<image src="/static/evaluation/aim.png" class='helpRobert-aim-img' />
		</view>
		<view v-else class="helpRobert-content">
			<image v-if="state.photo && state.photo.value == 'square'" :src="`/static/evaluation/team-${imgList[state.fruitIndex].img1}.png`" class='helpRobert-content-boss' />
			<image v-else :src='`/static/evaluation/team-${imgList[state.fruitIndex].img2}.png`' class='helpRobert-content-qianting' />
		</view>
		<view class="helpRobert-tips center" v-if="state.startGame">
			<view class="helpRobert-tips-err" v-if="state.finish">
				上面这个感叹信号闪现时，什么都不要点 <br />
				实际测评中，只会闪现一次
				<!-- <image class="helpRobert-tips-err-sig" src="/static/evaluation/quickAieve-alarm.png" /> ，什么都不要点 -->
			</view>
			<text v-else>信号出现时，用你最快的反应点击</text>
		</view>
		<view class="helpRobert-bottom" v-if="state.startGame&&!state.finish">
			<view class="helpRobert-bottom-click" @click="click('square')">
				<view class="helpRobert-bottom-click-text center" v-show="state.photo && state.photo.value == 'square'&&!state.timeShow">
					左手食指单击
				</view>
				<image class='helpRobert-bottom-img' mode="widthFix" :src="state.isClicked == 'square' ? imgList[state.fruitIndex].btn1Click
					: imgList[state.fruitIndex].btn1" />
				<image style="left: 0;" v-if="state.photo && state.photo.value == 'square'&&!state.timeShow" class="helpRobert-bottom-click-img"
					src="../../static/evaluation/iControlTeaching-clcik-left.gif" mode="widthFix"></image>
				<image style="left: 0;" v-else class="helpRobert-bottom-click-img" src="../../static/evaluation/iControlTeaching-clcik-left.png" mode="widthFix"></image>
			</view>
			<view class="helpRobert-bottom-click" @click="click('triangle')">
				<view class="helpRobert-bottom-click-text center" v-show="state.photo && state.photo.value == 'triangle'&&!state.timeShow">
					右手食指单击
				</view>
				<image class='helpRobert-bottom-img' mode=" widthFix" :src="state.isClicked == 'triangle' ? imgList[state.fruitIndex].btn2Click : imgList[state.fruitIndex].btn2" />
				<image v-if="state.photo && state.photo.value == 'triangle'&&!state.timeShow" class="helpRobert-bottom-click-img" src="../../static/evaluation/iControlTeaching-clcik-right.gif"
					mode="widthFix"></image>
				<image v-else class="helpRobert-bottom-click-img" src="/static/evaluation/iControlTeaching-clcik-right.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="helpRobert-teaText" v-if="state.startGame">
			<image src="/static/evaluation/iControlTeaching-tips.png" class="helpRobert-teaText-img" mode="widthFix"></image>
		</view>
		<view class="helpRobert-toast" v-if="state.showToastBox">
			<view class="helpRobert-toast-box">
				<image class="helpRobert-toast-box-bg" src="/static/evaluation/inhibitoryControl-pop.png" mode="widthFix"></image>
				<image class="helpRobert-toast-box-again" src="/static/evaluation/helpRobert-again.png" mode="widthFix" @click="agagin"></image>
				<image class="helpRobert-toast-box-btn" src="/static/evaluation/helpRobert-btn-true.png" mode="widthFix" @click="nextLevel"></image>
			</view>
		</view>
		<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal" />
		<view class="helpRobert-teaBtn" v-if="!state.startGame">
			<view class="helpRobert-teaBtn-left center" @click="go">
				跳过
			</view>
			<view class="helpRobert-teaBtn-right center" @click="start">
				开始教学
			</view>
		</view>
		<view class="helpRobert-bottom" v-if="state.finish">
			<view class="helpRobert-bottom-click" @click="click('square')">
				<view class="helpRobert-bottom-click-text center" v-show="state.photo ">
					不要点击
				</view>
				<image class='helpRobert-bottom-img' mode="widthFix" :src="state.isClicked == 'square' ? imgList[state.fruitIndex].btn1Click
					: imgList[state.fruitIndex].btn1" />
				<image class="helpRobert-bottom-click-x" src="/static/evaluation/iControlTeaching-X.png" mode="widthFix"></image>
			</view>
			<view class="helpRobert-bottom-click" @click="click('triangle')">
				<view class="helpRobert-bottom-click-text center" v-show="state.photo">
					不要点击
				</view>
				<image class='helpRobert-bottom-img' mode=" widthFix" :src="state.isClicked == 'triangle' ? imgList[state.fruitIndex].btn2Click : imgList[state.fruitIndex].btn2" />
				<image class="helpRobert-bottom-click-x" src="/static/evaluation/iControlTeaching-X.png" mode="widthFix"></image>
			</view>
		</view>
		<!-- 		<view class="helpRobert-teaBtn" v-if="state.finish">
			<view class="helpRobert-teaBtn-left center" style="width: 276rpx;" @click="againGame">
				再来一遍教学
			</view>
			<view class="helpRobert-teaBtn-right center" style="width: 404rpx;" @click="go">
				我已明白，正式开始
			</view>
		</view> -->
		<VideoMask v-if="state.teaching" video='/static/video/sc01_v.mp4' @event="closeVideo" text='做好准备' :videoBtn="state.videoState" />
		<ToastVue v-if="state.showTrueToast" text='正确' icon='&#xe60a;' top='58%' color='#1FBF77' background='#FFFFFF' textColor='#111111' />
		<ToastVue v-if="state.showFalseToast" text='错误' icon='&#xe7fa;' top='58%' color='#FF4747' background='#FFFFFF' textColor='#111111' />
	</view>
</template>

<script setup>
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import DropoutReminderVue from "../../components/DropoutReminder.vue";
	import VideoMask from '../../components/VideoMask.vue';
	import TipsVue from '../../components/Tips.vue';
	import ToastVue from '../../components/Toast.vue';
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		navigateTo,
		showToast,
		redirectTo,
		setKeepScreenOn
	} from '../../common/uniTool';
	import {
		postFreeAddRec,
		postFreeAddRecTime
	} from '../../service/evaluation';
	import {
		useHelper
	} from "../../stores/helper";
	import {
		randomNum
	} from '../../common/method';
	import {
		wsUrl
	} from '../../common/global';
	import ws from '@/utils/websocket.js'
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		addRealEndTime,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	const loginStore = useLoginStore()
	const helper = useHelper(); //websocket仓库
	const socket = useSocketStore(); //websocket仓库
	const innerAudioContextRef = ref(null) //音频
	const changeContextRef = ref(null) //音频
	const qiantingContextRef = ref(null) //音频
	const bossContextRef = ref(null) //音频
	const timeRef = ref(0)
	const clickAudioRef = ref(null)
	const submitRef = ref(false)
	const signalRef = ref(null)
	const isClick = ref(false)
	const frequencyRef = ref(0) //正式游戏次数
	const timeerRef = ref(null) //定时器
	const photoArrRef = ref([{
			value: 'square',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'square',
			sig: true
		},
	])
	const photoIndexRef = ref(-1)
	const showSig = ref(false) //是否显示刺激
	const eceuId = ref('') //时间戳校准id
	const state = reactive({
		startGame: false,
		frequency: 3,
		timeShow: true, //过度点
		photo: {
			value: '',
			sig: null
		},

		// showSig: false, //是否显示刺激
		finish: false, //游戏结束
		showTrueToast: false, //显示提示
		showFalseToast: false, //显示提示
		teaching: true, //教学提醒
		isClicked: '',
		videoState: {
			status: true,
			img1: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoFinishBtn.png',
			img2: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoBtn.png'
		}, //教学视频自定义按钮
		trueNum: 0, //做对次数
		continuousTrueNum: 0, //连续做对次数
		answer: [],
		showToastBox: false, //展示播放语音提醒
		fruitIndex: 0,
	})
	const btnList = []
	const imgList = [{
			img1: '04',
			img2: '03',
			btn1: '/static/evaluation/btn-1-1.png',
			btn1Click: '/static/evaluation/btn-1-1-click.png',
			btn2: '/static/evaluation/btn-1-2.png',
			btn2Click: '/static/evaluation/btn-1-2-click.png',
			mp3L: '/static/audio/mp3-1-1.MP3',
			mp3R: '/static/audio/mp3-1-2.MP3',
		},
		{
			img1: '06',
			img2: '05',
			btn1: '/static/evaluation/btn-2-1.png',
			btn1Click: '/static/evaluation/btn-2-1-click.png',
			btn2: '/static/evaluation/btn-2-2.png',
			btn2Click: '/static/evaluation/btn-2-2-click.png',
			mp3L: '/static/audio/mp3-2-1.MP3',
			mp3R: '/static/audio/mp3-2-2.MP3',
		},
		{
			img1: '07',
			img2: '12',
			btn1: '/static/evaluation/btn-3-1.png',
			btn1Click: '/static/evaluation/btn-3-1-click.png',
			btn2: '/static/evaluation/btn-3-2.png',
			btn2Click: '/static/evaluation/btn-3-2-click.png',
			mp3L: '/static/audio/mp3-3-1.MP3',
			mp3R: '/static/audio/mp3-3-2.MP3',
		},
		{
			img1: '08',
			img2: '09',
			btn1: '/static/evaluation/btn-4-1.png',
			btn1Click: '/static/evaluation/btn-4-1-click.png',
			btn2: '/static/evaluation/btn-4-2.png',
			btn2Click: '/static/evaluation/btn-4-2-click.png',
			mp3L: '/static/audio/mp3-4-1.MP3',
			mp3R: '/static/audio/mp3-4-2.MP3',
		},
		{
			img1: '10',
			img2: '11',
			btn1: '/static/evaluation/btn-5-1.png',
			btn1Click: '/static/evaluation/btn-5-1-click.png',
			btn2: '/static/evaluation/btn-5-2.png',
			btn2Click: '/static/evaluation/btn-5-2-click.png',
			mp3L: '/static/audio/mp3-5-1.MP3',
			mp3R: '/static/audio/mp3-5-2.MP3',
		},
		{
			img1: '01',
			img2: '02',
			btn1: '/static/evaluation/quickAieve-submarine.png',
			btn1Click: '/static/evaluation/quickAieve-submarine-click.png',
			btn2: '/static/evaluation/quickAieve-neurons.png',
			btn2Click: '/static/evaluation/quickAieve-neurons-click.png',
			mp3L: '/static/audio/sc01_square.mp3',
			mp3R: '/static/audio/sc01_triangle.mp3',
		}
	]
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif
	})
	onHide(() => {
		if (timeerRef.value) {
			clearInterval(timeerRef.value)
			timeerRef.value = null
		}
		changeContextRef.value && changeContextRef.value.destroy()
		state.teaching = false
	})

	onMounted(() => {
		setKeepScreenOn()
		clickAudioRef.value = uni.createInnerAudioContext()
		state.fruitIndex = randomNum(0, 5)
	})


	watch(() => state.photo, (photo) => {
		if (photo.value) {
			timeRef.value = (new Date).getTime()
		}
		if (photo && photo.sig) {
			timeerRef.value = setInterval(() => {
				getSigEle()
			}, 1500)
		}
	})

	watch(() => state.timeShow, (timeShow) => {
		if (!timeShow) {
			if (innerAudioContextRef.value) {
				innerAudioContextRef.value.destroy()
			}
			isClick.value = true
			clearTime()
			getPhoto()
		} else {
			clickAudioRef.value.stop()
			state.isClicked = false
		}
	})
	const againGame = () => {
		redirectTo('/pages/evaluation/iControlTeaching')
	}
	const agagin = () => {
		changeContextRef.value.stop()
		changeContextRef.value.play()
	}
	const clearTime = () => {
		if (signalRef.value) {
			clearTimeout(signalRef.value)
			signalRef.value = null
		}
	}
	//定时器正在动
	const getPhoto = () => {
		state.frequency--
		frequencyRef.value += 1
		photoIndexRef.value += 1
		state.photo = photoArrRef.value[photoIndexRef.value]
		console.log("state.frequency: " + JSON.stringify(state.frequency));
		if (state.frequency === 0) {
			clearTime()
			if (isClick.value) {
				isClick.value = false
				state.finish = true
				return
			}
		}
	}
	const nextLevel = () => {
		state.showToastBox = false
		changeContextRef.value.destroy()
		setTimeout(() => {
			state.timeShow = false
		}, 1000);
	}
	const getSigEle = () => {
		showSig.value = true
		let time = setTimeout(() => {
			showSig.value = false
			clearTimeout(time)
		}, 120);
	}
	onUnmounted(() => {

		//#ifdef APP-PLUS
		if (socket.socketTask) {
			addRealEndTime(eceuId.value)
			eceuId.value = ''
			socket.socketTask.closeSocket()
		}
		//#endif
	})
	const go = () => {
		redirectTo('/pages/evaluation/inhibitoryControl')
	}

	const start = () => {
		state.startGame = true
		setTimeout(() => {
			state.timeShow = false
		}, 1000);
	}
	const closeVideo = () => {
		state.teaching = false
		if (innerAudioContextRef.value) {
			innerAudioContextRef.value.destroy()
		}
	}
	const click = (value) => {
		if (!isClick.value) {
			return
		}
		clickAudioRef.value.destroy()
		clickAudioRef.value = uni.createInnerAudioContext()
		state.isClicked = value
		isClick.value = false
		clearTime()
		let Response = ''
		if (value === state.photo.value && !state.photo.sig) {
			if (value === 'square') {
				clickAudioRef.value.playbackRate = 2.0
				clickAudioRef.value.src = '/static/audio/sc01_rabbit.mp3'
				Response = 'R21'
			} else {
				clickAudioRef.value.src = '/static/audio/sc01_right.mp3'
				Response = 'R22'
			}
			clickAudioRef.value.play()
			state.trueNum += 1
			state.continuousTrueNum += 1
			state.showTrueToast = true
			setTimeout(() => {
				state.showTrueToast = false
			}, 500);
		} else if (value !== state.photo.value && !state.photo.sig) {
			clickAudioRef.value.src = '/static/audio/sc01_wrong.mp3'
			Response = 'R20'
			clickAudioRef.value.play()
			state.continuousTrueNum = 0
			state.showFalseToast = true
			setTimeout(() => {
				state.showFalseToast = false
			}, 500);
		}
		if (value && state.photo.sig) {
			clickAudioRef.value.src = '/static/audio/sc01_wrong.mp3'
			Response = 'R20'
			clickAudioRef.value.play()
			state.continuousTrueNum = 0
		}

		if (frequencyRef.value === 3) {
			state.finish = true

			return
		}
		if (photoIndexRef.value == photoArrRef.value.length - 1) {
			return
		}

		signalRef.value = setTimeout(() => {
			clearTime()
			state.timeShow = true
			signalRef.value = setTimeout(() => {
				state.timeShow = false
			}, 1000);
		}, 600)
	}
</script>

<style lang="scss">
	.helpRobert {
		width: 100%;
		height: 100vh;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 40rpx;
		justify-content: space-between;
		padding-top: 20rpx;

		&-teaBtn {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-around;

			&-left {
				width: 216rpx;
				height: 108rpx;
				background: #FFFFFF;
				border-radius: 16rpx;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 500;
				font-size: 38rpx;
				color: #0485F4;
			}

			&-right {
				width: 448rpx;
				height: 108rpx;
				background: linear-gradient(180deg, #84C6FF 0%, #0485F4 100%);
				border-radius: 16rpx;
				border: 2rpx solid #45A9FF;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: bold;
				font-size: 38rpx;
				color: #FFFFFF;
			}
		}

		&-teach {
			width: 96%;
			height: 56rpx;
			background: rgba(17, 17, 17, 0.5);
			border-radius: 4rpx;
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: bold;
			font-size: 34rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10rpx 16rpx;

			&-text {
				font-size: 28rpx;
			}
		}

		&-teaText {
			position: absolute;
			bottom: 0rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #FF0000;
			z-index: 1;

			&-img {
				width: 448rpx;
			}
		}

		&-sig {
			position: absolute;
			top: 50rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 130rpx;
			height: 130rpx;
		}

		&-toast {
			width: 100vw;
			height: 100vh;
			z-index: 999;
			position: absolute;
			top: 0;
			left: 0;

			&-box {
				position: absolute;
				width: 90%;
				top: 30%;
				left: 50%;
				transform: translateX(-50%);

				&-bg {
					width: 100%;

				}

				&-again {
					width: 144rpx;
					position: absolute;
					top: 24rpx;
					right: 24rpx;
				}

				&-btn {
					width: 500rpx;
					position: absolute;
					bottom: 66rpx;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}

		&-videoBtn {
			width: 590rpx;
		}

		&-videoFinishBtn {
			width: 590rpx;
		}

		&-mask {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 100%;
				position: absolute;
				bottom: 288rpx;
				left: 0;

				&-img {
					width: 100%;
				}

				&-icon {
					width: 40rpx;
					position: absolute;
					top: 27rpx;
					right: 25rpx;
				}
			}

			&-btn {
				position: absolute;
				left: 50%;
				bottom: 100rpx;
				transform: translateX(-50%);
				width: 590rpx;
			}
		}

		&-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			z-index: -1;
		}

		&-bottom {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 10rpx;


			&-click {
				position: relative;

				&-x {
					width: 286rpx;
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					top: 8%;
				}

				&-text {
					width: 224rpx;
					height: 68rpx;
					background: #FFFFFF;
					border-radius: 69rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #111111;
					position: absolute;
					top: -88rpx;
					left: 50%;
					transform: translateX(-50%);
				}

				&-img {
					width: 240rpx;
					height: 240rpx;
					position: absolute;
					right: 0;
					bottom: 0;
				}
			}

			&-img {
				width: 340rpx;
				height: 340rpx;
			}
		}


		&-aim {
			flex: 1;

			&-img {
				width: 242rpx;
				height: 242rpx;
			}
		}

		&-tips {
			width: 96%;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 4rpx;
			position: absolute;
			bottom: 43%;
			font-weight: 400;
			font-size: 34rpx;
			color: #FF0000;
			text-align: center;
			padding: 16rpx 0;

			&-err {
				display: flex;
				align-items: center;

				&-sig {
					width: 54rpx;
					height: 54rpx;
					margin-left: 6rpx;
				}
			}
		}

		&-content {
			position: relative;
			width: 100%;
			flex: 1;


			&-qianting {
				position: absolute;
				width: 340rpx;
				height: 340rpx;
				left: 50%;
				transform: translateX(-50%);
				top: 12%;
			}

			&-boss {
				position: absolute;
				width: 340rpx;
				height: 340rpx;
				left: 50%;
				transform: translateX(-50%);
				top: 15%;
			}

			&-alarm {
				width: 120rpx;
				height: 120rpx;
				position: absolute;
				top: 10%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
			}
		}

		&-end {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 650rpx;
				height: 420rpx;
				background: #ffffff;
				border-radius: 24rpx;
				position: absolute;
				top: 30%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				align-items: center;
				justify-content: space-around;
				flex-direction: column;

				&-title {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
				}

				&-img {
					width: 100%;
				}

				&-text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}

				&-btn {
					width: 570rpx;
					height: 88rpx;
					background: #0485f4;
					border-radius: 16rpx;
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}
	}

	image {
		will-change: transform
	}
</style>