<template>
	<view class="evaluation">
		<image class="evaluation-top" src="/static/evaluation/evaluation-top.png" mode="widthFix"></image>
		<view class="evaluation-bluBox" @click="goDevice">
			<image class="evaluation-bluBox-img" :src="helper.address?'/static/evaluation/evaluation-blu.png':'/static/evaluation/evaluation-blu-no.png'" mode="widthFix"></image>
			<view class="evaluation-bluBox-text">
				<view class="evaluation-bluBox-text-top">
					{{helper.address?'设备编号：'+helper.connectingDevice.name :'佩戴脑电头环'}}
				</view>
				<view class="evaluation-bluBox-text-bottom">
					实时监测大脑专注度
				</view>
			</view>
			<view class="evaluation-bluBox-btn">
				设备管理
			</view>
		</view>
		<view class="evaluation-box">
			<view class="evaluation-box-title">
				评估量表
			</view>
			<view class="evaluation-box-content" @click="()=>go('questionStatus')">
				<view class="evaluation-box-content-left">
					<view class="evaluation-box-content-left-top">
						SNAP-IV评定量表(父母版) <view class="evaluation-box-content-left-top-text">3分钟 18题</view>
					</view>
					<view class="evaluation-box-content-left-center">
						有助于更了解您孩子专注与多动的情况
					</view>
					<view class="evaluation-box-content-left-bottom">
						各大医院临床使用 <text>国际诊断标准编制</text>
					</view>
				</view>
				<view class="evaluation-box-content-right center" :class="state.progress.questionStatus?'evaluation-box-content-right-finish':''">
					{{state.progress.questionStatus?'已完成':'问卷填写'}}
				</view>
			</view>
		</view>
		<view class="evaluation-box" style="margin-top: 24rpx">
			<view class="evaluation-box-title">
				神经心理测验
			</view>
			<view v-for="(item,index) in evaList" :key="item.type" @click="go(item.type,item.value)">
				<view class="evaluation-box-content" style="padding: 12rpx 16rpx;">
					<image :src="item.img" class="evaluation-box-content-img" mode="widthFix"></image>
					<view class="evaluation-box-content-left">
						<view class="evaluation-box-content-left-top">
							{{item.name}}
							<view class="evaluation-box-content-left-top-text">{{item.time}}</view>
						</view>
						<view class="evaluation-box-content-left-center" style="margin: 0;margin-top: 16rpx;">
							{{item.text}}
						</view>
					</view>
					<view class="evaluation-box-content-right center" :class="item.value?'evaluation-box-content-right-finish':''">
						{{item.value?'已完成':'未测评'}}
					</view>
				</view>
			</view>
		</view>
		<view class="evaluation-finish center"
			:class="state.progress.questionStatus||state.progress.memoryStatus||state.progress.cancellatStatus||state.progress.signalStatus?'evaluation-finish-no':''" @click="goReport">
			结束测评，生成报告
		</view>
		<view class="evaluation-tips">
			根据已完成的测评项目生成报告内容
		</view>
		<!-- <image class="evaluation-center" :src="state.progress&&state.progress.questionStatus?'/static/evaluation/evaluation-question-finish.png':'/static/evaluation/evaluation-question.png'"
			mode="widthFix" style="margin-bottom: 16rpx;" @click="()=>go(1)"></image>
		<image class="evaluation-center" src="/static/evaluation/evaluation-test.png" mode="widthFix" @click="go"></image> -->
		<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal" />
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		navigateTo,
		showToast,
		getStorageSync,
		redirectTo
	} from "../../common/uniTool";
	import DropoutReminderVue from "../../components/DropoutReminder.vue";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		getFreeScreenProgress,
		resetFreeAddRec,
		handStopFreeScreenProgress
	} from "../../service/evaluation";
	import {
		wsUrl,
		reportUrl
	} from "../../common/global";
	import {
		useHelper
	} from "../../stores/helper";
	import ws from '@/utils/websocket.js'
	import {
		BleController
	} from '@/utils/bluType';
	const loginStore = useLoginStore()
	const helper = useHelper(); //设备仓库
	const finfish = ref(true)

	const evaList = ref([{
		img: '/static/evaluation/evaluation-test-1.png',
		name: '反应/不反应任务(Go/NoGo)',
		text: '被测技能：执行力、抑制力、反应时',
		time: '3分钟 20试次',
		type: 'signalStatus',
		value: 0
	}, {
		img: '/static/evaluation/evaluation-test-2.png',
		name: '工作记忆测评',
		text: '被测技能：瞬时记忆、注意力、更新',
		time: '2-3分钟',
		type: 'memoryStatus',
		value: 0
	}, {
		img: '/static/evaluation/evaluation-test-3.png',
		name: '数字划消测验',
		text: '被测技能：视觉分辨、注意广度、识别',
		time: '3-5分钟',
		type: 'cancellatStatus',
		value: 0
	}])
	const state = reactive({
		blu: {
			address: '',
		},
		progress: {
			cancellatStatus: 0,
			memoryStatus: 0,
			questionStatus: 0,
			signalStatus: 0,
		},
		webData: null,
		showTips: true,
	})
	onMounted(() => {

	})
	const change = (value) => {
		console.log(value);
		console.log('点击');
		state.showTips = value
	}
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(false);

		ownBluInit()
		//#endif
		getProgress()
	})
	const goReport = () => {
		if (state.progress.questionStatus || state.progress.memoryStatus || state.progress.cancellatStatus || state.progress.signalStatus) {
			handStopFreeScreenProgress({
				traineeId: loginStore.qryActiveTrainee,
				evaluatId: state.progress.evaluatId,
				round: state.progress.round
			}).then(res => {
				getProgress()
				navigateTo(
					`/pages/webview/index?url=${reportUrl}#/eegReport/${getStorageSync('ttoken')}/${state.progress.round}/${loginStore.qryActiveTrainee}/SMSCODE?evaluatId=${state.progress.evaluatId}`
				)
				console.log(res);
			})
		}
	}
	const getProgress = () => {
		getFreeScreenProgress({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.progress = res.data
			evaList.value.forEach(item => {
				item.value = res.data[item.type]
			})
			loginStore.evaluatId = res.data.evaluatId
			loginStore.round = res.data.round
		})
	}
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			const ret = JSON.parse(state)
			helper.bluData = ret
		})
	}


	const goDevice = () => {
		navigateTo('/pages/device/index')
	}
	const go = (type, finish) => {
		if (finish) {
			showToast('该测评已完成')
			return
		}
		if (!state.progress) {
			navigateTo('/pages/informationForm/index')
			return
		}
		if (type === 'questionStatus') {
			navigateTo(`/pages/scale/index?evaluatId=${state.progress.evaluatId}&round=${state.progress.round}`)
		} else if (type === 'signalStatus') {
			navigateTo('/pages/evaluation/introduce?type=0')
		} else if (type === 'memoryStatus') {
			navigateTo('/pages/evaluation/introduce?type=1')
		} else {
			navigateTo('/pages/evaluation/introduce?type=2')
		}
	}
</script>

<style lang="scss">
	.evaluation {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #F6F6F6;

		&-finish {
			width: 92%;
			height: 68rpx;
			border-radius: 39rpx;
			margin-top: 40rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			background: #EEEEEE;
			color: #999999;

			&-no {
				color: #FFFFFF;
				background: linear-gradient(90deg, #59A7FF 0%, #1F8CFD 100%);
				box-shadow: inset 0rpx 0 2rpx 0rpx rgba(255, 255, 255, 0.5);
			}
		}

		&-tips {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #FF0000;
			margin-top: 10rpx;
		}

		&-box {
			display: flex;
			flex-direction: column;
			width: 92%;

			&-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #111111;
			}

			&-content {
				background: #FFFFFF;
				border-radius: 16rpx;
				margin-top: 16rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 16rpx;

				&-img {
					width: 64rpx;
					height: 64rpx;
					margin-right: 10rpx;
				}

				&-left {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 1;

					&-top {
						display: flex;
						align-items: center;
						font-weight: bold;
						font-size: 24rpx;
						color: #111111;

						&-text {
							background: #FFA73D;
							border-radius: 4rpx;
							font-weight: bold;
							font-size: 16rpx;
							color: #FFFFFF;
							padding: 2rpx 6rpx;
							margin-left: 16rpx;
						}
					}

					&-center {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #666666;
						margin: 12rpx 0;
					}

					&-bottom {
						font-weight: 400;
						font-size: 16rpx;
						color: #207BFF;
					}
				}

				&-right {
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 24rpx;
					color: #FFFFFF;
					width: 136rpx;
					height: 48rpx;
					background: linear-gradient(90deg, #59A7FF 0%, #1F8CFD 100%);
					box-shadow: inset 0rpx 0 2rpx 0rpx rgba(255, 255, 255, 0.5);
					border-radius: 96rpx;

					&-finish {
						border: 1rpx solid #207BFF;
						color: #207BFF;
						background: #FFFFFF;
					}
				}
			}
		}

		&-test {
			position: relative;
			width: 100%;

			&-mouse {
				position: absolute;
				z-index: 999;
				top: 0;
				left: 0;
			}
		}

		&-bluBox {
			width: 92%;
			height: 120rpx;
			background: #FFFFFF;
			border-radius: 18rpx;
			display: flex;
			align-items: center;
			box-shadow: 0rpx 1rpx 4rpx 0rpx rgba(0, 0, 0, 0.1);
			margin-bottom: 18rpx;
			margin-top: 18rpx;
			padding: 28rpx 24rpx;

			&-btn {
				background: #F1EFFF;
				box-shadow: inset 0rpx 0 2rpx 0rpx rgba(255, 255, 255, 0.5);
				border-radius: 8rpx;
				border: 1rpx solid #6F5BFF;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #6F5BFF;
				padding: 8rpx 16rpx;
			}

			&-text {
				margin-left: 12rpx;
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				&-top {
					font-size: 22rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #111111;
				}

				&-bottom {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}
			}

			&-img {
				width: 60rpx;
			}
		}

		&-blu {
			background: #FFFFFF;
			border-radius: 18rpx;
			padding: 12rpx 12rpx 36rpx 36rpx;
			display: flex;
			flex-direction: column;

			&-content {
				width: 100%;
				background: #F8F8F7;
				border-radius: 9rpx;
				display: flex;
				height: 166rpx;
				padding: 22rpx 16rpx;
				flex-direction: column;

				&-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8rpx;

					&-left {
						display: flex;
						flex-direction: row;
						align-items: center;
						flex: 1;

						&-text {
							font-size: 22rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
							margin-left: 8rpx;
							margin-right: 20rpx;
						}

						&-icon {
							color: #59A7FF;
							font-size: 28rpx;
						}
					}

					&-right {
						background: linear-gradient(90deg, #59A7FF 0%, #1F8CFD 100%);
						box-shadow: inset 0rpx 0rpx 1rpx 0rpx rgba(255, 255, 255, 0.5);
						border-radius: 20rpx;
						font-size: 14rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #FFFFFF;
						padding: 8rpx 28rpx;
					}
				}

			}

			&-intro {
				font-size: 14rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				margin-top: 18rpx;
			}

			&-btn {
				width: 167rpx;
				height: 43rpx;
				background: linear-gradient(336deg, #812FD8 0%, #85EFFB 100%);
				border-radius: 23rpx;
				border: 2rpx solid #B4F5FF;
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				align-self: center;
				margin-top: 36rpx;
			}

			&-icon {
				display: flex;
				justify-content: flex-end;
				font-size: 30rpx;
				color: #C1C1C1;

			}

			&-title {
				font-size: 36rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #111111;
			}
		}

		&-top {
			width: 100%;
			flex-shrink: 0;
		}

		&-center {
			width: 92%;
			box-shadow: 0rpx 1rpx 4rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 20rpx;
		}

		&-btn {
			width: 400rpx;
			margin-top: 100rpx;
		}
	}
</style>