<template>
	<view class="helpRobert">
		<image src="/static/evaluation/screenbg.jpg" class='helpRobert-bg' />
		<image src="/static/blu-gif.gif" mode="widthFix" v-if="helper.bluData&&helper.bluData.signal===0"></image>
		<TipsVue :trueNum="state.trueNum" :frequency="state.frequency" :continuousTrueNum="state.continuousTrueNum" />
		<image v-show="showSig" class="helpRobert-sig" src="/static/evaluation/quickAieve-alarm.png" :style="{top:helper.bluData&&helper.bluData.signal===0?'210rpx':'150rpx'}" />
		<view class="helpRobert-aim center" v-if="state.timeShow">
			<image src="/static/evaluation/aim.png" class='helpRobert-aim-img' />
		</view>
		<view v-else class="helpRobert-content center">
			<image v-if="state.photo && state.photo.value == 'square'" :src="`/static/evaluation/team-${imgList[state.fruitIndex].img1}.png`" class='helpRobert-content-boss' />
			<image v-else :src='`/static/evaluation/team-${imgList[state.fruitIndex].img2}.png`' class='helpRobert-content-qianting' />
		</view>
		<view class="helpRobert-bottom">
			<image class='helpRobert-bottom-img' mode="widthFix" :src="state.isClicked == 'square' ? imgList[state.fruitIndex].btn1Click
				: imgList[state.fruitIndex].btn1" @click="click('square')" />
			<image class='helpRobert-bottom-img' mode=" widthFix" :src="state.isClicked == 'triangle' ? imgList[state.fruitIndex].btn2Click : imgList[state.fruitIndex].btn2"
				@click="click('triangle')" />
		</view>
		<view v-if="state.showPop" class="helpRobert-end">
			<view class="helpRobert-end-box">
				<view class="helpRobert-end-box-title">测评结束</view>
				<view class="helpRobert-end-box-text">测评已结束，点击回到主页</view>
				<view class="helpRobert-end-box-btn center" @click="go">回到主页</view>
			</view>
		</view>
		<view class="helpRobert-toast" v-if="state.showToastBox">
			<view class="helpRobert-toast-box">
				<image class="helpRobert-toast-box-bg" src="/static/evaluation/inhibitoryControl-pop.png" mode="widthFix"></image>
				<image class="helpRobert-toast-box-again" src="/static/evaluation/helpRobert-again.png" mode="widthFix" @click="agagin"></image>
				<image class="helpRobert-toast-box-btn" src="/static/evaluation/helpRobert-btn-true.png" mode="widthFix" @click="nextLevel"></image>
			</view>

		</view>
		<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal" />
		<VideoMask v-if="state.teaching" video='/static/video/sc01_v.mp4' @event="start" text='做好准备' :videoBtn="state.videoState" />
		<ToastVue v-if="state.showTrueToast" text='正确' icon='&#xe60a;' top='58%' color='#1FBF77' background='#FFFFFF' textColor='#111111' />
		<ToastVue v-if="state.showFalseToast" text='错误' icon='&#xe7fa;' top='58%' color='#FF4747' background='#FFFFFF' textColor='#111111' />
	</view>
</template>

<script setup>
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import DropoutReminderVue from "../../components/DropoutReminder.vue";
	import VideoMask from '../../components/VideoMask.vue';
	import TipsVue from '../../components/Tips.vue';
	import ToastVue from '../../components/Toast.vue';
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		navigateTo,
		showToast,
		redirectTo,
		navigateBack,
		setKeepScreenOn
	} from '../../common/uniTool';
	import {
		postFreeAddRec,
		postFreeAddRecTime
	} from '../../service/evaluation';
	import {
		useHelper
	} from "../../stores/helper";
	import {
		randomNum
	} from '../../common/method';
	import {
		wsUrl
	} from '../../common/global';
	import ws from '@/utils/websocket.js'
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		addRealEndTime,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	const loginStore = useLoginStore()
	const helper = useHelper(); //websocket仓库
	const socket = useSocketStore(); //websocket仓库
	const innerAudioContextRef = ref(null) //音频
	const changeContextRef = ref(null) //音频
	const qiantingContextRef = ref(null) //音频
	const bossContextRef = ref(null) //音频
	const timeRef = ref(0)
	const clickAudioRef = ref(null)
	const submitRef = ref(false)
	const signalRef = ref(null)
	const isClick = ref(false)
	const frequencyRef = ref(0) //正式游戏次数
	const photoArrRef = ref([{
			value: 'square',
			sig: true
		},
		{
			value: 'square',
			sig: true
		},
		{
			value: 'square',
			sig: true
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'square',
			sig: false
		},
		{
			value: 'triangle',
			sig: true
		},
		{
			value: 'triangle',
			sig: true
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		},
		{
			value: 'triangle',
			sig: false
		}
	])
	const photoIndexRef = ref(-1)
	const showSig = ref(false) //是否显示刺激
	const eceuId = ref('') //时间戳校准id
	const state = reactive({
		frequency: 20,
		timeShow: true, //过度点
		photo: {
			value: '',
			sig: null
		},
		showPop: false, //显示结束弹窗
		// showSig: false, //是否显示刺激
		finish: false, //游戏结束
		showTrueToast: false, //显示提示
		showFalseToast: false, //显示提示
		teaching: true, //教学提醒
		isClicked: '',
		videoState: {
			status: true,
			img1: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoFinishBtn.png',
			img2: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoBtn.png'
		}, //教学视频自定义按钮
		trueNum: 0, //做对次数
		continuousTrueNum: 0, //连续做对次数
		answer: [],
		isBreak: 0, //当前页面是否已经断开过
		showToastBox: false, //展示播放语音提醒
		fruitIndex: 0,
		markerStimulus: '', //刺激事件类型
		markerStime: null
	})
	const btnList = []
	const imgList = [{
			img1: '04',
			img2: '03',
			btn1: '/static/evaluation/btn-1-1.png',
			btn1Click: '/static/evaluation/btn-1-1-click.png',
			btn2: '/static/evaluation/btn-1-2.png',
			btn2Click: '/static/evaluation/btn-1-2-click.png',
			mp3L: '/static/audio/mp3-1-1.MP3',
			mp3R: '/static/audio/mp3-1-2.MP3',
		},
		{
			img1: '06',
			img2: '05',
			btn1: '/static/evaluation/btn-2-1.png',
			btn1Click: '/static/evaluation/btn-2-1-click.png',
			btn2: '/static/evaluation/btn-2-2.png',
			btn2Click: '/static/evaluation/btn-2-2-click.png',
			mp3L: '/static/audio/mp3-2-1.MP3',
			mp3R: '/static/audio/mp3-2-2.MP3',
		},
		{
			img1: '07',
			img2: '12',
			btn1: '/static/evaluation/btn-3-1.png',
			btn1Click: '/static/evaluation/btn-3-1-click.png',
			btn2: '/static/evaluation/btn-3-2.png',
			btn2Click: '/static/evaluation/btn-3-2-click.png',
			mp3L: '/static/audio/mp3-3-1.MP3',
			mp3R: '/static/audio/mp3-3-2.MP3',
		},
		{
			img1: '08',
			img2: '09',
			btn1: '/static/evaluation/btn-4-1.png',
			btn1Click: '/static/evaluation/btn-4-1-click.png',
			btn2: '/static/evaluation/btn-4-2.png',
			btn2Click: '/static/evaluation/btn-4-2-click.png',
			mp3L: '/static/audio/mp3-4-1.MP3',
			mp3R: '/static/audio/mp3-4-2.MP3',
		},
		{
			img1: '10',
			img2: '11',
			btn1: '/static/evaluation/btn-5-1.png',
			btn1Click: '/static/evaluation/btn-5-1-click.png',
			btn2: '/static/evaluation/btn-5-2.png',
			btn2Click: '/static/evaluation/btn-5-2-click.png',
			mp3L: '/static/audio/mp3-5-1.MP3',
			mp3R: '/static/audio/mp3-5-2.MP3',
		},
		{
			img1: '01',
			img2: '02',
			btn1: '/static/evaluation/quickAieve-submarine.png',
			btn1Click: '/static/evaluation/quickAieve-submarine-click.png',
			btn2: '/static/evaluation/quickAieve-neurons.png',
			btn2Click: '/static/evaluation/quickAieve-neurons-click.png',
			mp3L: '/static/audio/sc01_square.mp3',
			mp3R: '/static/audio/sc01_triangle.mp3',
		}
	]
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif

		ownBluInit()
	})
	onHide(() => {
		changeContextRef.value && changeContextRef.value.destroy()
		state.teaching = false
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(values => {
			const ret = JSON.parse(values)
			helper.bluData = ret
			if (socket.socketTask && !socket.socketTask.userClose && eceuId.value && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(values, eceuId.value, '', socket.socketTask)))
			}
			if (ret.signal && !state.finish) {
				state.isBreak++
			}
			if (state.isBreak > 0 && !ret.signal) {
				redirectTo('/pages/evaluation/inhibitoryControl')
			}
			// if (ret.signal && !state.finish) {
			// 	timeRef.value = null
			// 	signalRef.value && clearTimeout(signalRef.value)
			// 	signalRef.value = null
			// 	isClick.value = false
			// 	frequencyRef.value = 0
			// 	photoIndexRef.value = -1
			// 	state.isBreak++
			// 	state.frequency = 20,
			// 		state.timeShow = true, //过度点
			// 		state.photo = {
			// 			value: '',
			// 			sig: null
			// 		}
			// 	state.showPop = false //显示结束弹窗
			// 	showSig.value = false //是否显示刺激
			// 	state.finish = false //游戏结束
			// 	state.showTrueToast = false //显示提示
			// 	state.showFalseToast = false //显示提示
			// 	state.teaching = false //教学提醒
			// 	state.isClicked = ''

			// 	state.trueNum = 0 //做对次数
			// 	state.continuousTrueNum = 0 //连续做对次数
			// 	state.answer = []
			// 	state.showToastBox = false //展示播放语音提醒
			// }
			// if (state.isBreak > 0 && !ret.signal) {
			// 	state.teaching = true //教学提醒
			// 	state.isBreak = 0
			// }
		})
	}

	onMounted(() => {
		setKeepScreenOn()
		clickAudioRef.value = uni.createInnerAudioContext()
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}
		getUsePhoto()
		state.fruitIndex = randomNum(0, 5)

	})
	// const onSendMsg = (json) => {
	// 	const ret = JSON.parse(json)
	// 	let type = ret.type
	// 	console.log(ret, '1111111111111--------ret');
	// }
	//打乱图片
	const getUsePhoto = () => {
		const arr = [...photoArrRef.value]
		arr.sort(() => Math.random() > 0.5 ? -1 : 1)
		photoArrRef.value = arr
	}
	watch(() => state.photo, (photo) => {
		if (photo.value) {
			timeRef.value = (new Date).getTime()
		}
		if (photo && photo.sig) {
			getSigEle()
		}
	})
	watch([() => state.photo, () => state.frequency, () => state.fruitIndex], ([photo, frequency, fruitIndex]) => {
		let img = photo.value === 'square' ? imgList[state.fruitIndex].img1 : imgList[state.fruitIndex].img2
		if (photo.value && frequency < 10 && photo.sig) {
			state.markerStimulus = `S${img}111`
			state.markerStime = (new Date).getTime()
		} else if (photo.value && frequency < 10 && !photo.sig) {
			state.markerStimulus = `S${img}110`
			state.markerStime = (new Date).getTime()
		} else if (photo.value && photo.sig) {
			state.markerStimulus = `S${img}101`
			state.markerStime = (new Date).getTime()
		} else if (photo.value) {
			state.markerStimulus = `S${img}100`
			state.markerStime = (new Date).getTime()
		}
	})
	watch([() => state.frequency, () => state.timeShow], ([frequency, timeShow]) => {
		if (frequency === 10 && timeShow) {
			changeContextRef.value = uni.createInnerAudioContext()
			changeContextRef.value.sessionCategory = 'ambient'
			changeContextRef.value.src = '/static/audio/sc01_change.mp3'
			changeContextRef.value.play()
			isClick.value = false
			clearTime()
			state.showToastBox = true
		}
	})

	watch([() => state.frequency, () => state.photo], ([frequency, photo]) => {
		if (frequency < 10) {
			if (photo.value === 'triangle') {
				clickAudioRef.value.destroy()
				clickAudioRef.value = uni.createInnerAudioContext()
				clickAudioRef.value.src = imgList[state.fruitIndex].mp3L
				clickAudioRef.value.play()
			} else {
				clickAudioRef.value.destroy()
				clickAudioRef.value = uni.createInnerAudioContext()
				clickAudioRef.value.src = imgList[state.fruitIndex].mp3R
				clickAudioRef.value.play()
			}

		}
	})
	watch(() => state.timeShow, (timeShow) => {
		if (!timeShow) {
			if (innerAudioContextRef.value) {
				innerAudioContextRef.value.destroy()
			}
			isClick.value = true
			clearTime()
			getPhoto()
		} else {
			clickAudioRef.value.stop()
			state.isClicked = false
		}
	})
	watch(() => state.finish, (finish) => {
		if (finish) { //提交数据
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.sessionCategory = 'ambient'
			innerAudioContextRef.value.src = '/static/audio/sc_end.MP3'
			innerAudioContextRef.value.play()
			console.log(state.answer);
			postFreeAddRec({
				screenType: 1,
				signalData: state.answer,
				traineeId: loginStore.qryActiveTrainee,
				evaluatId: loginStore.evaluatId,
				round: loginStore.round
			}).then(res => {
				submitRef.value = true
				state.showPop = true
				showToast('成功提交');
			}).catch(() => {
				showToast('提交失败')
			})
		}
	})
	const agagin = () => {
		changeContextRef.value.stop()
		changeContextRef.value.play()
	}
	const clearTime = () => {
		if (signalRef.value) {
			clearTimeout(signalRef.value)
			signalRef.value = null
		}
	}
	//定时器正在动
	const getPhoto = () => {
		state.frequency--
		frequencyRef.value += 1
		photoIndexRef.value += 1
		state.photo = photoArrRef.value[photoIndexRef.value]
		signalRef.value = setTimeout(() => {
			clearTime()
			if (isClick.value) {
				isClick.value = false
				if (frequencyRef.value >= 0) {
					state.answer.push(
						`${photoArrRef.value[photoIndexRef.value].value === 'triangle' ? 1 : 2},${0},${photoArrRef.value[photoIndexRef.value].sig},${(new Date).getTime() - timeRef.value},${state.markerStimulus},${state.markerStime},null`
					)
				}
				if (frequencyRef.value === 20) {
					state.finish = true
					return
				}
				state.timeShow = true
				signalRef.value = setTimeout(() => {
					state.timeShow = false
				}, 1000);
			}
		}, 1500);
	}
	const nextLevel = () => {
		state.showToastBox = false
		changeContextRef.value.destroy()
		setTimeout(() => {
			state.timeShow = false
		}, 1000);
	}
	const getSigEle = () => {
		showSig.value = true
		let time = setTimeout(() => {
			showSig.value = false
			clearTimeout(time)
		}, 120);
	}
	onUnmounted(() => {
		//#ifdef APP-PLUS
		if (socket.socketTask) {
			addRealEndTime(eceuId.value)
			eceuId.value = ''
			socket.socketTask.closeSocket()
		}
		//#endif
	})
	const go = () => {
		navigateBack()
		// redirectTo('/pages/evaluation/index')
	}
	const goHelp = () => {
		if (innerAudioContextRef.value) {
			innerAudioContextRef.value.destroy()
		}
		setTimeout(() => {
			state.timeShow = false
		}, 1500);

	}
	const start = () => {
		postFreeAddRecTime({
			screenType: 1
		}).then(res => {
			eceuId.value = res.data.eceuId
			console.log('提交时间完成');
		})
		state.teaching = false
		goHelp()
	}
	const click = (value) => {
		if (!isClick.value) {
			return
		}
		clickAudioRef.value.destroy()
		clickAudioRef.value = uni.createInnerAudioContext()
		state.isClicked = value
		isClick.value = false
		clearTime()
		let Response = ''
		if (value === state.photo.value && !state.photo.sig) {
			if (value === 'square') {
				clickAudioRef.value.playbackRate = 2.0
				clickAudioRef.value.src = '/static/audio/sc01_rabbit.mp3'
				Response = 'R21'
			} else {
				clickAudioRef.value.src = '/static/audio/sc01_right.mp3'
				Response = 'R22'
			}
			clickAudioRef.value.play()
			state.trueNum += 1
			state.continuousTrueNum += 1
			state.showTrueToast = true
			setTimeout(() => {
				state.showTrueToast = false
			}, 500);
		} else if (value !== state.photo.value && !state.photo.sig) {
			clickAudioRef.value.src = '/static/audio/sc01_wrong.mp3'
			Response = 'R20'
			clickAudioRef.value.play()
			state.continuousTrueNum = 0
			state.showFalseToast = true
			setTimeout(() => {
				state.showFalseToast = false
			}, 500);
		}
		if (value && state.photo.sig) {
			clickAudioRef.value.src = '/static/audio/sc01_wrong.mp3'
			Response = 'R20'
			clickAudioRef.value.play()
			state.continuousTrueNum = 0
		}
		if (frequencyRef.value > 0) {
			state.answer.push(
				`${photoArrRef.value[photoIndexRef.value].value === 'triangle' ? 1 : 2},${value === 'triangle' ? 1 : 2},${photoArrRef.value[photoIndexRef.value].sig},${(new Date).getTime() - timeRef.value},${state.markerStimulus},${state.markerStime},R1,${(new Date).getTime()},${Response}`
			)
		}
		if (frequencyRef.value === 20) {
			state.finish = true
			return
		}
		if (photoIndexRef.value == photoArrRef.value.length - 1) {
			return
		}

		signalRef.value = setTimeout(() => {
			clearTime()
			state.timeShow = true
			signalRef.value = setTimeout(() => {
				state.timeShow = false
			}, 1000);
		}, 600)
	}
</script>

<style lang="scss">
	.helpRobert {
		width: 100%;
		height: 100vh;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 90rpx;
		justify-content: space-between;
		padding-top: 20rpx;

		&-sig {
			position: absolute;
			top: 50rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 130rpx;
			height: 130rpx;
		}

		&-toast {
			width: 100vw;
			height: 100vh;
			z-index: 999;
			position: absolute;
			top: 0;
			left: 0;

			&-box {
				position: absolute;
				width: 90%;
				top: 30%;
				left: 50%;
				transform: translateX(-50%);

				&-bg {
					width: 100%;

				}

				&-again {
					width: 144rpx;
					position: absolute;
					top: 24rpx;
					right: 24rpx;
				}

				&-btn {
					width: 500rpx;
					position: absolute;
					bottom: 66rpx;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}

		&-videoBtn {
			width: 590rpx;
		}

		&-videoFinishBtn {
			width: 590rpx;
		}

		&-mask {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 100%;
				position: absolute;
				bottom: 288rpx;
				left: 0;

				&-img {
					width: 100%;
				}

				&-icon {
					width: 40rpx;
					position: absolute;
					top: 27rpx;
					right: 25rpx;
				}
			}

			&-btn {
				position: absolute;
				left: 50%;
				bottom: 100rpx;
				transform: translateX(-50%);
				width: 590rpx;
			}
		}

		&-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			z-index: -1;
		}

		&-bottom {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 10rpx;

			&-img {
				width: 340rpx;
				height: 340rpx;
			}
		}


		&-aim {
			flex: 1;

			&-img {
				width: 242rpx;
				height: 242rpx;
			}
		}

		&-content {
			position: relative;
			width: 100%;
			flex: 1;

			&-qianting {
				width: 340rpx;
				height: 340rpx;
			}

			&-boss {
				width: 340rpx;
				height: 340rpx;
			}

			&-alarm {
				width: 120rpx;
				height: 120rpx;
				position: absolute;
				top: 10%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
			}
		}

		&-end {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 650rpx;
				height: 420rpx;
				background: #ffffff;
				border-radius: 24rpx;
				position: absolute;
				top: 30%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				align-items: center;
				justify-content: space-around;
				flex-direction: column;

				&-title {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
				}

				&-img {
					width: 100%;
				}

				&-text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}

				&-btn {
					width: 570rpx;
					height: 88rpx;
					background: #0485f4;
					border-radius: 16rpx;
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}
	}

	image {
		will-change: transform
	}
</style>