<template>
	<view class="trackRobert">
		<image src="../../static/evaluation/bg_sc02.jpg" class='trackRobert-bg' />
		<image src="../../static/blu-gif.gif" mode="widthFix" v-if="helper.bluData&&helper.bluData.signal===0"></image>
		<TipsVue :trueNum="state.trueNum" :continuousTrueNum="state.continuousTrueNum" />
		<view class="trackRobert-lattice">
			<image src="../../static/evaluation/mouse.png" class='trackRobert-lattice-robert' :style="{ 'left': Number(state.robertPosition.split('-')[0]) + 30 + 'rpx' , 'bottom' :
				Number(state.robertPosition.split('-')[1]) + 14 + 'rpx' }" />
			<template v-for="(item,index) in positionValue[state.positionIndex]['position']" :key="item.index">
				<view v-if="state.grassPosition!=-1&& state.grassPosition == index" class="trackRobert-lattice-box">
					<view class="trackRobert-lattice-box-grassPosition">
						<image :src="`../../static/evaluation/mouse-${state.mouseNum}.png`" class="trackRobert-lattice-box-grassPosition-mouse" mode="widthFix"></image>
						<image src="../../static/evaluation/hole.png" class="trackRobert-lattice-box-grassPosition-hole"></image>
					</view>
				</view>
				<image v-else-if="state.clickArr.indexOf(item.index) !== -1" :src="item.clickedImg" class='trackRobert-lattice-clicked' @click="clickStoned(item)" />
				<image v-else :src="item.img" class='trackRobert-lattice-clicked' @click="clickStoned(item)" />
			</template>
		</view>
		<image v-if="state.isReady && state.isReady != -1" class='trackRobert-ready' mode="widthFix" src='../../static/evaluation/ready_sc02.png' />
		<image v-if="state.memoryStart && state.memoryStart != -1 " class='trackRobert-ready' mode="widthFix" src='../../static/evaluation/sc02_recall.png' />

		<view v-if="state.showTrueToast" class="trackRobert-mask">
			<image class='trackRobert-img' mode="widthFix" src='../../static/evaluation/sc02_correct.png' />
		</view>
		<view v-if="state.showFalseToast" class="trackRobert-mask">
			<image class='trackRobert-img' mode="widthFix" src='../../static/evaluation/sc02_wrong.png' />
		</view>
		<VideoMask v-if="state.teaching" video='../../static/video/sc02_v.mp4' @event="start" text='做好准备' :videoBtn="state.videoState" />
		<view class="trackRobert-tips">
			【温馨提示】单击选中，再次点击取消
		</view>
		<image :src="`http://**************:9900/resources/quickAieve/${state.clickArr.length === state.numArr.length && state.clickArr.length > 0 ? 'affirm-finish-btn' : 'affirm-btn'}.png`"
			class='trackRobert-btn' @click="reset" />
	</view>
	<uni-popup ref="popup" type="center" :animation="false" :mask-click="false">
		<view class="trackRobert-end">
			<view class="trackRobert-end-title">测评结束</view>
			<view class="trackRobert-end-text">测评已结束，点击回到主页</view>
			<view class="trackRobert-end-btn center" @click="go">回到主页</view>
		</view>
	</uni-popup>
	<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal&&!submitRef" />
</template>

<script setup>
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import DropoutReminderVue from "../../components/DropoutReminder.vue";
	import {
		reactive,
		ref,
		watch,
		onMounted,
		onUnmounted
	} from "vue";
	import {
		showToast,
		navigateTo,
		getStorageSync,
		redirectTo,
		setKeepScreenOn,
		navigateBack
	} from "../../common/uniTool";
	import TipsVue from "../../components/Tips.vue";
	import Mask from "../../components/Mask.vue";
	import VideoMask from "../../components/VideoMask.vue";
	import {
		postFreeAddRec,
		postFreeAddRecTime
	} from "../../service/evaluation";
	import {
		randomNum
	} from "../../common/method";
	import positionValue from "../../utils/getPosttion";
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		reportUrl,
		wsUrl
	} from "../../common/global";
	import {
		useLoginStore
	} from "../../stores/login";
	import ws from '@/utils/websocket.js'
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		addRealEndTime,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	const loginStore = useLoginStore()
	const socket = useSocketStore()
	const helper = useHelper(); //设备仓库
	const lengthRef = ref(2)
	const isClickRef = ref(false) //是否可以点击
	const innerAudioContextRef = ref(null) //音频
	const falseAll = ref(0) //累计错误次数
	const trueAnswerRef = ref(0) //累计正确答案次数
	const clickAudioRef = ref(null)
	const submitRef = ref(false) //游戏结束提交
	const answer = ref([])
	const popup = ref(null) ///显示弹窗
	const eceuId = ref('') //时间戳校准id
	const state = reactive({
		trueNum: 0, //做对次数
		continuousTrueNum: 0, //连续做对次数
		showInfo: false, //前面提示
		isReady: -1, //准备显示
		robertPosition: '0-492', //罗伯特位置
		grassPosition: -1, //草丛位置
		memoryStart: -1, //开始回忆
		clickArr: [], //选中的数组
		numArr: [], //循环的数组
		showTrueToast: false, ////显示提示
		showFalseToast: false, //显示提示
		teaching: true, //教学提醒
		mouseNum: 1, //切换地鼠图片index
		videoState: {
			status: true,
			img1: 'http://**************:9900/resources/quickAieve/evaluation-videoFinishBtn.png',
			img2: 'http://**************:9900/resources/quickAieve/evaluation-videoBtn.png'
		}, //教学视频自定义按钮
		positionIndex: randomNum(0, positionValue.length - 1),
		gameNum: 0, //游戏轮次
		isBreak: 0, //当前页面是否已经断开过
	})
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif
		ownBluInit()

	})
	onHide(() => {
		state.teaching = false
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(values => {
			// console.log('数据接受中', values);
			const ret = JSON.parse(values)
			helper.bluData = ret
			if (socket.socketTask && !socket.socketTask.userClose && eceuId.value && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(values, eceuId.value, '', socket.socketTask)))
			}
			if (ret.signal && !submitRef.value) {
				state.isBreak++
			}
			if (state.isBreak > 0 && !ret.signal) {
				redirectTo('/pages/evaluation/numberBackwards')
			}
			// if (ret.signal && !submitRef.value) {
			// 	lengthRef.value = 2
			// 	isClickRef.value = false
			// 	falseAll.value = 0
			// 	trueAnswerRef.value = 0
			// 	answer.value = []
			// 	state.isBreak++
			// 	state.trueNum = 0
			// 	state.continuousTrueNum = 0
			// 	state.showInfo = false
			// 	state.isReady = -1
			// 	state.robertPosition = '0-492' //罗伯特位置
			// 	state.grassPosition = -1 //草丛位置
			// 	state.memoryStart = -1 //开始回忆
			// 	state.clickArr = [] //选中的数组
			// 	state.numArr = [] //循环的数组
			// 	state.showTrueToast = false ////显示提示
			// 	state.showFalseToast = false //显示提示
			// 	state.teaching = false //教学提醒
			// 	state.mouseNum = 1 //切换地鼠图片index
			// 	state.positionIndex = randomNum(0, positionValue.length - 1)
			// 	state.gameNum = 0 //游戏轮次
			// }
			// if (state.isBreak > 0 && !ret.signal) {
			// 	state.teaching = true //教学提醒
			// 	state.isBreak = 0
			// }
		})
	}

	const start = () => {
		postFreeAddRecTime({
			screenType: 2
		}).then(res => {
			eceuId.value = res.data.eceuId
			console.log('提交时间完成');
		})
		state.teaching = false
		readyGame()
	}
	onMounted(() => {
		setKeepScreenOn()
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}
		innerAudioContextRef.value = uni.createInnerAudioContext()
	})
	const readyGame = () => {
		state.isReady = true
		state.gameNum++
		setTimeout(() => {
			if (state.gameNum !== 1) {
				state.positionIndex = randomNum(0, positionValue.length - 1)
			}
			state.isReady = false
		}, 1000);
	}
	const clickStoned = (value) => {
		if (!isClickRef.value || !value.isClick) {
			return
		}
		const arr = [...state.clickArr]
		const index = arr.indexOf(value.index)
		if (index == -1) {
			arr.push(value.index)
		} else {
			arr.splice(index, 1)
		}
		if (arr.length > lengthRef.value) {
			showToast(`小地鼠只经过了${lengthRef.value}个位置哦~`)
			return
		}
		state.clickArr = arr
	}
	watch(() => state.isReady, (isReady) => {
		if (!isReady && isReady != -1) {
			setTimeout(() => {
				getPosition(lengthRef.value)
			}, 500);
		}
	})
	watch(() => state.memoryStart, (memoryStart) => {
		if (memoryStart && memoryStart != -1) {
			setTimeout(() => {
				state.memoryStart = false
				isClickRef.value = true
			}, 1000);
		}
	})
	const getNoRepeatEle = (n) => {
		const arr = [...positionValue[state.positionIndex]['num']]
		const result = [];
		let len = arr.length;
		for (let i = 0; i < n; i++) {
			const index = ~~(Math.random() * len) + i; // ~~双按位非 取整
			if (result.includes(arr[index])) {
				continue;
			}
			result.push(arr[index]);
			arr[index] = arr[i];
			len--;
		}
		return result;
	}

	const getPosition = (length) => {
		const arr = getNoRepeatEle(length)
		const numArr = []
		arr.forEach(item => {
			numArr.push(positionValue[state.positionIndex]['num'].indexOf(item) + 1)
		})
		state.numArr = numArr
		for (var i = 0; i < length; i++) {
			(function (j) {
				setTimeout(function timer() {
					const element = arr[j];
					state.robertPosition = '999-999'
					state.grassPosition = element
					const timer = setInterval(() => {
						if (state.mouseNum == 5) {
							state.mouseNum = 1
							clearInterval(timer)
						} else {
							state.mouseNum++
						}
					}, 200)
					if (j === length - 1) {
						setTimeout(() => {
							state.memoryStart = state
							state.grassPosition = -1

						}, 1000);
					}
				}, j * 1000);
			})(i)
		}
	}
	const scalarArrayEquals = (array1, array2) => {
		return array1.length == array2.length && array1.every(function (v, i) {
			return v === array2[i]
		});
	}

	//重置游戏
	const reset = () => {
		if (!isClickRef.value) {
			return
		}
		if (state.clickArr.length == 0) {
			showToast(`小地鼠经过了${state.numArr.length}个位置哦~`)
			return
		}
		if (state.clickArr.length != state.numArr.length) {
			showToast(`小地鼠经过了${state.numArr.length}个位置哦~`)
			return
		}
		answer.value.push(`${state.numArr.toString().replace(/,/g, "")},${state.clickArr.toString().replace(/,/g, "")}`)
		if (scalarArrayEquals(state.clickArr.reverse(), state.numArr)) {
			innerAudioContextRef.value.src = 'http://**************:9900/resources/quickAieve/sc02/sc02_right.mp3'
			innerAudioContextRef.value.play()
			state.showTrueToast = true
			trueAnswerRef.value += 1 //累计对的次数
			state.continuousTrueNum++
			state.trueNum++
			setTimeout(() => {
				state.showTrueToast = false
			}, 1000);
		} else {
			innerAudioContextRef.value.src = 'http://**************:9900/resources/quickAieve/sc02/sc02_wrong.mp3'
			innerAudioContextRef.value.play()
			state.showFalseToast = true
			falseAll.value += 1 //累计错的次数
			state.continuousTrueNum = 0
			setTimeout(() => {
				state.showFalseToast = false
			}, 1000);
		}

		if (falseAll.value == 2) { //用户累计错2次||数字倒背9位后累计错2次，显示本轮测评结束弹窗
			state.showFalseToast = false
			innerAudioContextRef.value.src = '/static/audio/sc_end.MP3'
			innerAudioContextRef.value.play()
			console.log(answer.value);
			postFreeAddRec({
				screenType: 2,
				memData: answer.value,
				traineeId: loginStore.qryActiveTrainee,
				evaluatId: loginStore.evaluatId,
				round: loginStore.round
			}).then(res => {
				if (res.code === '0000') {
					submitRef.value = true
					popup.value.open('center')
					showToast('成功提交');
				} else {
					showToast('提交失败')
				}
			})
			return
		}
		if (trueAnswerRef.value == 2 && lengthRef.value < 9) { //连续对两个数组个数加一
			lengthRef.value += 1
			trueAnswerRef.value = 0
			falseAll.value = 0
		}
		state.numArr = []
		state.clickArr = []
		isClickRef.value = false
		setTimeout(() => {
			readyGame()
		}, 1000);
	}
	onUnmounted(() => {
		//#ifdef APP-PLUS
		if (socket.socketTask) {
			addRealEndTime(eceuId.value)
			eceuId.value = ''
			socket.socketTask.closeSocket()
		}
		//#endif
	})
	const go = () => {
		navigateBack()
		// redirectTo('/pages/evaluation/index')
	}
</script>

<style lang="scss">
	.trackRobert {
		width: 100%;
		height: 100vh;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 40rpx;
		justify-content: space-between;
		padding-top: 20rpx;

		&-bg {
			position: absolute;
			height: 100%;
			top: 0;
			left: 0;
			width: 100%;
			z-index: -1;
		}

		&-lattice {
			width: 656rpx;
			height: 656rpx;
			position: relative;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
			margin-top: 100rpx;

			&-img {
				width: 656rpx;
			}

			&-robert {
				width: 89rpx;
				height: 119rpx;
				position: absolute;
				bottom: 492rpx;
				left: 0;
				top: 0rpx;
			}

			&-clicked {
				width: 150rpx;
				height: 88rpx;
				flex-shrink: 0;
				margin-bottom: 18rpx;
			}

			&-box {
				width: 150rpx;
				height: 88rpx;
				flex-shrink: 0;
				margin-bottom: 18rpx;
				position: relative;

				&-grassPosition {
					width: 150rpx;
					height: 88rpx;
					position: absolute;
					bottom: 0;
					left: 0;

					&-hole {
						width: 150rpx;
						height: 88rpx;
						flex-shrink: 0;
					}

					&-mouse {
						width: 150rpx;
						flex-shrink: 0;
						position: absolute;
						z-index: 2;
						bottom: 0rpx;
						left: 0;
					}
				}
			}
		}

		&-tips {
			font-size: 22rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 30rpx;
			margin-bottom: 10rpx;
		}

		&-btn {
			width: 590rpx;
			height: 112rpx;
		}

		&-mask {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 100%;
				position: absolute;
				bottom: 656rpx;
				left: 0;

				&-img {
					width: 100%;
				}

				&-icon {
					width: 40rpx;
					position: absolute;
					top: 27rpx;
					right: 25rpx;
				}
			}

			&-btn {
				position: absolute;
				left: 50%;
				bottom: 100rpx;
				transform: translateX(-50%);
				width: 590rpx;
			}
		}

		&-ready {
			width: 100%;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
		}

		&-img {
			width: 80%;
			position: absolute;
			top: 30%;
			transform: translateY(-50%);
			left: 50%;
			transform: translateX(-50%);
		}

		&-videoBtn {
			width: 590rpx;
		}

		&-videoFinishBtn {
			width: 590rpx;
		}

		&-end {
			width: 650rpx;
			height: 420rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			&-title {
				font-size: 48rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #111111;
			}

			&-img {
				width: 100%;
			}

			&-text {
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
			}

			&-btn {
				width: 570rpx;
				height: 88rpx;
				background: #0485f4;
				border-radius: 16rpx;
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #ffffff;
			}
		}
	}
</style>