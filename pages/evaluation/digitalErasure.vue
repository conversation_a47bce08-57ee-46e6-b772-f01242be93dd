<template>
	<view class="erasure" :class="state.showPop?'erasure-ovflow':''">
		<view class="erasure-top">
			<view class="erasure-top-title">
				题目：划掉数字“{{state.target}}”
			</view>
			<view class="erasure-top-box">
				<view class="erasure-top-box-left">
					<view class="erasure-top-box-left-item" :class="state.clickFW===item.value?'erasure-top-box-left-item-click':''" v-for="(item,index) in sizeList" @click="()=>change(item.value)"
						:key="item.value">
						{{item.text}}
					</view>
				</view>
				<view class="erasure-top-box-right">
					<view class="erasure-top-box-right-time center">
						<text class="iconfont">&#xe74f;</text>
						<u-count-down @change="onChange" ref="countDown" @finish="finish" :autoStart="false" :time="5 * 60  * 1000" format="mm:ss">
							<view class="erasure-top-box-right-time-text">
								{{state.timeData.minutes}}:{{state.timeData.seconds >=10?state.timeData.seconds :'0'+state.timeData.seconds  }}
							</view>
						</u-count-down>
					</view>
					<view class="erasure-top-box-right-btn center" @click="postAnswer">
						提交
					</view>
				</view>
			</view>
		</view>
		<view class="erasure-value" v-if="state.numList.length>0">
			<view v-for="(item,index) in state.numList"
				:class="[state.clickNum.indexOf(`${index},${item}`) !== -1?'erasure-value-item-click':'', state.clickFW==='sm'?'erasure-value-item-sm':state.clickFW==='me'?'erasure-value-item-me':'erasure-value-item-big']"
				@click="()=>clicked(index,item)" class="erasure-value-item" :key="index">
				{{item}}
			</view>
		</view>
	</view>
	<view v-if="state.showPop" class="erasure-end">
		<view class="erasure-end-box">
			<view class="erasure-end-box-title">测评结束</view>
			<view class="erasure-end-box-text">测评已结束，点击回到主页</view>
			<view class="erasure-end-box-btn center" @click="go">回到主页</view>
		</view>
	</view>
	<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal" />
	<VideoMask v-if="state.teaching" video='/static/video/sc03_v.mp4' @event="start" text='做好准备' :videoBtn="state.videoState" />
</template>

<script setup>
	import DropoutReminderVue from '../../components/DropoutReminder.vue';
	import VideoMask from '../../components/VideoMask.vue';
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		getCancetRandNum,
		postFreeAddRec,
		postFreeAddRecTime
	} from '../../service/evaluation';
	import {
		getFakeTime,
		addRealEndTime,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		showToast,
		getStorageSync,
		redirectTo,
		setKeepScreenOn,
		navigateBack
	} from '../../common/uniTool';
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		reportUrl,
		wsUrl
	} from '../../common/global';
	import ws from '@/utils/websocket.js'
	import {
		BleController
	} from '@/utils/bluType';
	const helper = useHelper(); //websocket仓库
	const socket = useSocketStore()
	const innerAudioContextRef = ref(null)
	const eceuId = ref('') //时间戳校准id
	const state = reactive({
		clickFW: 'sm',
		clickNum: [],
		timeData: {},
		showPop: false,
		teaching: true, //教学提醒
		numList: [], //随机数字
		target: '', //目标数字
		isBreak: 0, //当前页面是否已经断开过
		videoState: {
			status: true,
			img1: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoFinishBtn.png',
			img2: 'http://101.35.248.239:9900/resources/quickAieve/evaluation-videoBtn.png'
		}, //教学视频自定义按钮
	})
	const loginStore = useLoginStore()
	const countDown = ref(null)
	const sizeList = [{
		text: '小号字体',
		value: 'sm'
	}, {
		text: '中号字体',
		value: 'me'
	}, {
		text: '大号字体',
		value: 'big'
	}]
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif

		ownBluInit()
		if (state.isBreak > 0) {
			countDown.value.start();
			state.isBreak = 0
		}
	})
	onHide(() => {
		countDown.value.pause();
	})
	onMounted(() => {
		setKeepScreenOn()
		getCancetRandNum({
			traineeId: loginStore.qryActiveTrainee,
			evaluatId: loginStore.evaluatId,
			round: loginStore.round
		}).then(res => {
			state.numList = res.data.randList
			state.target = res.data.target
		})
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}
		innerAudioContextRef.value = uni.createInnerAudioContext()
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(values => {
			// console.log('数据接受中', values);
			const ret = JSON.parse(values)
			helper.bluData = ret
			if (ret.signal && !state.showPop) {
				state.isBreak++
			}
			if (socket.socketTask && !socket.socketTask.userClose && eceuId.value && socket.socketTask.ws.readyState === 1) {
				// console.log('发送数据');
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(values, eceuId.value, '', socket.socketTask)))
			}
		})
	}

	const onChange = (e) => {
		state.timeData = e
	}

	const postAnswer = () => {
		postFreeAddRec({
			target: state.target,
			cancetData: state.clickNum,
			screenType: 3,
			traineeId: loginStore.qryActiveTrainee,
			evaluatId: loginStore.evaluatId,
			round: loginStore.round
		}).then(res => {
			innerAudioContextRef.value.src = '/static/audio/sc_end.MP3'
			innerAudioContextRef.value.play()
			state.showPop = true
		}).catch(err => {
			showToast(err.desc)
		})
	}
	const start = () => {
		postFreeAddRecTime({
			screenType: 3
		}).then(res => {
			eceuId.value = res.data.eceuId
			console.log('提交时间完成');
		})
		state.teaching = false
		countDown.value.start()
	}
	const clicked = (index, item) => {
		const arr = [...state.clickNum]
		const num = arr.indexOf(`${index},${item}`)
		if (num == -1) {
			arr.push(`${index},${item}`)
		} else {
			arr.splice(num, 1)
		}
		state.clickNum = arr
	}
	const change = (value) => {
		state.clickFW = value
	}
	const finish = () => {
		postAnswer()
	}
	onUnmounted(() => {
		//#ifdef APP-PLUS
		if (socket.socketTask) {
			addRealEndTime(eceuId.value)
			eceuId.value = ''
			socket.socketTask.closeSocket()
		}
		//#endif
	})
	const go = () => {
		navigateBack()
		// redirectTo('/pages/evaluation/index')
		// let tToken = getStorageSync('ttoken');
		// redirectTo(`/pages/webview/index?url=${reportUrl}#/eegReport/${tToken}/0/${loginStore.qryActiveTrainee}/SMSCODE`)
	}
</script>

<style lang="scss">
	.erasure {
		width: 100vw;
		flex: 1;
		background: #F6F6F6;

		&-ovflow {
			overflow: hidden;
		}

		&-end {
			width: 100%;
			height: 100%;
			position: fixed;
			left: 0;
			top: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 2;

			&-box {
				width: 650rpx;
				height: 420rpx;
				background: #ffffff;
				border-radius: 24rpx;
				position: absolute;
				top: 30%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				align-items: center;
				justify-content: space-around;
				flex-direction: column;

				&-title {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
				}

				&-img {
					width: 100%;
				}

				&-text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}

				&-btn {
					width: 570rpx;
					height: 88rpx;
					background: #0485f4;
					border-radius: 16rpx;
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}

		&-value {
			width: 100%;
			margin-top: 16rpx;
			padding: 24rpx;
			background: #FFFFFF;
			display: flex;
			flex-wrap: wrap;

			&-item {
				width: 5%;
				height: 60rpx;
				text-align: center;

				&-sm {
					width: 5%;
					height: 60rpx;
					font-size: 48rpx;
				}

				&-me {
					width: 6.6%;
					height: 76rpx;
					font-size: 56rpx;
				}

				&-big {
					width: 10%;
					height: 82rpx;
					font-size: 64rpx;
				}

				&-click {
					text-decoration: line-through;
				}
			}
		}

		&-top {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 36rpx 24rpx 16rpx 24rpx;
			background: #FFFFFF;
			position: sticky;
			top: 0;

			&-title {
				font-size: 32rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: bold;
				color: #111111;
				margin-bottom: 24rpx;
			}

			&-box {
				width: 100%;
				display: flex;
				justify-content: space-between;

				&-left {
					display: flex;
					align-items: center;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #111111;

					&-item {
						border: 1rpx solid #999999;
						padding: 8rpx 16rpx;

						&-click {
							font-size: 20rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: bold;
							color: #287FFF;
						}
					}

					&-item:nth-child(2) {
						border-right: none;
						border-left: none;
					}
				}

				&-right {
					display: flex;
					align-items: center;
					flex: 1;
					justify-content: flex-end;

					&-num {
						text-align: right;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #111111;
					}

					&-time {
						font-size: 20rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #111111;

						&-text {
							font-size: 20rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #111111;
							width: 60rpx;
							margin-left: 8rpx;
						}
					}

					&-btn {
						width: 88rpx;
						height: 36rpx;
						background: #287FFF;
						border-radius: 4rpx;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #FFFFFF;
						margin-left: 24rpx;
					}
				}
			}
		}
	}
</style>