<template>
	<view class='inhibitionControl'>
		<image src="../../static/quick/quick-bg-game.png" class='inhibitionControl-bg' />
		<view class='inhibitionControl-tips'>
			<view class='inhibitionControl-tips-left'>
				<image class="inhibitionControl-tips-left-img" src="../../static/quick/quick-true.png" />
				<view class='inhibitionControl-tips-left-num'>{{state.trueNum}}</view>
			</view>
			<view class='inhibitionControl-tips-right'>
				<image class="inhibitionControl-tips-right-img" src="../../static/quick/quick-alarm.png" />
				<view class='inhibitionControl-tips-right-value'>
					<view class='inhibitionControl-tips-right-value-num' :style="{width:state.fiveLimit+'px'}"></view>
				</view>
			</view>
		</view>
		<PositionBox v-if="state.picArr" wid='666' hei='666' :length="state.picArr.numPics.length" :imgList="state.picArr.numPics"></PositionBox>
		<view v-else :style="{ width: '666rpx', height: '666rpx' }"></view>
		<view class='inhibitionControl-question'>
			<view class='inhibitionControl-question-icon  iconfont'>&#xe643;</view><text class='inhibitionControl-question-text'>请问上面出现了几个气球?</text>
		</view>
		<view class='inhibitionControl-btn'>
			<view v-for="item in state.btnArr" class="inhibitionControl-btn-item center" :class="[state.click == item.value ? 'inhibitionControl-btn-item-click' : '']"
				@click="() => isClick ? clicked(item.value) : noClick()" :key="item.value">
				{{item.value}}
				<image v-if="state.showFace != -1 && state.click == item.value &&state.showFace == 1 " class="inhibitionControl-btn-item-img" />
				<image v-else class="inhibitionControl-btn-item-img" />
			</view>
		</view>
		<Countdown v-if="state.timeShow&&!state.teaching" count="3" @show="show" speed="600" :isStart="!state.teaching" />
		<VideoMask v-if="state.teaching" video='http://**************:9900/num/helpnum.mp4' @event="start" text="开始测评" />
	</view>
</template>

<script setup>
	import {
		reactive,
		ref
	} from "vue";
	import {
		showToast
	} from "../../common/uniTool";
	import Countdown from "../../components/CountDown.vue";
	import PositionBox from "../../components/PositionBox.vue";
	import VideoMask from "../../components/VideoMask.vue";
	const innerAudioContextRef = ref(null) //音频
	const limitTimeRef = ref(null) //90S限制时间
	const fiveLimitRef = ref(null) //5s倒计时
	const roundRef = ref(1) //轮次
	const startTime = ref(0)
	const finishRef = ref(false) //是否完成
	const state = reactive({
		trueNum: 0,
		fiveLimit: '',
		picArr: null, //获取的图片数组
		btnArr: [{
				value: 1
			},
			{
				value: 2
			},
			{
				value: 3
			},
			{
				value: 4
			}
		],
		click: 0, //点击答案
		showFace: -1, //展示对错反馈
		isClick: true, //是否点击
		trueNum: 0,
		limitOut: false, //超出时间游戏结束
		showPop: false, //显示弹窗
		timeShow: true, //倒计时弹窗
		teaching: true, //视频弹窗
	})
	const show = (value) => {
		state.timeShow = value
	}
	const start = () => {
		state.teaching = false
	}
	const clicked = (value) => {
		console.log(value);
		// if (value == state.picArr.numPics.length) {
		//     setTrueNum(v => v + 1)
		//     setShowFace(1)
		// } else {
		//     setShowFace(2)
		// }
		// setIsClick(false)
		// setClick(value)
		// dispatch({
		//     type: 'quick/addValue',
		//     payload: {
		//         value: `${roundRef.current - 1},${(new Date).getTime() - startTime.current},${value}`
		//     }
		// })
		// clearFiveTime()
		// setTimeout(() => {
		//     if (!finishRef.current) {
		//         setFiveLimit(10)
		//         setShowFace(-1)
		//         fiveLimitTime()
		//     }
		// }, 1000);

	}
	const noClick = () => {
		showToast('你已经选择啦~')
	}
</script>

<style lang="scss">
	.inhibitionControl {
		width: 100%;
		height: 100%;
		position: relative;

		&-bg {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: -999;
		}

		&-tips {
			width: 100%;
			padding: 0 42rpx;
			display: flex;
			padding-top: 64rpx;
			justify-content: space-between;
			margin-bottom: 60rpx;

			&-left {
				display: flex;
				align-items: center;
				height: 88rpx;
				width: 214rpx;
				position: relative;

				&-img {
					width: 92rpx;
					height: 88rpx;
					position: absolute;
					top: 0;
					left: 0;
					z-index: 1;
				}

				&-num {
					position: absolute;
					top: 16rpx;
					left: 24rpx;
					width: 214rpx;
					height: 60rpx;
					background: rgba(85, 129, 152, 0.5);
					box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(255, 255, 255, 0.5),
						inset 0rpx 1rpx 11rpx 0rpx rgba(102, 157, 185, 1);
					border-radius: 40rpx;
					text-align: center;
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
					text-shadow: 0rpx 2rpx 4rpx #2a6a8a;
				}
			}

			&-right {
				display: flex;
				align-items: center;
				position: relative;
				height: 88rpx;
				width: 415rpx;

				&-img {
					width: 88rpx;
					height: 88rpx;
					position: absolute;
					right: 300rpx;
					z-index: 1;
					top: 0;
				}

				&-value {
					position: absolute;
					right: 0rpx;
					top: 20rpx;
					width: 370rpx;
					height: 60rpx;
					background: rgba(85, 129, 152, 0.5);
					box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(255, 255, 255, 0.5),
						inset 0rpx 1rpx 11rpx 0rpx rgba(102, 157, 185, 1);
					border-radius: 40rpx;

					&-num {
						margin-top: 6rpx;
						width: 0;
						height: 48rpx;
						background: linear-gradient(180deg, #fff0ac 0%, #fdb41e 100%);
						border-radius: 40rpx 100rpx 100rpx 40rpx;
					}
				}
			}
		}

		&-question {
			margin: 0 auto;
			margin-top: 40rpx;
			width: 668rpx;
			height: 90rpx;
			background: linear-gradient(270deg,
					rgba(17, 17, 17, 0) 0%,
					rgba(84, 179, 218, 0.6) 50%,
					rgba(17, 17, 17, 0) 100%);
			display: flex;
			align-items: center;
			justify-content: center;

			&-text {
				font-size: 40rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
			}

			&-icon {
				font-size: 46rpx;
				margin-right: 10rpx;
				margin-top: 2rpx;
				color: #adb3b8;
			}
		}

		&-btn {
			width: 100%;
			padding: 0 45rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			&-item {
				width: 310rpx;
				height: 140rpx;
				margin-top: 40rpx;
				position: relative;
				background: linear-gradient(180deg, #99eafb 0%, #58c6fd 98%);
				box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(27, 131, 204, 1),
					inset 0rpx -12rpx 8rpx 0rpx rgba(32, 150, 218, 1),
					inset 0rpx 4rpx 7rpx 0rpx rgba(255, 255, 255, 0.95);
				border-radius: 16rpx;
				font-size: 90rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #1f5a9a;
				line-height: 90rpx;
				text-shadow: 0rpx 2rpx 4rpx rgba(255, 255, 255, 0.5), 0rpx 1rpx 3rpx #1f5a9a;

				&-img {
					position: absolute;
					z-index: 999;
					width: 140rpx;
					height: 140rpx;
					top: 0;
					left: 50%;
					transform: translateX(-50%);
				}

				&-click {
					width: 310rpx;
					height: 140rpx;
					background: linear-gradient(180deg, #fff1bb 0%, #fdb51f 100%);
					box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(239, 175, 83, 1);
					border-radius: 16rpx;
					font-size: 90rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #a9450b;
					line-height: 90rpx;
					text-shadow: 0rpx 2rpx 4rpx rgba(255, 255, 255, 0.5), 0rpx 1rpx 3rpx #a9450b;
				}
			}

		}
	}
</style>
