<template>
	<view class='introduce'>
		<image src="/static/evaluation/bg.jpg" class='introduce-bg' />
		<image :src="getSrc" mode="heightFix" class='introduce-top' />
		<view class='introduce-purpose'>
			<view class='introduce-purpose-title'>测评目的</view>
			<view class='introduce-purpose-text'>{{state.purpose[props.type]}}</view>
		</view>
		<view class='introduce-purpose'>
			<view class='introduce-purpose-title'>测评原理</view>
			<view class='introduce-purpose-text'>{{state.principle[props.type]}}</view>
		</view>
		<DropoutReminderVue v-if="helper.bluData&&helper.bluData.signal" />
		<view v-if="props.type == 0" class='introduce-purpose-text'>本测评结果反映了一个人根据任务的变换，迅速自控，调节专注对象的能力。</view>
		<image src="/static/evaluation/introduce-btn.png" mode="widthFix" @click="next" class='introduce-btn' />
		<view class="introduce-end" v-if="state.showTost">
			<view class="introduce-end-box">
				<view class="introduce-end-box-title">提示</view>
				<view class="introduce-end-box-text">请确保佩戴好脑电设备，在测评过程中，会实时监测分析脑电数据。</view>
				<view class="introduce-end-box-btn center" @click="go">我已知晓</view>
			</view>
		</view>
		<view class="introduce-gif" v-show="props.type==='0'">
			<view class="introduce-gif-box">
				<image src="/static/evaluation/eva-intro-left.png" style="width: 100%;" mode="widthFix" />
				<text class="introduce-gif-box-text">手指悬浮 <br /> 抬离屏幕1厘米左右</text>
			</view>
			<view class="introduce-gif-box">
				<image src="/static/evaluation/eva-intro-right.gif" style="width: 100%;" mode="widthFix" />
				<text class="introduce-gif-box-text">请在第一时间 <br /> 又快又准做出反应</text>
			</view>

		</view>
	</view>
</template>

<script setup>
	import {
		getLocationParams
	} from '../../common/method';
	import DropoutReminderVue from "../../components/DropoutReminder.vue";
	import {
		computed,
		onMounted,
		reactive,
		watch
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app'

	import {
		navigateTo,
		redirectTo,
		setNavigationBarTitle
	} from '../../common/uniTool';
	import {
		useHelper
	} from "../../stores/helper";
	import {
		BleController
	} from '@/utils/bluType';
	const helper = useHelper(); //设备仓库
	const props = defineProps(['type'])
	const state = reactive({
		principle: [
			'当前测评任务是根据信号停止范式（stop-signal paradigm）设计。本范式可以用来测试，当受测者对刺激行为反馈时，突然抑制此前所持续反应的能力。可反映个体在根据任务的变换，迅速自控，调节专注对象的能力。',
			'当前测评任务是根据数字倒背范式（backward digit span）设计。本范式广受国际学术界和教育界认可，被包括韦氏智力测验（WIS）在内的许多经典测验收录使用。',
			'在数字划消的过程中，需要限时5分钟内，记录正确、错划、漏划的数量，并以此计算出能力指数。孩子要想快速的找到自己想要的数字，就需要保持高度的注意力稳定。因为一旦走神，那孩子的视觉关注点就可能在眼花缭乱的数字中迷失，忘记了刚才关注的位置，也就大大的增加了自己的时间成本和错误率。'
		],
		purpose: [
			'针对受测者的自我控制能力进行评估。在日常中，每个人的自我控制能力，可表现为排除无关事情的干扰，保持对特定事情和目标的专注。',
			'针对受测孩子认知能力中的工作记忆-将获取到的信息暂时保存并进行操作的能力。短时记忆能力较差的青少儿表现为学子能力不足、无法保持较好专注力等。',
			'数字划消中的数字设计的都是一些相似或相近的数字串，孩子要在这眼花缭乱的数字中迅速的找到自己需要找的数字，测量的是孩子注意力的指向性和稳定性，以及视觉广度、观察的敏锐度、耐心、细心等方面的能力。'
		],
		type: '',
		showTost: false
	})

	const next = () => {
		if (props.type == 0) {
			state.showTost = true
		} else if (props.type == 1) {
			redirectTo('/pages/evaluation/numberBackwards')
		} else if (props.type == 2) {
			redirectTo('/pages/evaluation/digitalErasure')
		}
	}
	const go = () => {
		redirectTo('/pages/evaluation/iControlTeaching')
	}
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif

		ownBluInit()
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			const ret = JSON.parse(state)
			helper.bluData = ret
		})
	}

	onMounted(() => {
		state.type = props.type
	})
	const getSrc = computed(() => {
		return props.type == 0 ? '/static/evaluation/introduce-top.png' : props.type == 1 ? '/static/evaluation/quickTop2.png' : '/static/evaluation/quickTop3.png'
	})
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		position: relative;
		padding: 0 40rpx;

		&-gif {
			position: absolute;
			bottom: 218rpx;
			width: 100%;
			display: flex;
			height: 350rpx;
			justify-content: center;
			left: 0;

			&-box {
				width: 40%;
				display: flex;
				flex-direction: column;
				align-items: center;
				position: relative;

				&-text {
					position: absolute;
					font-size: 14rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					bottom: 18rpx;
					text-align: center;
				}
			}
		}

		&-end {
			width: 100%;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			background: rgba(17, 17, 17, 0.7);
			z-index: 999;

			&-box {
				width: 650rpx;
				height: 420rpx;
				background: #ffffff;
				border-radius: 24rpx;
				position: absolute;
				top: 30%;
				transform: translateY(-50%);
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				align-items: center;
				justify-content: space-around;
				flex-direction: column;

				&-title {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
				}

				&-img {
					width: 100%;
				}

				&-text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					width: 80%;
				}

				&-btn {
					width: 570rpx;
					height: 88rpx;
					background: #0485f4;
					border-radius: 16rpx;
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}

		&-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			z-index: -1;
			height: 100%;
		}

		&-top {
			height: 32rpx;
			margin-top: 28rpx;
			z-index: 999;
		}

		&-purpose {
			margin-top: 40rpx;
			display: flex;
			flex-direction: column;

			&-title {
				font-size: 25rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #ffffff;
			}

			&-text {
				font-size: 21rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 44rpx;
				margin-top: 20rpx;
			}
		}

		&-btn {
			width: 590rpx;
			position: absolute;
			bottom: 68rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}
</style>