const ivaData = {
	'0': {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1"
		],
		"signalList": [
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A"
		],
		"modTypelList": [
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			""
		]
	},
	"1": {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2"
		],
		"signalList": [
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V"
		],
		"modTypelList": [
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R"
		]
	},
	"2": {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2"
		],
		"signalList": [
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V"
		],
		"modTypelList": [
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			""
		]
	},
	"3": {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1"
		],
		"signalList": [
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A"
		],
		"modTypelList": [
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			""
		]
	}
}

const ivaMini = {
	'0': {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1"
		],
		"signalList": [
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A",
			"A"
		],
		"modTypelList": [
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			""
		]
	},
	'1': {
		"numList": [
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"1",
			"1",
			"1",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2",
			"2",
			"2",
			"2",
			"1",
			"2"
		],
		"signalList": [
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"V",
			"V",
			"V",
			"V",
			"A",
			"A",
			"V",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"V",
			"A",
			"A",
			"V",
			"V",
			"A",
			"V",
			"A",
			"A",
			"A",
			"V",
			"A",
			"V",
			"V",
			"V"
		],
		"modTypelList": [
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"F",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R",
			"R"
		]
	}
}

const getIVAData = (type, level) => {
	if (type === 'mini') {
		return ivaMini[level]
	} else {
		return ivaData[level]
	}
}

export default getIVAData