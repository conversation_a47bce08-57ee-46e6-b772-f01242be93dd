<template>
	<view class="introduce">
		<view class="introduce-title">
			{{props.type==1?'视听整合持续测试':'视听整合持续性操作测验'}}
		</view>
		<view class="introduce-text">
			{{props.type==1?'IVA-CPT（MINI版）':'（IVA-CPT）2023'}}
		</view>
		<view class="introduce-box center">
			<view class="introduce-box-other center" v-if="props.type==2">
				<view class="introduce-box-other-other1">

				</view>
			</view>
		</view>
		<view class="introduce-tips" :style="{width:props.type==2?'98%':'',fontSize:'29rpx',marginBottom:'40rpx'}">
			{{props.type==1?'我们现在进行测试前的规则讲解':'我们现在进行测验前的练习，注意看、听和点击屏幕'}}，<br />
			{{props.type==1?'注意看、听和点击屏幕的操作':'测试过程中，可从屏幕侧边滑动，中止测试'}}。
		</view>
		<view class="introduce-btn center" @click="go" v-if="props.type==1">
			开始讲解
		</view>
		<view class="introduce-btntwo" v-else>
			<view class="introduce-btntwo-value center" style="color:#287FFF ;" @click="open">
				佩戴BCI
			</view>
			<view class="introduce-btntwo-value center" style="color:#F4A300 ;" @click="go">
				未戴BCI
			</view>
		</view>
	</view>
	<uni-popup ref="popup" type="center" :is-mask-click="true">
		<PopBoxVue :text="{value:''}" title="当前设备未连接" :btn="{center:'前往设备管理'}" @click="goDevice"></PopBoxVue>
	</uni-popup>
</template>

<script setup>
	import PopBoxVue from '../../components/PopBox.vue';
	const props = defineProps(['type'])
	import {
		onMounted,
		onUnmounted,
		ref
	} from 'vue';
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		navigateTo
	} from '../../common/uniTool';
	import {
		useSocketStore
	} from '@/stores/socket';
	import {
		useHelper
	} from '@/stores/helper';
	const helper = useHelper(); //设备仓库
	const socket = useSocketStore(); //websocket仓库
	const popup = ref(null)
	const innerAudioRef = ref(null)
	onMounted(() => {
		innerAudioRef.value = uni.createInnerAudioContext()
		if (props.type == 1) {
			innerAudioRef.value.src = '/static/audio/ivacpt/iva-introduce.MP3'
		} else {
			innerAudioRef.value.src = '/static/audio/ivacpt/iva-introduce-sta.MP3'
		}
		innerAudioRef.value.play()
	})
	onHide(() => {
		popup.value.close()
		if (innerAudioRef.value) {
			innerAudioRef.value.destroy()
			innerAudioRef.value = null
		}
	})
	onUnmounted(() => {
		if (innerAudioRef.value) {
			innerAudioRef.value.destroy()
			innerAudioRef.value = null
		}
	})
	onShow(() => {
		// #ifdef APP-PLUS
		if (socket.socketTask) {
			console.log('断开连接');
			socket.socketTask.closeSocket()
		}
		// #endif
	})
	const open = () => {
		if (!helper.address) {
			popup.value.open()
		} else {
			navigateTo('/pages/ivacpt/standard')
		}

	}
	const go = () => {
		if (props.type == 1) {
			navigateTo('/pages/ivacpt/index')
		} else {
			navigateTo('/pages/ivacpt/standard')
		}
	}
	const goDevice = () => {
		navigateTo('/pages/device/index')
	}
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		background: #333333;
		display: flex;
		flex-direction: column;
		align-items: center;

		&-btntwo {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-around;

			&-value {
				width: 330rpx;
				height: 116rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				font-size: 48rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
			}
		}

		&-title {
			font-size: 48rpx;
			font-family: SourceHanSansCN-Bold, SourceHanSansCN;
			font-weight: bold;
			color: #FFFFFF;
			margin-top: 90rpx;
			margin-bottom: 32rpx;
		}

		&-text {
			font-size: 40rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #FFFFFF;
		}

		&-box {
			width: 364rpx;
			height: 464rpx;
			border-radius: 32rpx;
			border: 4rpx solid #FFDF9E;
			margin-top: 90rpx;
			margin-bottom: 55rpx;

			&-other {
				width: 334rpx;
				height: 434rpx;
				border: 4rpx solid #FFDF9E;
				border-radius: 16rpx;

				&-other1 {
					width: 300rpx;
					height: 400rpx;
					border: 4rpx solid #FFDF9E;
					border-radius: 10rpx;
				}
			}
		}

		&-tips {
			font-size: 32rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #FFAB01;
			line-height: 48rpx;
			width: 480rpx;
			text-align: center;
			margin-bottom: 90rpx;
		}

		&-btn {
			width: 480rpx;
			height: 116rpx;
			background: #FFFFFF;
			border-radius: 108rpx;
			font-size: 48rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #111111;
		}
	}
</style>