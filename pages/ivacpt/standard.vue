<template>
	<clickEffect :isClick="state.isClick"></clickEffect>
	<view class="iva" @click="click">
		<StepsFour :options="state.stepList" :active="props.type==1?'4':'1'"></StepsFour>
		<view class="iva-content">
			<view class="iva-content-box" v-if="state.active===1">
				<view class="iva-content-box-title">
					<view class="iva-content-box-title-icon center iconfont">&#xe6b3;</view>
					<text class="iva-content-box-title-text"> {{rules[state.teach].text}}</text>
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}} <text v-if="state.teach>=2&&state.teach<5">1</text>
						</view>
					</view>
				</view>
				<text class="iva-content-box-tips">
					{{rules[state.teach].rule}}
				</text>
				<view class="iva-content-box-btn center" v-if="state.teach>=4" @click.stop="next">
					{{props.type==1?'恢复性测试':'10次热身操作'}}
				</view>
			</view>
			<view v-if="state.active===2" class="iva-content-box">
				<view class="iva-content-box-title">
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
			</view>
			<view v-if="state.active===3" class="iva-content-box">
				<view class="iva-content-box-title">
					<view class="iva-content-box-title-icon center iconfont">&#xe6b3;</view>
					<text class="iva-content-box-title-text center"> {{rules[state.teach].text}}</text>
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}} <text v-if="state.teach>=2&&state.teach<5">1</text>
						</view>
					</view>
				</view>
				<text class="iva-content-box-tips">
					{{rules[state.teach].rule}}
				</text>
				<view class="iva-content-box-btn center" v-if="state.teach>=9" @click.stop="next">
					{{props.type==1?'恢复性测试':'10次热身操作'}}
				</view>
			</view>
		</view>
	</view>
	<uni-popup ref="popup" type="center" :is-mask-click="false">
		<PopBoxVue title="测评结束" :text="{value:''}" :btn="{center:'查看报告'}" @click="go"></PopBoxVue>
	</uni-popup>
	<IvaDropoutReminderVue :eceuId="eceuId" :isStartCollect="isStartCollect"></IvaDropoutReminderVue>
</template>

<script setup>
	import StepsFour from '@/components/StepsFour.vue';
	import IvaDropoutReminderVue from '../../components/IvaDropoutReminder.vue';
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		getStorageSync,
		navigateTo,
		redirectTo
	} from '@/common/uniTool';
	import PopBoxVue from '@/components/PopBox.vue';
	import {
		getIvacptData,
		subIvacptStdData
	} from '@/service/ivacpt';
	import {
		useLoginStore
	} from "@/stores/login";
	import {
		reportUrl,
		wsUrl
	} from '@/common/global';
	import {
		onBackPress
	} from '@dcloudio/uni-app'
	import clickEffect from '@/components/pc-clickEffect/pc-clickEffect.vue'
	import getIVAData from './ivaData';

	const props = defineProps(['type', 'evaluatId'])
	const loginStore = useLoginStore()
	const innerAudioContextRef = ref(null) //音频
	const timeRef = ref(null) //定时器
	const eventInfos = ref([]) //事件信息
	const popup = ref(null)
	const eceuId = ref('') //时间戳id
	const isStartCollect = ref(false) //是否开始脑电采集
	const rules = ref([{
		text: '你会看到一个数字闪现在屏幕上，像这样',
		rule: '',
	}, {
		text: '你会看到一个数字闪现在屏幕上，像这样\n这个数字始终是“1”',
		rule: '',
	}, {
		text: '你会看到一个数字闪现在屏幕上，像这样\n这个数字始终是“1”\n一旦你看到这个数字，就点击屏幕一次',
		rule: '',
	}, {
		text: '你会看到一个数字闪现在屏幕上，像这样\n这个数字始终是“1”\n一旦你看到这个数字，就点击屏幕一次',
		rule: '尽可能快一些，但也要小心',
	}, {
		text: '你会看到一个数字闪现在屏幕上，像这样\n这个数字始终是“1”\n一旦你看到这个数字，就点击屏幕一次',
		rule: '尽可能快一些，但也要小心\n当你准备好开始之后，点击“10次热身操作”\n',
	}, {
		text: '你会看到一个数字闪现在屏幕上，像这样\n这个数字始终是“1”\n一旦你看到这个数字，就点击屏幕一次',
		rule: '尽可能快一些，但也要小心\n当你准备好开始之后，点击“10次热身操作”\n',
		btn: '10次热身操作'
	}, {
		text: '现在你将听到一个数字，像这样',
		rule: '',
	}, {
		text: '现在你将听到一个数字，像这样\n这个数字始终是“1”',
		rule: '',
	}, {
		text: '现在你将听到一个数字，像这样\n这个数字始终是“1”\n一旦你听到这个数字，就点击屏幕一次',
		rule: '',
	}, {
		text: '现在你将听到一个数字，像这样\n这个数字始终是“1”\n一旦你听到这个数字，就点击屏幕一次',
		rule: '尽可能快一些，但也要小心\n当你准备好开始之后，点击“10次热身操作”\n',
	}, {
		text: '现在你将听到一个数字，像这样\n这个数字始终是“1”\n一旦你听到这个数字，就点击屏幕一次',
		rule: '尽可能快一些，但也要小心\n当你准备好开始之后，点击“10次热身操作”\n',
		btn: '10次热身操作'
	}])
	const state = reactive({
		stepList: [{
			title: '热身阶段'
		}, {
			title: '练习阶段'
		}, {
			title: '主测试阶段'
		}, {
			title: '恢复阶段'
		}],
		active: 1,
		step: 0, //音频步骤
		isMp3End: false,
		teach: 0, //音频步骤
		showNum1: false,
		showNum2: false,
		testRound: 0, //测试轮次
		isClick: false, //是否开始添加数据
		clickErr: 0, //连续错误次数
		round: 0, //正式环节轮次
		ivaData: null
	})
	watch(() => props.type, (type) => {
		if (type == 1) {
			rules.value[4].rule = '尽可能快一些，但也要小心\n当你准备好开始之后，点击“恢复性测试”\n'
			rules.value[9].rule = '尽可能快一些，但也要小心\n当你准备好开始之后，点击“恢复性测试”\n'
			rules.value[10].rule = '尽可能快一些，但也要小心\n当你准备好开始之后，点击“恢复性测试”\n'
			rules.value[5].rule = '尽可能快一些，但也要小心\n当你准备好开始之后，点击“恢复性测试”\n'
			rules.value[5].btn = '恢复性测试'
			rules.value[10].btn = '恢复性测试'
		}
	}, {
		immediate: true
	})
	onMounted(() => {
		props.type == 1 ? getData('3') : getData('0')
		innerAudioContextRef.value = uni.createInnerAudioContext()
		innerAudioContextRef.value.src = '/static/audio/ivacpt/step-1.MP3'
		innerAudioContextRef.value.play()
		innerAudioContextRef.value.onEnded(() => {
			if (state.step === 1) {
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
				state.isMp3End = true
			}
			if (state.step === 0) {
				state.showNum1 = true
				setTimeout(() => {
					state.showNum1 = false
					setTimeout(() => {
						state.showNum1 = true
						setTimeout(() => {
							state.showNum1 = false
							innerAudioContextRef.value.src = '/static/audio/ivacpt/step-2.MP3'
							innerAudioContextRef.value.play()
							state.step = 1
							state.teach = 1
						}, 200)
					}, 2760)
				}, 200)
			}
		})

	})
	const getData = (num) => {
		getIvacptData({
			traineeId: loginStore.qryActiveTrainee,
			type: num,
			evaluatId: num === '0' ? null : props.type == 1 ? props.evaluatId : state.ivaData.evaluatId,
			version: 'STD'
		}).then(res => {
			console.log(res);
			state.ivaData = res.data
			eceuId.value = res.data.eceuId
		})
	}
	onUnmounted(() => {
		innerAudioContextRef.value && innerAudioContextRef.value.destroy()
		timeRef.value && clearInterval(timeRef.value)
	})

	watch(() => state.active, (active) => {
		console.log(active);
		if (active === 3) { //看结束进入听环节
			console.log(eventInfos.value);
			console.log('看结束进入听环节');
			state.teach = 6
			state.step = 4
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.src = '/static/audio/ivacpt/step-6.MP3'
			innerAudioContextRef.value.play()
		}
	})
	watch([() => state.step, () => state.isMp3End], ([step, isMp3End]) => {
		if (step === 1 && isMp3End) {
			state.teach = 2
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.src = '/static/audio/ivacpt/step-3.MP3'
			innerAudioContextRef.value.play()
			innerAudioContextRef.value.onEnded(() => {
				if (state.step === 1) {
					innerAudioContextRef.value.src = '/static/audio/ivacpt/step-4.MP3'
					innerAudioContextRef.value.play()
					state.teach = 3
					state.step = 2
				}
			})
		} else if (step === 2) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
				state.step = 3

			})
		} else if (step === 3) {
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.src = props.type == 1 ? '/static/audio/ivacpt/step-8.MP3' : '/static/audio/ivacpt/step-5.MP3'
			innerAudioContextRef.value.play()
			state.teach = 4
			innerAudioContextRef.value.onEnded(() => {
				state.teach = 5
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
			})
		} else if (step === 4) {
			innerAudioContextRef.value.onEnded(() => {
				if (state.step === 4) {
					playNum('1')
					state.step = 5
				}
			})
		} else if (step === 5) {
			innerAudioContextRef.value.onEnded(() => {
				if (state.step === 5) {
					setTimeout(() => {
						if (state.step === 5) {
							playNum('1')
						}
						state.step = 6
					}, 2760)
				}
			})
		} else if (step === 6) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.src = '/static/audio/ivacpt/step-2.MP3'
				innerAudioContextRef.value.play()
				state.teach = 7
				state.step = 7
			})
		} else if (step === 7) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.src = '/static/audio/ivacpt/step-7.MP3'
				innerAudioContextRef.value.play()
				state.step = 8
				state.teach = 8
			})
		} else if (step === 8) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.src = '/static/audio/ivacpt/step-4.MP3'
				innerAudioContextRef.value.play()
				state.step = 9
				state.teach = 9
			})
		} else if (step === 9) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.src = props.type == 1 ? '/static/audio/ivacpt/step-8.MP3' : '/static/audio/ivacpt/step-5.MP3'
				innerAudioContextRef.value.play()
				state.step = 10
			})
		} else if (step === 10) {
			innerAudioContextRef.value.onEnded(() => {
				state.teach = 10
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
			})
		}

	})

	const getType = (round, level) => {
		const data = getIVAData('', level)
		let num = data['numList'][round]
		let type = data['signalList'][round]
		let modType = data['modTypelList'][round]
		let signal = data['signalList'][round]
		if (type === 'A') {
			playNum(num, modType, signal)
		}
		if (type === "V") {
			showNum(num, modType, signal)
		}
	}

	const showNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			state.showNum1 = true
			setTimeout(() => {
				state.showNum1 = false
			}, 200)
		} else {
			state.showNum2 = true
			setTimeout(() => {
				state.showNum2 = false
			}, 200)
		}

	}
	const playNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-1.MP3'
			innerAudioContextRef.value.play()
		} else {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-2.MP3'
			innerAudioContextRef.value.play()
		}
	}

	const postData = (num) => {
		isStartCollect.value = false
		subIvacptStdData({
			evaluatId: state.ivaData.evaluatId,
			traineeId: loginStore.qryActiveTrainee,
			type: num,
			eventInfos: eventInfos.value
		}).then(res => {
			if (props.type == 1) {
				popup.value.open()
			} else {
				redirectTo(`/pages/ivacpt/practice?evaluatId=${state.ivaData.evaluatId}`)
			}
		})
	}
	const click = (e) => {
		if (state.isClick) {
			eventInfos.value[state.testRound - 1]['clickDate'].push(new Date().getTime())
		}
	}
	const next = () => {
		isStartCollect.value = true
		innerAudioContextRef.value && innerAudioContextRef.value.stop()
		innerAudioContextRef.value = uni.createInnerAudioContext()
		state.active = 2
		// state.testRound++
		// state.isClick = true
		// getType(state.testRound - 1)
		timeRef.value = setInterval(() => {
			state.testRound++ //轮次加一
			state.isClick = true
			getType(state.testRound - 1, props.type == 1 ? '3' : '0')
			if (state.testRound === 10) { //测试环节结束进入试听环节
				clearInterval(timeRef.value)
				setTimeout(() => {
					state.isClick = false
					state.active = 3
				}, 1500)
			}
			if (state.testRound === 20) { //测试结束
				clearInterval(timeRef.value)
				setTimeout(() => {
					state.isClick = false
					props.type == 1 ? postData('3') : postData('0')
				}, 1500)
			}
		}, 1500)
	}
	const go = () => {
		redirectTo(`/pages/webview/index?url=${reportUrl}#/ivacpt/${getStorageSync('ttoken')}/${loginStore.qryActiveTrainee}/${state.ivaData.evaluatId}/SMSCODE/13`)
	}
</script>

<style lang="scss">
	.iva {
		width: 100vw;
		height: 100vh;
		padding-top: 60rpx;
		background: #333333;
		position: relative;

		&-step {
			width: 120%;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}

		&-content {
			height: 84vh;
			width: 100%;
			position: absolute;
			top: 15%;
			padding: 32rpx;

			&-box {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-btn {
					width: 480rpx;
					height: 116rpx;
					background: #FFFFFF;
					border-radius: 108rpx;
					font-size: 48rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
				}

				&-tril {
					font-size: 40rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FFAB01;
				}

				&-rule {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-img {
						width: 369rpx;
						margin-top: 20rpx;
					}

					&-text {
						font-size: 40rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #ffffff;
						margin-top: 40rpx;
						margin-bottom: 140rpx;
					}

					&-btn {
						width: 628rpx;
						height: 120rpx;
						background: #287FFF;
						border-radius: 74rpx;
						font-size: 40rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #FFFFFF;
					}
				}

				&-tips {
					font-size: 32rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FFAB01;
					text-align: center;
					margin-bottom: 20rpx;
				}

				&-value {
					width: 364rpx;
					height: 464rpx;
					background: #333333;
					border: 4rpx solid #FFDF9E;
					border-radius: 16rpx;
					margin-top: 46rpx;
					margin-bottom: 40rpx;

					&-other {
						width: 334rpx;
						height: 434rpx;
						border: 4rpx solid #FFDF9E;
						border-radius: 16rpx;

						&-other1 {
							width: 300rpx;
							height: 400rpx;
							border: 4rpx solid #FFDF9E;
							border-radius: 16rpx;
							font-size: 320rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: bold;
							color: #00B210;
						}
					}
				}

				&-title {
					font-size: 32rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #FFFFFF;
					display: flex;
					align-items: flex-start;
					width: 100%;
					height: 150rpx;

					&-text {
						flex: 1;
						text-align: center;
					}

					&-icon {
						border-radius: 50%;
						width: 48rpx;
						height: 48rpx;
						background: #FFFFFF;
						color: #287FFF;
						margin-right: 10rpx;
						flex-shrink: 0;
					}
				}
			}
		}
	}
</style>