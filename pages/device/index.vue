<template>
	<view class="device">
		<view class="device-top">
			<view class="device-top-title">
				已连接设备
			</view>
			<view class="device-top-content" v-if="helper.address">
				<image class="device-top-content-img" src="../../static/device/blu-1.png" mode="widthFix"></image>
				<view class="device-top-content-text">
					<view class="device-top-content-text-value">
						名称：{{helper.address.substring(0, 4) === 'Dbay'?'脑电设备':'BrainW<PERSON>'}}
					</view>
					<view class="device-top-content-text-value">
						型号：{{helper.connectingDevice.name}}
					</view>
					<view class="device-top-content-text-value">
						电量：{{helper.electricity}}%
					</view>
				</view>
			</view>
			<view class="device-top-nocontent" v-else>
				<image class="device-top-nocontent-img" src="../../static/blu-no.png" mode="widthFix"></image>
				<view class="device-top-nocontent-text">
					暂无已配对设备，快快添加吧～
				</view>
			</view>
			<view class="device-top-list">
				<view class="device-top-list-title">
					设备列表 <text @click="onClick_scanDevice" class="device-top-list-title-icon iconfont">&#xe631;</text>
				</view>
				<template v-if="state.bluList[0].address">
					<view class="device-top-list-box" v-for="(item,index) in state.bluList" :key="item.address">
						<view class="device-top-list-box-left" v-show="item.address">
							<image class="device-top-list-box-left-img" src="../../static/device/blu-icon.png" mode="widthFix"></image>
							<text class="device-top-list-box-left-address">{{item.name}}</text>
							<text class="device-top-list-box-left-status" v-show="helper.address&&helper.address==item.address">已连接</text>
						</view>
						<view class="device-top-list-box-rignt" v-show="item.address" @click="()=>onClick_connectDevice(item)">
							{{helper.address&&helper.address==item.address?'断开' :'连接'}}
						</view>
					</view>
				</template>

			</view>
		</view>
		<view class="device-bottom">
			<view class="device-bottom-title">
				<view class="device-bottom-title-left">
					<image class="device-bottom-title-left-img" src="../../static/device/blu-wave.png" mode="widthFix"></image>
					<text>脑电波形</text>
				</view>
				<view class="device-bottom-text center" :class="helper.bluData&&helper.bluData.signal?'device-bottom-text-signal':''" v-if="helper.address">
					脑机设备佩戴：{{helper.bluData&&helper.bluData.signal?'脱落':'良好'}}
				</view>
			</view>
			<EchartsVue :amplitudeDate="amplitudeDate" :address="helper.address" v-if="helper.address" />
			<view class="device-bottom-title-noData center" v-else>
				没有数据显示
			</view>
		</view>
	</view>
</template>

<!-- 原生APP -->
<script setup>
	import {
		reactive,
		onMounted,
		watch,
		ref,
		onUnmounted
	} from "vue";
	import {
		useHelper
	} from "../../stores/helper";
	import EchartsVue from './Echarts.vue';
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		hideLoading,
		navigateTo,
		showLoading,
		showToast
	} from '../../common/uniTool';
	import {
		hex2int,
		randomNum
	} from '../../common/method';
	import {
		Blue
	} from '@/utils/bluConfig';
	import {
		BleController
	} from '@/utils/bluType';
	import {
		CTLineNum,
		DbayLineNum,
		changeAddress
	} from "../../common/global";

	import douglasPeucker from '@/utils/douglas_peuker.js'
	const helper = useHelper(); //设备仓库
	const state = reactive({
		bluList: [{
			address: "",
			name: ''
		}],
	})
	const amplitudeDate = ref({})
	onMounted(() => {
		// #ifdef APP-PLUS

		if (helper.address) {
			Blue.bleConnectDeviceID = helper.address
			state.bluList = [helper.connectingDevice]

		}
		ownBluInit()
		// #endif
	})

	onUnmounted(() => {
		console.log('卸载');
		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		onClick_scanStop()
	})
	watch(() => helper.numBox, (numBox) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (numBox.AF7Data.length >= DbayLineNum) {
				helper.getAmplitudeArr()
			}
		} else {
			if (numBox.AF7Data.length === CTLineNum) {
				helper.getAmplitudeArr()
			}
		}

	}, {
		deep: true
	})
	watch(() => helper.amplitudeDateArr, (amplitudeDateArr) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (amplitudeDateArr.AF7Data.length >= DbayLineNum) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item + 25]), 2),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item + 75]), 2),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		} else {
			if (amplitudeDateArr.AF7Data.length === CTLineNum) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item + 25]), 4),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item + 75]), 4),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		}

	}, {
		deep: true
	})
	const ownBluInit = () => {
		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		Blue.start()
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			let value = JSON.parse(state)
			helper.bluData = value
			helper.addAmplitudeDate(value)
			if (value.ele !== helper.electricity) {
				helper.electricity = value.ele
			}
		})
		BleController.addConnectStateListen(state => {
			// console.log(state, '-------------');
			if (state.code === 200 && state.deviceInfo) {
				connectSuccess(state.deviceInfo.address, state.deviceInfo.name) //连接成功
				Blue.isInit = true
			} else if (state.code === -1) {
				uni.showModal({
					title: state.label,
					showCancel: false,
				});
			} else if (state.code === -2) {
				showToast(state.label)
			} else if (state.code === 500) {
				changeAddress('')
				helper.address = ''
				helper.connectingDevice = null
				Blue.bleConnectDeviceID = null
				showToast(state.label)
			}
		})
		BleController.addDeviceListListen(res => {
			const bList = [{
				"name": res.name,
				"address": res.deviceId,
			}]
			if (!state.bluList[0].address) {
				state.bluList = bList
			}
			state.bluList = [...filterList(state.bluList, bList)]
		})
	}
	const onClick_scanDevice = () => {
		// console.log('扫描');
		Blue.start()
	}
	const onClick_scanStop = () => {
		Blue.stopBluetoothDevicesDiscovery()
	}

	//去掉重复数组
	const filterList = (arr1, arr2) => {
		let values = [...arr1, ...arr2]
		let map = new Map()
		for (let item of values) {
			if (!map.has(item.address)) {
				map.set(item.address, item)
			}
		}
		return map.values()
	}

	//连接成功
	const connectSuccess = (value, name) => {
		helper.address = value
		changeAddress(name)
		helper.connectingDevice = {
			"name": name,
			"address": value,
		}
		onClick_scanStop()
		hideLoading()
		showToast('设备连接成功，请开始你的测评吧~')
	}


	//断连
	const disconnectDevice = (device) => {
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.closeBLEConnection(device.address)
		}
		Blue.bleConnectDeviceID = null
	}
	//连接
	const connectDevice = (device) => {
		// console.log(device, 'connectDevice');
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.createBLEConnection(device)
		}
	}
	const onClick_connectDevice = (device) => {
		if (!state.bluList[0].address) {
			showToast('没有搜索到蓝牙设备哦~')
			return
		}
		helper.resetBox()
		helper.bluData = null
		if (helper.address === device.address) {
			disconnectDevice(device) //断连
			changeAddress('')
			helper.address = ''
			helper.connectingDevice = null
			showToast('设备蓝牙已断开')
			return
		}
		if (helper.address && helper.address !== device.address) {
			disconnectDevice(helper.connectingDevice) //断连
		}
		changeAddress('')
		helper.address = ''
		helper.connectingDevice = null
		showLoading('设备连接中...')
		connectDevice(device) //连接
	}
</script>

<style lang="scss">
	.device {
		width: 100vw;
		flex: 1;
		background: #EEEEEE;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		&-bottom {
			background: #FFFFFF;
			display: flex;
			flex-direction: column;
			margin-top: 8rpx;
			width: 100%;

			&-text {
				background: #F7FAFF;
				border-radius: 16rpx;
				padding: 8rpx 30rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #287FFF;

				&-signal {
					background: #FFF7F7;
					color: #FF4747;
				}
			}

			&-title {
				width: 100%;
				display: flex;
				align-items: center;
				font-size: 32rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #111111;
				padding: 16rpx 32rpx 16rpx 32rpx;

				&-noData {
					height: 300rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}

				&-left {
					display: flex;
					align-items: center;
					flex: 1;

					&-img {
						width: 40rpx;
						height: 40rpx;
						margin-right: 16rpx;
					}
				}

				&-full {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					background: #2C72DA;
					border-radius: 8rpx;
					padding: 12rpx 16rpx;

					&-icon {
						margin-left: 10rpx;
					}
				}

				&-max {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 12rpx 16rpx;
					color: #111111;
					background: #F6F6F6;
					border: 1rpx solid #E3E3E3;
					border-radius: 8rpx;
					margin-left: 16rpx;
				}
			}


		}

		&-top {
			display: flex;
			flex-direction: column;
			background: #FFFFFF;
			width: 100%;
			padding: 16rpx 32rpx 16rpx 32rpx;

			&-nocontent {
				display: flex;
				flex-direction: column;
				align-items: center;
				height: 300rpx;
				justify-content: center;

				&-img {
					width: 204rpx;
					margin-bottom: 32rpx;
				}

				&-text {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
				}
			}

			&-title {
				font-size: 32rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
			}

			&-content {
				background: #F6F6F6;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				height: 254rpx;
				margin-top: 24rpx;

				&-img {
					width: 324rpx;
				}

				&-text {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					margin-left: 38rpx;
					height: 70%;
					display: flex;
					flex-direction: column;
					justify-content: space-around;
				}
			}

			&-list {
				display: flex;
				flex-direction: column;
				margin-top: 40rpx;

				&-title {
					font-size: 32rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
					margin-bottom: 32rpx;

					&-icon {
						color: #287FFF;
						margin-left: 10rpx;
					}
				}

				&-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 32rpx;

					&-left {
						display: flex;
						align-items: center;
						flex: 1;

						&-img {
							width: 56rpx;
							height: 56rpx;
						}

						&-address {
							font-size: 24rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
							margin-left: 24rpx;
							margin-right: 16rpx;

						}

						&-status {
							border-radius: 4rpx;
							border: 1rpx solid #111111;
							font-size: 18rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
							padding: 3rpx 8rpx;
						}
					}

					&-rignt {
						background: #F6F6F6;
						border-radius: 43rpx;
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #287FFF;
						padding: 16rpx 44rpx;
					}
				}
			}
		}
	}
</style>