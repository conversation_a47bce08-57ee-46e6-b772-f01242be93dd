<template>
	<view class="from center" v-if="state.show">
		<uni-forms ref="valiForm" :rules="rules" :model="state.valiFormData" style="width: 80%;">
			<uni-forms-item label="对应测评项目" required name="evaluatItem" :label-width="110" class="center">
				<uni-data-checkbox v-model="state.valiFormData.evaluatItem" :localdata="state.assess" />
			</uni-forms-item>
			<uni-forms-item label="临床诊断分类" required name="medicalType" :label-width="110" class="center">
				<uni-data-checkbox v-model="state.valiFormData.medicalType" placeholder="请输入年龄" :localdata="state.types" />
			</uni-forms-item>
			<uni-forms-item label="状态" required name="eyeState" :label-width="110" class="center">
				<uni-data-checkbox v-model="state.valiFormData.eyeState" placeholder="请输入年龄" :localdata="state.eyes" />
			</uni-forms-item>
			<uni-forms-item label="其他内容备注" name="remark" :label-width="110">
				<uni-easyinput type="textarea" :maxlength="120" v-model="state.valiFormData.remark" placeholder="字数限制200字以内" />
			</uni-forms-item>
			<button type="primary" form-type="submit" class="from-btn center" @click="goFull">
				进入脑电采集
			</button>
		</uni-forms>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref,
		onMounted,
		onUnmounted
	} from "vue";
	import {
		onShow,
	} from '@dcloudio/uni-app'
	import {
		navigateTo
	} from "../../common/uniTool";
	import {
		BleController
	} from '@/utils/bluType';
	const valiForm = ref(null)
	const rules = ref({
		// 对name字段进行必填验证
		evaluatItem: {
			// name 字段的校验规则
			rules: [
				// 校验 name 不能为空
				{
					required: true,
					errorMessage: '对应测评项目不能为空',
				},
			],
		},
		medicalType: {
			// name 字段的校验规则
			rules: [
				// 校验 name 不能为空
				{
					required: true,
					errorMessage: '临床诊断分类不能为空',
				},
			],
		},
		eyeState: {
			// name 字段的校验规则
			rules: [
				// 校验 name 不能为空
				{
					required: true,
					errorMessage: '状态不能为空',
				},
			],
		},
	})
	const state = reactive({
		valiFormData: {
			evaluatItem: '',
			medicalType: '',
			remark: '',
			type: ''
		},
		show: false,
		assess: [{
			text: 'CPT测评',
			value: 0
		}, {
			text: 'PPVT测评',
			value: 1
		}, {
			text: '划消测评',
			value: 2
		}, {
			text: '量表评估',
			value: 3
		}, {
			text: '静息态',
			value: 4
		}, {
			text: '其他项目',
			value: 9
		}],
		types: [{
			text: '注意缺陷',
			value: 0
		}, {
			text: '多动冲动',
			value: 1
		}, {
			text: '注意缺陷及多动冲动',
			value: 9
		}, {
			text: '其他',
			value: 8
		}],
		eyes: [{
			text: '睁眼',
			value: 1
		}, {
			text: '闭眼',
			value: 0
		}, {
			text: '其他',
			value: 9
		}],
	})
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(false);
		state.show = false
		state.valiFormData = {
			evaluatItem: '',
			medicalType: '',
			remark: '',
			eyeState: ''
		}
		uni.showLoading({
			title: "正在进入脑电采集系统..."
		})
		setTimeout(() => {
			plus.screen.unlockOrientation();
			plus.screen.lockOrientation('landscape-primary');
			uni.hideLoading();
			state.show = true
		}, 1200)
		//#endif
		BleController.addDeviceAcceptListListen(() => {})
	})

	onUnmounted(() => {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary');
		// #endif
	})
	const goFull = () => {
		valiForm.value.validate().then(res => {
			navigateTo(`/pages/collect/fullScreen?values=${JSON.stringify(res)}`)
		}).catch(err => {
			console.log('err', err);
		})
	}
</script>

<style lang="scss">
	.from {
		width: 100vw;
		height: 100vh;

		&-btn {
			width: 190rpx;
			height: 40rpx;
			background: #0485F4;
			border-radius: 28rpx;
			font-size: 20rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: bold;
			color: #FFFFFF;
			margin: 0 auto;
			margin-top: 50rpx;
		}
	}
</style>