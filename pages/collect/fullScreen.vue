<template>
	<view class="full">
		<view class="full-info">
			<view class="full-top">
				<text class="full-top-icon iconfont" @click="goBack">&#xe604;</text>
				<view class="full-top-content center">
					<view class="full-top-content-text">
						{{state.userInfo.traineeName}}
					</view>
					<view class="full-top-content-text">
						{{state.userInfo.sex===1?'男':'女'}}
					</view>
					<view class="full-top-content-text">
						{{state.userInfo.birth}}
					</view>
					<view class="full-top-content-text">
						{{state.eva}}
					</view>
					<view class="full-top-content-text">
						{{state.typeText}}
					</view>
					<view class="full-top-content-text">
						{{state.eyes}}
					</view>
				</view>
				<view class="full-top-right" v-if="helper.bluData&&!helper.bluData.signal">
					当前脑电信号
				</view>
			</view>
			<view class="full-top" style="justify-content: flex-end;padding-top: 8rpx;">
				<view class="full-top-content center">
					<view class="full-top-content-text full-top-content-text-time" style="width: 100rpx;">
						{{state.timeText}}
					</view>
					<view class="full-top-content-btn center" @click="startGather" v-if="!state.start">
						<text class="full-top-content-btn-icon iconfont">&#xea90;</text>开始采集
					</view>
					<view class="full-top-content-btn center" @click="endGather" v-else>
						<text class="full-top-content-btn-icon iconfont">&#xea91;</text>停止采集
					</view>
				</view>
				<view class="full-top-right center">
					<view class="full-top-right-tip center" v-if="helper.bluData" :class="helper.bluData.signal?'full-top-right-tip-signal':''">
						{{helper.bluData.signal?'脱落':'良好'}}
					</view>
				</view>
			</view>
		</view>
		<view class="full-value" v-if="state.start">
			<EchartAF7Vue :amplitudeDate='amplitudeDate' :address="helper.address" />
			<EchartAF8Vue :amplitudeDate='amplitudeDate' :address="helper.address" />
		</view>
		<view class="full-value-no" v-else>
			当前为接入脑电信号页面，请点击“开始”进行采集
		</view>
	</view>

	<uni-popup ref="tip" :is-mask-click="false" :animation="false">
		<view class="tip">
			<view class="tip-title">
				{{state.clickBack?'停止脑电数据评估采集':'保存脑电数据'}}
			</view>
			<view class="tip-text" v-if="state.clickBack">
				直接点击返回按键退出采集页面<br />本条脑电数据记录不会保存
				{{state.clickBack?``:'是否确定停止本次脑电评估'}}
			</view>
			<view class="tip-text" v-else>
				是否确定停止本次脑电评估
			</view>
			<view class="tip-btn" v-if="state.clickBack">
				<view class="tip-btn-right center" @click="stop">
					停止采集
				</view>
				<view class="tip-btn-left center" @click="next">
					继续采集
				</view>
			</view>
			<view class="tip-btn" v-else>
				<view class="tip-btn-left center" @click="next">
					继续采集
				</view>
				<view class="tip-btn-right center" @click="stop">
					停止采集
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		randomNum,
		formatSeconds
	} from '../../common/method';
	import {
		onMounted,
		reactive,
		onUnmounted,
		ref,
		watch
	} from "vue";
	import {
		uptEndDate,
		subEvaluationInfo
	} from '../../service/eegassess';
	import {
		getVisitorDetail
	} from '../../service';
	import {
		navigateBack,
		redirectTo
	} from "../../common/uniTool";
	import {
		onBackPress,
	} from '@dcloudio/uni-app'
	import {
		wsUrl
	} from '../../common/global';
	import ws from '@/utils/websocket.js'
	import douglasPeucker from '../../utils/douglas_peuker';
	import {
		useHelper
	} from "../../stores/helper";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		useSocketStore
	} from "../../stores/socket";
	import EchartAF7Vue from './EchartAF7.vue';
	import EchartAF8Vue from './EchartAF8.vue';
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		addRealEndTime,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	import {
		CTLineNumFull,
		DbayLineNumFull
	} from "../../common/global";
	const socket = useSocketStore(); //websocket仓库
	const loginStore = useLoginStore()
	const helper = useHelper(); //设备仓库
	const tip = ref(null)
	const amplitudeDate = ref({})

	const props = defineProps({
		values: {
			type: String,
		},
	})
	const timeRef = ref(null)
	const eceuId = ref('') //时间戳校准id
	const state = reactive({
		start: false, //是否开始采集
		clickBack: false, //是否点击返回键
		timeText: '00:00:00',
		time: 0,
		evaluatId: '',
		userInfo: {
			traineeName: '',
			sex: '',
			birth: ''
		},
		eva: '',
		typeText: '',
		eyes: '',
	})
	const longTime = ref(null) //时间戳
	const bagIndex = ref(null) //包序
	onMounted(() => {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('landscape-primary'); // 正常横屏
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}
		socket.socketTask.getWebSocketMsg((data) => {
			state.webData = data
		})
		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		// #endif
		getUserInfo()
		ownBluInit()
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			const ret = JSON.parse(state)
			helper.addAmplitudeDateFull(ret)
			helper.bluData = ret
			if (socket.socketTask && !socket.socketTask.userClose && eceuId.value && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(state, eceuId.value, '', socket.socketTask)))
			}
		})
	}
	const getUserInfo = () => {
		getVisitorDetail({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.userInfo = res.data
		})
	}
	const geteva = (obj) => {
		let text = ''
		if (obj.evaluatItem === 0) {
			text = 'CPT测评'
		} else if (obj.evaluatItem === 1) {
			text = 'ppvt测评'
		} else if (obj.evaluatItem === 2) {
			text = '划消测评'
		} else if (obj.evaluatItem === 3) {
			text = '量表测评'
		} else if (obj.evaluatItem === 4) {
			text = '静息态'
		} else {
			text = '其他项目'
		}
		return text
	}
	const getype = (obj) => {
		let text = ''
		if (obj.medicalType === 0) {
			text = '注意缺陷'
		} else if (obj.medicalType === 1) {
			text = '多动冲动'
		} else if (obj.medicalType === 8) {
			text = '其他'
		} else {
			text = '注意缺陷及多动冲动'
		}
		return text
	}
	const getEye = (obj) => {
		let text = ''
		if (obj.eyeState === 1) {
			text = '睁眼'
		} else if (obj.eyeState === 0) {
			text = '闭眼'
		} else {
			text = '其他'
		}
		return text
	}

	watch(() => props.values, (values) => {
		let obj = JSON.parse(values)
		state.eva = geteva(obj)
		state.typeText = getype(obj)
		state.eyes = getEye(obj)
	}, {
		immediate: true
	})
	watch(() => helper.numBox, (numBox) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (numBox.AF7Data.length >= DbayLineNumFull) {
				helper.getAmplitudeArrFull()
			}
		} else {
			if (numBox.AF7Data.length === CTLineNumFull) {
				helper.getAmplitudeArrFull()
			}
		}
	}, {
		deep: true
	})
	watch(() => helper.amplitudeDateArr, (amplitudeDateArr) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (amplitudeDateArr.AF7Data.length >= DbayLineNumFull) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item]), 2),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item]), 2),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		} else {
			if (amplitudeDateArr.AF7Data.length === CTLineNumFull) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item]), 2),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item]), 2),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		}
	}, {
		deep: true
	})
	onBackPress(() => {
		console.log('阻止');
		if (state.start) {
			state.clickBack = true
			tip.value.open('center')
			return true
		}
	})
	//开始采集
	const startGather = () => {
		getTimer()
		state.start = true

		let params = {
			traineeId: loginStore.qryActiveTrainee,
			...JSON.parse(props.values)
		}
		subEvaluationInfo(params).then(res => {
			state.evaluatId = res.data.evaluatId
			eceuId.value = res.data.eceuId

		}).catch(err => {})
	}
	const getTimer = () => {
		timeRef.value = setInterval(() => {
			state.time++
			state.timeText = formatSeconds(state.time)
		}, 1000)
	}
	const endGather = () => {
		tip.value.open('center')
	}
	const goBack = () => {
		navigateBack()
	}
	onUnmounted(() => {
		// #ifdef APP-PLUS
		if (socket.socketTask) {
			addRealEndTime(eceuId.value)
			eceuId.value = ''
			socket.socketTask.closeSocket()
		}
		// #endif
		if (timeRef.value) {
			clearInterval(timeRef.value)
			timeRef.value = null
		}

		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary');
		// #endif
		console.log('页面卸载');
	})
	const stop = () => {
		console.log(state.evaluatId);
		if (state.clickBack) {
			console.log('返回');
			state.start = false
			navigateBack()
		} else {
			uptEndDate({
				evaluatId: state.evaluatId
			}).then(res => {
				console.log("res: " + JSON.stringify(res));
				state.start = false
				redirectTo('/pages/home/<USER>/index')
			}).catch(err => {
				console.log("err: " + JSON.stringify(err));
			})

		}

	}
	const next = () => {
		state.clickBack = false
		state.start = true
		tip.value.close()
	}
</script>

<style lang="scss">
	.full {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;

		&-value {
			background: #FFFFFF;
			flex: 1;

			&-no {
				font-size: 20rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #287FFF;
				text-align: center;
				margin: auto 0;
			}
		}

		&-info {
			background: #FFFFFF;
			font-size: 15rpx;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #111111;
			padding: 20rpx;
			border-bottom: 10rpx solid #F6F6F6;
		}

		&-top {
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-right {
				display: flex;
				align-items: center;
				width: 90rpx;

				&-tip {
					width: 50rpx;
					height: 20rpx;
					background: #F1F7FF;
					border-radius: 5rpx;
					border: 1rpx solid #287FFF;
					font-size: 15rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #287FFF;

					&-signal {
						font-size: 15rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #FF4747;
						width: 50rpx;
						height: 20rpx;
						background: #FFF7F7;
						border-radius: 5rpx;
						border: 1rpx solid #FF4747;
					}

				}

				&-img {
					width: 15rpx;
				}
			}

			&-content {
				flex: 1;
				display: flex;
				align-items: center;

				&-text {
					margin: 0 3rpx;

					&-time {
						font-size: 20rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
					}
				}

				&-btn {
					width: 93rpx;
					height: 20rpx;
					background: #FFFFFF;
					border-radius: 5rpx;
					border: 1rpx solid #999999;
					font-size: 15rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;

					&-icon {
						font-size: 12rpx;
						margin-right: 4rpx;
					}
				}
			}
		}
	}

	.tip {
		width: 443rpx;
		height: 255rpx;
		background: #FFFFFF;
		border-radius: 25rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;

		&-title {
			font-size: 25rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: bolder;
			color: #111111;
		}

		&-text {
			font-size: 20rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			text-align: center;
		}

		&-btn {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-around;

			&-left {
				width: 149rpx;
				height: 40rpx;
				border-radius: 25rpx;
				border: 1rpx solid #0485F4;
				font-size: 20rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #0485F4;
			}

			&-right {
				width: 149rpx;
				height: 40rpx;
				background: #0485F4;
				border-radius: 25rpx;
				font-size: 20rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #FFFFFF;
			}
		}
	}
</style>