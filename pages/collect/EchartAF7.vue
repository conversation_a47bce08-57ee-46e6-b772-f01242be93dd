<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="echarts.onClick" :name="address" :change:name="echarts.updateName" :prop="amplitudeDate" :change:prop="echarts.updateEcharts" id="AF7FPz" class="echarts"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	import {
		getLine
	} from '@/utils/bandpass_filter_offline.js'
	import {
		randomNum
	} from "@/common/method";
	export default {
		name: 'Echarts',
		props: {
			amplitudeDate: {
				required: true
			},
			address: {
				required: true
			}
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="echarts" lang="renderjs">
	let myChart
	let data = new Array(100).fill(25)
	export default {
		data() {
			return {
				address: '',
				option: {
					animation: true,
					animationDuration: 5000,
					grid: {
						left: '10%',
						right: '2%',
						bottom: '20%',
						top: '6%'
					},
					xAxis: {
						name: '',
						minInterval: 2,
						minorSplitLine: {
							show: false
						},
						minorTick: {
							show: false
						},
						axisLabel: {
							show: false
						},
						axisLine: {
							show: true
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false,
						}
					},
					yAxis: {
						name: "AF7-FPz",
						nameLocation: "middle",
						nameGap: 38.5,
						min: -40,
						max: 40,
						minorTick: {
							show: false
						},
						minorSplitLine: {
							show: false
						},
						axisLabel: {
							show: true
						},
						axisLine: {
							show: true
						},
						axisTick: {
							show: true
						},
						splitLine: {
							show: false
						},
						nameRotate: 0
					},
					series: [{
						name: 'AF7-FPz',
						type: 'line',
						showSymbol: false,
						clip: false,
						data: data,
						smooth: true,
						lineStyle: {
							width: 1
						},
						markLine: {
							silent: true,
							symbol: "none",
							label: {
								show: false
							},
							animation: false,
							lineStyle: {
								color: '#333'
							},
							data: [{
									xAxis: 250,
								},
								{
									xAxis: 500
								},
								{
									xAxis: 750
								},
								{
									xAxis: 1000
								},
								{
									xAxis: 1250
								},
								{
									xAxis: 1500
								},
								{
									xAxis: 1750
								}
							]
						}
					}]
				}
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('AF7FPz'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateName(newValue, oldValue, ownerInstance, instance) {
				this.address = newValue
				console.log(newValue);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				myChart && myChart.clear()
				if (this.address.substring(0, 4) !== 'Dbay') {
					this.option.series[0].markLine.data = [{
							xAxis: 500,
						},
						{
							xAxis: 1000
						},
						{
							xAxis: 1500
						},
						{
							xAxis: 2000
						},
						{
							xAxis: 2500
						},
						{
							xAxis: 3000
						},
						{
							xAxis: 3500
						}
					]
				}
				// 监听 service 层数据变更
				if (Object.keys(newValue).length > 0) {
					data = newValue.AF7Data
					this.option.series[0].data = data
					myChart && myChart.setOption(this.option)
				}
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 160rpx;
		position: relative;
	}



	.echarts {
		height: 100%;
		width: 100%;
	}
</style>