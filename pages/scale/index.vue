<template>
	<view class="question">
		<view class="question-schedule">
			<progress class="question-schedule-icon" :percent="questionConfig.questionIndex*5.56" show-info border-radius="50" activeColor="#55CC66" stroke-width="8" />
			<view class="question-schedule-text"><text class="question-schedule-text-num">{{questionConfig.questionIndex}}</text>/18</view>
		</view>
		<view class="question-content">
			<view class="question-content__text">请根据您孩子过去一周内的实际情况选择</view>
			<view class="question-content__topic">
				<text class="question-content__topic-text">{{question[questionConfig.questionIndex - 1]}}</text>
				<view v-for="item in options" class="question-content__topic-options" :class="[questionConfig.answer==item.value?'clicked':'']" :key="item.index" @click="click(item)">
					<text class="question-content__topic-options-text">{{item.text}}</text>
				</view>
			</view>
		</view>
		<view class="question-bottom">
			<view class="question-bottom-next center" @click="previous" v-if="showBottom">
				<text class="question-bottom-next-text">上一题</text>
			</view>
		</view>
		<uni-popup ref="popup" type="center" :animation="false" :is-mask-click="false">
			<view class="popup-box">
				<view class="popup-box-top">
					<view class="popup-box-top__text">
						<view class="popup-box-top__text-top">确认提交问卷</view>
					</view>
				</view>
				<view class="popup-box-center">点击确认,将会提交问卷结果。
					<!-- 		<view class="">
						在接下来的3个测评任务中请将设备交给孩子,大约会花费10分钟时间。
					</view> -->
				</view>
				<view class="popup-box__button">
					<view class="popup-box__button-left" @click="close">取消</view>
					<view class="popup-box__button-right" @click="go">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		computed,
		reactive,
		ref
	} from "vue";
	import {
		navigateTo,
		redirectTo,
		navigateBack,
		showLoading,
		hideLoading
	} from "../../common/uniTool";
	import {
		postQuestionnaire
	} from "../../service/scale";
	import {
		onShow,
	} from '@dcloudio/uni-app'
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		throttle
	} from 'lodash';
	const loginStore = useLoginStore()
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif
	})
	const props = defineProps(['round', 'evaluatId'])
	const popup = ref(null)
	const questionConfig = reactive({
		answers: [],
		questionIndex: 1,
		answer: -1,
		isClick: true,
		isEnd: -1
	})
	const options = ref([{
		value: 0,
		text: '无'
	}, {
		value: 1,
		text: '偶尔'
	}, {
		value: 2,
		text: '常常'
	}, {
		value: 3,
		text: '总是'
	}])
	const question = ref([
		'学习、做事时不注意细节，出现粗心大意的错',
		'打扰别人',
		'在学习、做事或玩的时候很难保持注意力集中',
		'别人对他讲话时好像没在听或没听见',
		'做作业或完成任务时虎头蛇尾，不能始终按要求做事',
		'很难组织好分配给他的任务或活动',
		'不愿意做需要持续用脑的事情（例如家庭或课堂作业）',
		'把学习、生活必需的东西弄丢',
		'容易因外界刺激而分心',
		'忘记分配的任务',
		'坐不住，手脚动作多或身体扭来扭去',
		'在教室或者其他需要静坐的场合离开座位',
		'在不该动的场合乱跑（中学生有主观上坐不住的感觉）',
		'在休闲活动中很难保持安静',
		'忙忙碌碌，精力充沛',
		'说话过多',
		'在问题没说完时抢答',
		'很难按顺序等候'
	])
	//是否展示上下一题按钮
	const showBottom = computed(() => {
		return questionConfig.questionIndex > 1
	})
	const close = throttle(() => {
		popup.value.close()
		questionConfig.answers.pop()
	}, 2000); // 2秒节流时间

	const go = () => {
		uni.showLoading({
			mask: true,
			title: '正在提交量表中...'
		})
		postQuestionnaire({
			answers: questionConfig.answers,
			evaluatId: props.evaluatId,
			traineeId: loginStore.qryActiveTrainee,
			round: props.round
		}).then(res => {
			hideLoading()
			navigateBack()
			// redirectTo('/pages/evaluation/index')
		}).catch((err) => {
			hideLoading()
			console.log(err);
		})
	}
	const click = (item) => {
		if (!questionConfig.isClick) {
			return
		}
		questionConfig.answer = item.value
		questionConfig.isClick = false
		setTimeout(() => {
			if (questionConfig.answers.length == 18) {
				questionConfig.answers[17] = `17,${item.value}`
			} else {
				questionConfig.answers.push(`${questionConfig.questionIndex},${item.value}`)
			}

			if (questionConfig.questionIndex >= 18) {
				popup.value.open('center')
				questionConfig.answer = item.value
				questionConfig.isClick = true
				return
			}
			questionConfig.questionIndex += 1
			questionConfig.answer = -1
			questionConfig.isClick = true
		}, 250)
	}
	const previous = () => {
		questionConfig.questionIndex -= 1
		questionConfig.answer = questionConfig.answers[questionConfig.questionIndex - 1].split(',')[1]
		questionConfig.answers.pop()
	}
</script>

<style lang="scss">
	.popup-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		width: 640rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 0 40rpx;

		&-top {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			&__icon {
				width: 160rpx;
				height: 160rpx;
			}

			&__text {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-top {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
					margin-bottom: 80rpx;
					margin-top: 80rpx;
				}
			}
		}

		&-center {
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 48rpx;
			margin-bottom: 72rpx;
		}

		&__button {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			&-left {
				width: 260rpx;
				height: 120rpx;
				background: rgba($color: #55cc66, $alpha: 0.1);
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #55cc66;
				text-align: center;
				line-height: 120rpx;
			}

			&-right {
				width: 260rpx;
				height: 120rpx;
				background: #55cc66;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
			}

			&-only {
				width: 560rpx;
				height: 120rpx;
				background: #55cc66;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
			}
		}
	}

	.question {
		width: 100vw;
		height: 100vh;
		background: #f6f6f6;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		position: relative;

		&-schedule {
			display: flex;
			width: 100%;
			flex-direction: column;
			align-items: center;

			&-icon {
				width: 100%;
			}

			&-text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-top: 40rpx;

				&-num {
					font-size: 48rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
				}
			}
		}

		&-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;

			&__text {
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-bottom: 40rpx;
			}

			&__topic {
				width: 88%;
				background: #ffffff;
				border-radius: 32rpx;
				padding: 40rpx 40rpx 40rpx 40rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
					line-height: 56rpx;
					height: 128rpx;
					display: flex;
					align-items: center;
				}

				&-options {
					width: 590rpx;
					height: 120rpx;
					margin-bottom: 20rpx;
					background: #f6f6f6;
					border-radius: 60rpx;
					text-align: center;
					line-height: 120rpx;
					color: #111111;

					&-text {
						font-size: 36rpx;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: 600;
						color: #111111;
					}
				}
			}
		}

		&-bottom {
			width: 670rpx;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 20rpx;

			&-next {
				width: 510rpx;
				height: 100rpx;
				background: #f6f6f6;
				border-radius: 60rpx;
				border: 2rpx solid #55cc66;
				margin: 0 auto;

				&-text {
					font-size: 36rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #55cc66;
				}
			}

			&-one {
				width: 100%;
				height: 100%;
				background: #55cc66;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			&-left {
				width: 315rpx;
				height: 120rpx;
				background: #e5f0e6;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #55cc66;
				}
			}

			&-right {
				width: 315rpx;
				height: 120rpx;
				background: #55cc66;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}
	}



	.clicked {
		border: 4rpx solid #55cc66;
		background: #ffffff;
		color: #55cc66;
	}
</style>