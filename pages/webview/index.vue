<template>
	<web-view :src="props.url" class="web" @message="handleMessage"></web-view>
</template>

<script setup>
	import {
		hideLoading,
		showLoading
	} from '../../common/uniTool';
	import {
		onMounted
	} from "vue";
	const props = defineProps(['url'])
	onMounted(() => {
		showLoading('报告生成中...')
	})
	const handleMessage = (evt) => {
		if (evt.detail.data[0].action) {
			hideLoading()
		}
	}
</script>

<style lang="scss">
	.web {
		flex: 1
	}
</style>