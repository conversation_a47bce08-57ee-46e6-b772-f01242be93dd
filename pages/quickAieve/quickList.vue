<template>
	<view class="quick-list">
		<image class="quick-list-img" src="../../static/quick/quick-bg-game.png" />
		<view class="quick-list-bgc">
			<view class="quick-list-bgc-top">
				<view>请又快又准地完成本测评。</view>
				<view>我们会从三个维度来对你的大脑专注能力进</view>
				<view>行评估，结束之后会生成你的专注力报告。</view>
			</view>
			<view v-for="item in project" class="quick-list-bgc-item">
				<image class="quick-list-bgc-item-img" :src="item.img" />
				<view class="quick-list-bgc-item-content">
					<view class="quick-list-bgc-item-content-title">{{item.title}}</view>
				</view>
			</view>
		</view>
		<image class="quick-list-btn"></image>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue'
	const project = ref([{
			title: '测评一：帮助罗伯特',
			text: '自我控制能力',
			num: '1分钟',
			img: 'http://**************:9900/quickAieve/sc01_icon.png'
		},
		{
			title: '测评二：追踪罗伯特',
			text: '短时记忆能力',
			num: '1分钟',
			img: 'http://**************:9900/quickAieve/sc02_icon.png'
		},
		{
			title: '测评三：罗伯特解谜',
			text: '归纳总结能力',
			num: '3分钟',
			img: 'http://**************:9900/quickAieve/sc03_icon.png'
		}
	])
</script>

<style lang="scss">
	.quick-list {
		width: 100%;
		height: 100vh;
		position: relative;

		&-img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: -999;
		}

		&-bgc {
			width: 100%;
			height: 1136rpx;

			&-top {
				text-align: center;
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 48rpx;
				width: 654rpx;
				padding-top: 83rpx;
				margin-bottom: 72rpx;
				background: linear-gradient(270deg,
						rgba(17, 17, 17, 0) 0%,
						rgba(84, 179, 218, 0.6) 50%,
						rgba(17, 17, 17, 0) 100%);
			}

			&-item {
				display: flex;
				align-items: center;
				width: 598rpx;
				margin-bottom: 64rpx;
				position: relative;
				height: 216rpx;

				&-img {
					width: 216rpx;
					height: 216rpx;
					background: #d8d8d8;
					border-radius: 16rpx;
					position: absolute;
					z-index: 999;
					top: 0;
					left: 0;
				}

				&-content {
					padding: 32rpx 32rpx 32rpx 60rpx;
					width: 400rpx;
					height: 200rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					background: linear-gradient(270deg,
							rgba(84, 179, 218, 0.6) 0%,
							rgba(17, 17, 17, 0) 100%);
					border-radius: 32rpx;
					flex: 1;
					position: absolute;
					right: 20rpx;
					top: 0;
					margin-top: 8rpx;

					&-title {
						font-size: 32rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #ffffff;
					}

					&-text {
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #ffffff;

						&-icon {
							color: #ffffff;
							margin-right: 10rpx;
							font-size: 26rpx;
						}
					}

					&-num {
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #ffffff;

						&-icon {
							color: #ffffff;
							margin-right: 18rpx;
							font-size: 28rpx;
						}
					}
				}
			}
		}

		&-btn {
			width: 590rpx;
			height: 112rpx;
			position: absolute;
			bottom: 98rpx;
			left: 50%;
			transform: translateX(-50%);
		}

		&-text {
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #ffffff;
			margin-top: 52rpx;
		}
	}
</style>
