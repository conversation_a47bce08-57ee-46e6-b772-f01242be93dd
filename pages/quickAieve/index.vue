<template>
	<view class="quick">
		<image class="quick-bg" src='http://**************:9900/minipro/images/quick/quick-bg.png' />
		<view class="quick-box">
			<view class="quick-box-text">把手机交给您的孩子，他也可以测评哦！</view>
			<image class="quick-box-join" src="../../static/quick/quick-join.png" @click="go" mode="widthFix" />
		</view>
	</view>
</template>

<script setup>
	import {
		navigateTo
	} from '@/common/uniTool.js'
	const go = () => {
		navigateTo('/pages/quickAieve/quickList')
	}
</script>

<style lang="scss">
	.quick {
		width: 100%;
		height: 100vh;

		&-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}

		&-box {
			position: absolute;
			bottom: 108rpx;
			left: 50%;
			transform: translateX(-50%);

			&-join {
				width: 590rpx;
			}

			&-text {
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #253b93;
				margin-bottom: 16rpx;
				width: 100%;
				text-align: center;
			}
		}
	}
</style>
