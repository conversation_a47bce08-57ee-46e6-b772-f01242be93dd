<template>
	<view class="shopping" :style="{'background-image': props.level % 2 !== 0?`url('/static/shopping/shop-ani-bg.png')`:`url('/static/shopping/shop-skin-bg.png')`}">
		<image class="shopping-back" src="../../static/shopping/shopping-back-icon.png" mode="widthFix" @click="goBack"></image>
		<scroll-view class="shopping-list" v-if="state.type===1" scroll-y="true">
			<view class="shopping-list-box">
				<view v-for="(item,index) in state.shopList" :key="item.carrierId" class="shopping-list-item" :style="{ border:item.useFlag==='1'?'': 'none' }">
					<image :src="`${pdfUrl}${item.skinUrl}`" class="shopping-list-item-img" mode="widthFix"></image>
					<view class="shopping-list-item-name">
						{{item.zodiacName||item.skinName}}
					</view>
					<view class="shopping-list-item-buy center" v-if="item.summonFlag==='1'">
						已召唤
					</view>
					<view class="shopping-list-item-buyno" v-else>
						<view class="shopping-list-item-buyno-btn center" @click="()=>exchange(item)">
							召唤
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<!-- <view class="shopping-list" v-else>
			<view v-for="(item,index) in state.skinList" :key="item.skinId" class="shopping-list-item" :style="{ border:item.useFlag==='1'?'': 'none' }">
				<view class="shopping-list-item-name">
					{{item.carrierName}}
				</view>
				<image :src="`${pdfUrl}${item.skinUrl}`" class="shopping-list-item-img" mode="widthFix"></image>
				<view class="shopping-list-item-buy" v-if="item.buyFlag==='1'">
					已拥有
				</view>
				<view class="shopping-list-item-buyno" v-else>
					<view class="shopping-list-item-buyno-value">
						<image class="shopping-list-item-buyno-value-icon" src="../../static/shopping/shopping-money-icon.png" mode="widthFix"></image>
						{{item.points}}个
					</view>
					<view class="shopping-list-item-buyno-btn shopping-list-item-use-skin center" @click="()=>exchangeSkin(item.carrierId,item.skinId)"
						:style="{'background': loginStore.qryPoints<item.points?'#E3E3E3':'#FF943B','color': loginStore.qryPoints<item.points?'#111111':'#FFFFFF'}">
						兑换
					</view>
				</view>
				<view class="shopping-list-item-use center shopping-list-item-use-skin" v-if="item.useFlag==='1'&&item.buyFlag==='1'" style="background: #FF7A4C;color: #FFFFFF;">
					当前
				</view>
				<view class="shopping-list-item-use-skin center" v-if="item.useFlag==='0'&&item.buyFlag==='1'" @click="()=>useSkin(item.carrierId,item.skinId)"
					style="background: #FFFFFF;color: #FFFFFF;border: 1rpx solid #FF7A4C;color: #FF7A4C;">
					使用
				</view>
			</view>
		</view> -->
		<uni-popup ref="exchangePop" type="center" :mask-click="false">
			<image :src="`/static/shopping/shop-pop-animate.gif?${new Date().getTime()}`" class="pop-ani" mode="widthFix"></image>
			<view class="pop-ani-img">
				<image :src="`${pdfUrl}${state.clickVakue.skinUrl}`" class="pop-ani-img-value" mode="heightFix"></image>
				<view class="pop-ani-img-text">
					{{state.clickVakue.zodiacName||state.clickVakue.skinName}}
				</view>
				<view class="pop-ani-img-btn">
					<view class="pop-ani-img-btn-left center" @click="cancel">
						取消
					</view>
					<view class="pop-ani-img-btn-right center" @click="next">
						下一步
					</view>
				</view>
			</view>
		</uni-popup>
		<uni-popup ref="nextPop" type="center" :animation="false" :mask-click="false">
			<view class="nextPop">
				<text class="nextPop-name">提示</text>
				<view class="nextPop-text">
					点击确定后，召唤成功的生肖皮肤
					将<text style="color: #FF0000;">无法更改。</text>
				</view>
				<view class="nextPop-btn">
					<view class="nextPop-btn-left center" @click="cancel">
						取消
					</view>
					<view class="nextPop-btn-right center" @click="go">
						确定
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		navigateBack,
		navigateTo,
		showToast,
		redirectTo
	} from '@/common/uniTool.js'
	import {
		getTraineeCarrier,
		getTraineeCarrierSkin,
		changeBuySkin,
		changeUseSkin,
		changebuyCarrier,
		changeUseCarrier,
		getqryAllDefaultSkinList,
		getqryCarrierAllSkinList,
		summonCarrierSkin
	} from '../../service/shopping';
	import {
		onMounted,
		reactive,
		onUnmounted,
		ref
	} from "vue";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		pdfUrl
	} from '../../common/global';

	const props = defineProps(['level', 'summonCardId', 'carrierId'])
	const innerAudioContext = ref(null)
	const loginStore = useLoginStore() //用户参数
	const exchangePop = ref(null)
	const nextPop = ref(null)
	const state = reactive({
		shopList: [], //动物列表
		skinList: [], //皮肤列表
		type: 1, //1动物 2皮肤
		show: true,
		clickVakue: null
	})
	onMounted(() => {
		innerAudioContext.value = uni.createInnerAudioContext()
		// #ifdef APP-PLUS
		uni.showLoading({
			title: "正在进入脑电反训练系统..."
		})
		//#endif
		getCarList()
	})
	// onUnmounted(() => {
	// 	// #ifdef APP-PLUS
	// 	plus.screen.lockOrientation('portrait-primary');
	// 	// #endif
	// })
	const cancel = () => {
		exchangePop.value.close()
		nextPop.value.close()
	}
	const exchange = (item) => {
		if (item.state === '0') {
			showToast('敬请期待~')
			return
		}
		exchangePop.value.open()
		innerAudioContext.value.src = '/static/audio/summoningCard.MP3'
		innerAudioContext.value.play()
		state.clickVakue = item
		// changebuyCarrier({
		// 	traineeId: loginStore.qryActiveTrainee,
		// 	carrierId: item.carrierId
		// }).then(res => {
		// 	loginStore.getPoints(loginStore.qryActiveTrainee)
		// 	showToast(res.data)
		// 	getCarList()
		// }).catch(err => {
		// 	showToast(err.desc)
		// })
	}
	const go = () => {
		summonCarrierSkin({
			traineeId: loginStore.qryActiveTrainee,
			carrierId: state.clickVakue.carrierId,
			skinId: state.clickVakue.skinId,
			level: props.level,
			summonCardId: props.summonCardId
		}).then(res => {
			if (!props.level) {
				redirectTo('/pages/game/course')
			} else {
				nextPop.value.close()
				redirectTo('/pages/game/growthPath')
			}
			// loginStore.getPoints(loginStore.qryActiveTrainee)
			// showToast(res.data)
		}).catch(err => {
			showToast(err.desc)
		})
	}
	const next = () => {
		exchangePop.value.close()
		nextPop.value.open()
	}
	// const useSkin = (carrierId, skinId) => {
	// 	changeUseSkin({
	// 		traineeId: loginStore.qryActiveTrainee,
	// 		carrierId,
	// 		skinId
	// 	}).then(res => {
	// 		changeSkin(carrierId)
	// 		showToast(res.data)
	// 	}).catch(err => {
	// 		showToast(err.desc)
	// 	})
	// }
	// const changeCarrier = (carrierId) => {
	// 	changeUseCarrier({
	// 		traineeId: loginStore.qryActiveTrainee,
	// 		carrierId
	// 	}).then(res => {
	// 		showToast(res.data)
	// 		getCarList()
	// 	})
	// }
	// const exchangeSkin = (carrierId, skinId) => {
	// 	changeBuySkin({
	// 		traineeId: loginStore.qryActiveTrainee,
	// 		carrierId,
	// 		skinId
	// 	}).then(res => {
	// 		changeSkin(carrierId)
	// 		loginStore.getPoints(loginStore.qryActiveTrainee)
	// 		showToast(res.data)
	// 	}).catch(err => {
	// 		showToast(err.desc)
	// 	})
	// }

	// const changeSkin = (carrierId) => {
	// 	getTraineeCarrierSkin({
	// 		traineeId: loginStore.qryActiveTrainee,
	// 		carrierId
	// 	}).then(res => {
	// 		state.type = 2
	// 		state.skinList = res.data
	// 	})
	// }
	const getCarList = () => {
		if (props.level % 2 === 0) {
			getqryCarrierAllSkinList({
				traineeId: loginStore.qryActiveTrainee,
				carrierId: props.carrierId
			}).then(res => {
				state.shopList = res.data
				uni.hideLoading();
			})
		} else {
			getqryAllDefaultSkinList({
				traineeId: loginStore.qryActiveTrainee,
			}).then(res => {
				state.shopList = res.data
				plus.screen.unlockOrientation();
				plus.screen.lockOrientation('landscape-primary');
				uni.hideLoading();
				state.show = true
			}).catch(err => {
				showToast(err.desc)
			})
		}

	}
	const goBack = () => {
		if (state.type == 2) {
			state.type = 1
		} else {
			navigateBack()
		}

	}
</script>

<style lang="scss">
	.shopping {
		width: 100vw;
		height: 100vh;
		box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100vw 100vh;
		position: relative;


		&-list {
			height: 100vh;
			margin: 0 auto;
			width: 84vw;
			padding-top: 110rpx;

			&-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: wrap;
			}

			&-item {
				width: 188rpx;
				height: 240rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 0;
				background: #FFFFFF;
				box-shadow: 0rpx 3rpx 5rpx 0rpx rgba(232, 152, 64, 0.5);
				border-radius: 15rpx;
				border: 3rpx solid #FE865C;
				margin-bottom: 15rpx;

				&-buyno {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-btn {
						width: 119rpx;
						height: 30rpx;
						background: #FF7A4C;
						border-radius: 5rpx;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #FFFFFF;
					}

					&-value {
						display: flex;
						align-items: center;
						margin-bottom: 18rpx;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: bold;
						color: #111111;

						&-icon {
							width: 25rpx;
							margin-right: 13rpx;
						}
					}
				}

				&-name {
					font-size: 20rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
				}

				&-img {
					width: 89rpx;
					height: 104rpx;
				}

				&-buy {
					width: 119rpx;
					height: 30rpx;
					background: #FFEFC7;
					border-radius: 5rpx;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FF7A4C;
				}

				&-use {
					width: 119rpx;
					height: 30rpx;
					border-radius: 5rpx;
					border: 1rpx solid #B26034;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #B26034;

					&-skin {
						width: 119rpx;
						height: 30rpx;
						background: #FBDAAA;
						border-radius: 5rpx;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: bold;
						color: #111111;
						border: none
					}
				}

				&-btn {
					width: 119rpx;
					height: 30rpx;
					background: #B26034;
					border-radius: 5rpx;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #FFFFFF;
				}
			}
		}

		&-back {
			width: 50rpx;
			position: absolute;
			top: 15rpx;
			right: 15rpx;
		}

		&-money {
			position: absolute;
			right: 105rpx;
			top: 44rpx;
			width: 107rpx;
			height: 30rpx;
			background: #FDD9A8;
			border-radius: 5rpx;
			font-size: 20rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #B26034;
			display: flex;
			align-items: center;
			padding-left: 25rpx;

			&-icon {
				position: absolute;
				left: -15rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 33rpx;
			}
		}
	}

	.pop-ani {
		width: 100vw;

		&-img {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			top: 35%;
			animation: fadein 3s;
			display: flex;
			flex-direction: column;
			align-items: center;

			&-btn {
				margin-top: 30rpx;
				display: flex;
				align-items: center;

				&-left {
					width: 133rpx;
					height: 43rpx;
					background: #FF7A4C;
					box-shadow: inset 0rpx 0 1rpx 0rpx #FFED80;
					border-radius: 10rpx;
					border: 1rpx solid #DF9C51;
					font-weight: bold;
					font-size: 20rpx;
					color: #FFFFFF;
					margin-right: 24rpx;
				}

				&-right {
					width: 133rpx;
					height: 43rpx;
					background: #FDDC55;
					box-shadow: inset 0rpx 0 1rpx 0rpx #FFED80;
					border-radius: 10rpx;
					border: 1rpx solid #DF9C51;
					font-weight: 500;
					font-size: 20rpx;
					color: #AF6317;
				}
			}

			&-value {
				height: 130rpx;
			}

			&-text {
				text-align: center;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 500;
				font-size: 20rpx;
				color: #AF6317;
				margin-top: 10rpx;
			}
		}
	}

	.nextPop {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		align-items: center;
		width: 450rpx;
		height: 271rpx;
		background: url('~@/static/game/course-pop-img.png');
		background-repeat: no-repeat;
		background-size: 450rpx 271rpx;
		border-radius: 25rpx;
		padding: 14rpx;

		&-name {
			font-family: PingFangSC, PingFang SC;
			font-weight: bolder;
			font-size: 30rpx;
			color: #A9582E;
		}

		&-text {
			width: 375rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 25rpx;
			color: #111111;
			text-align: center;
		}

		&-btn {
			width: 80%;
			display: flex;
			align-items: center;
			justify-content: space-around;

			&-left {
				width: 110rpx;
				height: 40rpx;
				border-radius: 20rpx;
				border: 1rpx solid #FF943B;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 400;
				font-size: 20rpx;
				color: #FF943B;
			}

			&-right {
				width: 110rpx;
				height: 40rpx;
				background: #FF943B;
				border-radius: 20rpx;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: bold;
				font-size: 20rpx;
				color: #FFFFFF;
			}
		}
	}

	/* 进入动画 */
	@keyframes fadein {
		0% {
			opacity: 0;
		}

		50% {
			opacity: 0.1;
		}

		100% {
			opacity: 1;
		}
	}
</style>