<template>
	<view class="introduce">
		<video :loop="false" v-if="videoMusic" style="position: fixed;top: -100vh;left: -200vw;" id="myVideo" src="../../static/audio/ppvt/intro.mp3" :autoplay="true" />
		<image src="../../static/ppvt/introduce.png" mode="widthFix" class="introduce-img"></image>
		<view class="introduce-radio center" @click="agagin"><text class="introduce-radio-icon iconfont">&#xe6b3;</text>朗读</view>
		<view class="introduce-btn">
			<view class="introduce-btn-left center" @click="navigateBack()">
				取消
			</view>
			<view class="introduce-btn-center center" @click="goTest">
				进入演示题练习
			</view>
			<view class="introduce-btn-right center" @click="go">
				跳过演示，正式测评
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		navigateTo,
		navigateBack
	} from '../../common/uniTool';
	import {
		onHide,
		onShow
	} from '@dcloudio/uni-app'
	const videoMusic = ref(true)
	onShow(() => {
		// #ifdef APP-PLUS
		setTimeout(() => {
			plus.navigator.setFullscreen(false);
			plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
			plus.screen.lockOrientation('landscape-primary'); //锁死屏幕方向为竖屏
		}, 200)
		// #endif
	})
	onMounted(() => {
		videoMusic.value = true
	})
	onHide(() => {
		videoMusic.value = false
	})
	const goTest = () => {
		navigateTo('/pages/ppvt/question/test')
	}
	const go = () => {
		navigateTo('/pages/ppvt/question/index')
	}
	onUnmounted(() => {
		videoMusic.value = false
		console.log('卸载');
		// #ifdef APP-PLUS
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('portrait');
		// #endif
	})
	const agagin = () => {
		videoMusic.value = false
		videoMusic.value = true
	}
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		position: relative;

		&-img {
			width: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
		}

		&-radio {
			width: 100rpx;
			height: 30rpx;
			border-radius: 5rpx;
			border: 1rpx solid #111111;
			font-size: 20rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			position: absolute;
			right: 25rpx;
			top: 25rpx;

			&-icon {
				margin-right: 16rpx;
			}
		}

		&-btn {
			display: flex;
			align-items: center;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: 68rpx;
			width: 90%;
			justify-content: space-around;

			&-left {
				width: 113rpx;
				height: 55rpx;
				border-radius: 28rpx;
				border: 1rpx solid #41A8DE;
				font-size: 25rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #41A8DE;
			}

			&-center {
				width: 263rpx;
				height: 55rpx;
				background: #41A8DE;
				border-radius: 28rpx;
				font-size: 25rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
			}

			&-right {
				width: 263rpx;
				height: 55rpx;
				border-radius: 28rpx;
				border: 1rpx solid #41A8DE;
				font-size: 25rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #41A8DE;
			}
		}
	}
</style>