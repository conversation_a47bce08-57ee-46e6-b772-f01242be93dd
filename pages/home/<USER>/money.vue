<template>
	<view class="money">
		<view class="money-top">
			<view class="money-top-left">
				<image src="../../../static/shopping/money-icon.png" mode="widthFix" class="money-top-left-img"></image>
				{{loginStore.qryPoints}}
				<text class="money-top-left-text">个</text>
			</view>
			<view class="money-top-right">
				<text class="money-top-right-title">脑力值规则： \n</text>
				1、每天训练满30分钟，即可获得30个脑力值；<br />
				2、未满30分钟，无法获得所累积的脑力值，不计入脑力值账户总额；<br />
				3、每天最多可获得30个脑力值，训练大于30分钟仍为30个脑力值。
			</view>
		</view>
		<view class="money-box">
			<view v-for="(item,index) in state.recordsList" :key="index" class="money-box-item ">
				<view class="iconfont money-box-item-icon center">
					&#xe76b;
				</view>
				<view class="money-box-item-strip">

				</view>
				<view class="money-box-item-value" :class="index % 2==0?'money-box-item-value-left':'money-box-item-value-right'">
					<view class="money-box-item-value-title">
						{{item.createDate}}
					</view>
					训练{{item.consumTime}} <br />
					<view class="money-box-item-value-num">获得{{item.points}}个脑力值<image src="../../../static/shopping/money-icon.png" mode="widthFix" class="money-box-item-value-num-img"></image>
					</view>
				</view>
				<view class="money-box-item-first center iconfont" v-show="index==state.recordsList.length-1">
					&#xe850;
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		getqryPointsRecordsList
	} from '../../../service/shopping';
	import {
		onMounted,
		reactive
	} from "vue";
	import {
		useLoginStore
	} from "../../../stores/login";
	const loginStore = useLoginStore()
	const state = reactive({
		recordsList: []
	})
	onMounted(() => {
		getqryPointsRecordsList({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.recordsList = res.data
			console.log(res);
		})
	})
</script>

<style lang="scss">
	.money {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;
		padding: 32rpx 17rpx 0 32rpx;
		display: flex;
		flex-direction: column;

		&-box {
			width: 100%;
			background: #FFFFFF;
			border-radius: 32rpx 32rpx 0 0;
			flex: 1;
			margin-top: 48rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-top: 40rpx;
			overflow: hidden;
			overflow-y: auto;

			&-item {
				position: relative;
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;

				&-first {
					width: 80rpx;
					height: 80rpx;
					background: linear-gradient(180deg, #3DC9FF 0%, #1EA1FF 100%);
					font-size: 48rpx;
					color: #FFFFFF;
					border-radius: 50%;
					border: 6rpx solid #FFFFFF;
				}

				&-icon {
					width: 48rpx;
					height: 48rpx;
					background: linear-gradient(180deg, #3DC9FF 0%, #1EA1FF 100%);
					border: 6rpx solid #FFFFFF;
					color: #FFFFFF;
					border-radius: 50%;
					font-size: 20rpx;
				}

				&-value {
					position: absolute;
					width: 100%;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					line-height: 34rpx;

					&-num {
						display: flex;
						align-items: center;

						&-img {
							width: 32rpx;
							margin-left: 4rpx;
						}
					}


					&-left {
						left: 104rpx;
						top: 2rpx;
					}

					&-right {
						left: 404rpx;
						top: 2rpx;
					}

					&-title {
						font-size: 32rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #111111;
						line-height: 58rpx;
					}
				}

				&-strip {
					width: 16rpx;
					height: 184rpx;
					background: linear-gradient(180deg, #3DC9FF 0%, #1EA1FF 100%);
				}
			}
		}

		&-top {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-left {
				display: flex;
				align-items: center;
				font-size: 58rpx;
				font-family: DINAlternate-Bold, DINAlternate;
				font-weight: bold;
				color: #111111;

				&-img {
					width: 64rpx;
					height: 64rpx;
					margin-right: 16rpx;
				}

				&-text {
					font-size: 28rpx;
					margin-left: 8rpx;
				}
			}

			&-right {
				font-size: 12rpx;
				font-family: SourceHanSansCN-Normal, SourceHanSansCN;
				font-weight: 400;
				color: #111111;
				line-height: 20rpx;

				&-title {
					font-size: 16rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
					line-height: 36rpx;
				}
			}
		}
	}
</style>