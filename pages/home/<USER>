<template>
	<view style="padding-top: 30rpx">
		<uni-list-item style="height: 52rpx;padding: 0 50rpx">
			<template v-slot:body>
				<view class="home-version center">
					关于版本——版本名称：ADHD {{state.channel}}
				</view>
			</template>
		</uni-list-item>
		<uni-list>
			<uni-list-item style="height: 52rpx;padding: 0 50rpx;">
				<template v-slot:body>
					<view class="home-version center" @click="updataVersion">
						<uni-badge :is-dot="true" :text="state.versionCode < loginStore.appVersion.version?'1':''" absolute="rightTop" size="small">
							版本号：{{state.versionCode.toString().substring(0,1)}}.{{state.versionCode.toString().substring(1,2)}}.{{state.versionCode.toString().substring(2,3)}}
						</uni-badge>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
		<uni-list>
			<uni-list-item style="height: 52rpx;padding: 0 50rpx;">
				<template v-slot:body>
					<view class="home-version center" @click="goIcp">
						ICP备案编号：{{loginStore.appVersion.icp}}
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</view>

</template>

<script setup>
	import {
		reactive,
		onMounted
	} from "vue";
	import {
		navigateTo
	} from "../../common/uniTool";
	import {
		useLoginStore
	} from "../../stores/login";
	const loginStore = useLoginStore()
	const state = reactive({
		versionCode: '',
		channel: '',
	})
	onMounted(() => {
		// #ifdef APP-PLUS
		state.channel = plus.runtime.channel
		plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
			console.log(wgtinfo);
			state.versionCode = wgtinfo.versionCode
		});
		// #endif
	})
	const updataVersion = () => {
		loginStore.getAppVersionData('update')
	}
	const goIcp = () => {
		navigateTo('/pages/webview/url?url=https://beian.miit.gov.cn')

	}
</script>

<style>
</style>