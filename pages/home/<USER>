<template>
	<view class="home">
		<view class="home-info">
			<view class="home-info-top">
				<image class="home-info-top-img" src="/static/parent-boy.png" mode="widthFix"></image>
				<view class="home-info-top-right">
					<view class="home-info-top-right-name">
						{{loginStore.userInfo.username}}
					</view>
					<view class="home-info-top-right-phone">
						手机号 {{loginStore.userInfo.mobile}}
					</view>
				</view>
				<!-- 	<view class="home-info-top-money">
					<view class="home-info-top-money-left">
						<text class="home-info-top-money-left-top">{{loginStore.qryPoints}}</text>
						<view class="home-info-top-money-left-bottom">
							脑力值(个) <text class="iconfont">&#xe618;</text>
						</view>
					</view>
					<image src="/static/home.png" class="home-info-top-money-right" mode="widthFix"></image>
				</view> -->
			</view>
			<view class="home-info-bottom">
				<view class="home-info-bottom-tips">
					<view class="home-info-bottom-tips-left">
						我的亲属（{{loginStore.visitorList.length}}个）
					</view>
					<view class="home-info-bottom-tips-right" @click="navigateTo('/pages/home/<USER>/index')">
						全部
						<text class="home-info-bottom-tips-right-icon iconfont">&#xe60d;</text>
					</view>
				</view>
				<view class="home-info-bottom-people">
					<view v-for="(item,index) in loginStore.visitorList" :key="item.idnum">
						<view class="home-info-bottom-people-item" v-if="index<5"
							:style="item.sex===2&&state.activeStyle">
							<view>
								<text class="iconfont" v-if="item.sex===1">&#xe6db;</text> <text class="iconfont"
									v-else>&#xe8b3;</text>{{item.traineeName.length>=4?item.traineeName.slice(0,4):item.traineeName}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="home-record" v-if="loginStore.recordList&&loginStore.recordList.length>0">
			<view class="home-record-top">
				<view class="home-record-top-left">
					评估记录
				</view>
				<view class="home-record-top-right" @click="()=>golist(1)">
					查看更多 <text class="home-info-bottom-tips-right-icon iconfont">&#xe60d;</text>
				</view>
			</view>
			<recordBoxVue :data="loginStore.recordList[0]" />
		</view>
		<view class="home-record" v-if="loginStore.prescriptionList&&loginStore.prescriptionList.length>0">
			<view class="home-record-top">
				<view class="home-record-top-left">
					训练方案
				</view>
				<view class="home-record-top-right" @click="()=>golist(2)">
					查看更多 <text class="home-info-bottom-tips-right-icon iconfont">&#xe60d;</text>
				</view>
			</view>
			<view class="home-record-bottom">
				<view class="home-record-bottom-top">
					<image class="home-record-bottom-top-img" src="/static/prescription-img.png" mode="widthFix">
					</image>
					<view class="home-record-bottom-top-content">
						<view class="home-record-bottom-top-content-title">
							{{loginStore.prescriptionList[0].categoryName}}
						</view>
						<view class="home-record-bottom-top-content-info">
							<span
								style="margin-right: 16rpx;">{{loginStore.prescriptionList[0].traineeInfo.traineeName}}</span>
							<span
								style="margin-right: 16rpx;">{{loginStore.prescriptionList[0].traineeInfo.sex===1?'男':'女'}}</span>
							<span>{{loginStore.prescriptionList[0].traineeInfo.birth}}</span>
						</view>
						<view class="home-record-bottom-top-content-hospital">
							{{loginStore.prescriptionList[0].traineeInfo.traineeHospitalName}}
						</view>
					</view>
				</view>
				<view class="home-record-bottom-btn">
					<view class="home-record-bottom-btn-time">
						方案时间：{{loginStore.prescriptionList[0].beginDate}}
					</view>
					<view class="home-record-bottom-btn-value center"
						@click="openPdf(loginStore.prescriptionList[0].prescriptionId)">
						查看方案
					</view>
				</view>
			</view>
		</view>
		<view class="home-more">
			<view class="home-more-title">
				其他服务
			</view>
			<view class="home-more-box">
				<view class="home-more-box-item center" v-for="(item,index) in state.project" :key="item.name"
					@click="()=>goUrl(item)">
					<text class="home-more-box-item-icon iconfont">{{item.icon}}</text>
					<text class="home-more-box-item-text">{{item.name}}</text>
				</view>
			</view>
		</view>
		<uni-list-item style="height: 62rpx;margin-top:16rpx ;padding: 0 50rpx;">
			<template v-slot:header>
				<view class="home-version center">
					关于版本
				</view>
			</template>
			<template v-slot:footer>
				<view class="home-version center" @click="updataVersion">
					<uni-badge :is-dot="true" :text="state.versionCode < loginStore.appVersion.version?'1':''"
						absolute="rightTop" size="small">
						版本{{state.versionCode.toString().substring(0,1)}}.{{state.versionCode.toString().substring(1,2)}}.{{state.versionCode.toString().substring(2,3)}}
					</uni-badge>
				</view>
			</template>
		</uni-list-item>
		<view class="home-out center" @click="outLogin">
			退出登入(v{{state.versionCode.toString().substring(0,1)}}.{{state.versionCode.toString().substring(1,2)}}.{{state.versionCode.toString().substring(2,3)}})
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive
	} from "vue";
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		reportUrl,
		pdfUrl
	} from "@/common/global";
	import {
		clearStorage,
		reLaunch,
		getStorageSync,
		showLoading,
		hideLoading,
		navigateTo,
		clearStorageSync
	} from "@/common/uniTool";
	import recordBoxVue from "@/components/recordBox.vue";
	import {
		getQryTraineePrescripPdf,
		updateQryActiveTrainee
	} from "@/service";
	import {
		qryFreeEvaResultPDF
	} from "@/service/scale";
	import {
		useLoginStore
	} from "@/stores/login";

	const loginStore = useLoginStore()
	const state = reactive({
		versionCode: '',
		activeStyle: {
			color: '#FF4747',
			background: '#FFF0F0',
			border: '1rpx solid #FF2020'
		},
		project: [{
				name: '用户协议',
				icon: '\ue657',
				url: 'http://**************:9900/resources/quickAieve/html/aura.htm'
			},
			{
				name: '儿童信息保护规则',
				icon: '\ue60b',
				url: 'http://**************:9900/resources/quickAieve/html/cpipa.htm'
			},
			{
				name: '隐私协议',
				icon: '\ue75e',
				url: 'http://**************:9900/resources/quickAieve/html/privacy.html'
			},
		],
	})
	const goUrl = (value) => {
		navigateTo(`/pages/webview/url?url=${value.url}`)
	}

	const updataVersion = () => {
		navigateTo('/pages/home/<USER>')
	}

	const golist = (type) => {
		if (type === 1) {
			navigateTo('/pages/home/<USER>/index')
		} else {
			navigateTo('/pages/home/<USER>/prescription')
		}
	}
	const openPdf = (prescriptionId) => {
		getQryTraineePrescripPdf({
			prescriptionId
		}).then(res => {
			showLoading('打开方案中...')
			uni.downloadFile({
				url: `${pdfUrl}resources/quickAieve/pdf/${res.data.fileName}`,
				success: function(res) {
					var filePath = res.tempFilePath;
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function(res) {
							hideLoading()
							console.log('打开文档成功');
						}
					});
				}
			});
		}).catch(err => {
			console.log(err, '-----------err');
		})

	}
	onShow(() => {
		loginStore.getRecordList()
		loginStore.getTraineePrescripList(null)
		// #ifdef APP-PLUS
		plus.runtime.getProperty(plus.runtime.appid, function(wgtinfo) {
			state.versionCode = wgtinfo.versionCode
		});
		// #endif
	})

	const outLogin = () => {
		uni.showModal({
			title: '',
			content: '确定退出登录吗',
			showCancel: true,
			cancelText: '否',
			confirmText: '是',
			success: res => {
				if (res.confirm) {
					clearStorageSync();
					reLaunch('/pages/login/index')
				}
			},
			fail: () => {},
			complete: () => {}
		});

	}
</script>

<style lang="scss">
	.home {
		width: 100vw;
		flex: 1;
		background: #F6F6F6;
		padding-bottom: 32rpx;

		&-version {
			height: 40px;
			font-size: 20rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
		}

		&-out {
			width: 100%;
			background: #FFFFFF;
			margin-top: 18rpx;
			padding: 18rpx 0;
			font-size: 22rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #111111;
		}

		&-more {
			background: #FFFFFF;
			padding: 36rpx 68rpx;
			margin-top: 18rpx;

			&-title {
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
				margin-bottom: 36rpx;
			}

			&-box {
				display: flex;
				margin-top: 36rpx;

				&-item {
					display: flex;
					flex-direction: column;
					margin-right: 80rpx;

					&-icon {
						font-size: 50rpx;
						margin-bottom: 8rpx;
					}

					&-text {
						font-size: 18rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
					}
				}


			}

		}

		&-record {
			width: 100%;
			background: #FFFFFF;
			padding: 36rpx 68rpx;
			margin-top: 18rpx;

			&-bottom {
				display: flex;
				flex-direction: column;
				margin-top: 28rpx;

				&-btn {
					display: flex;
					justify-content: space-between;
					margin-top: 16rpx;
					align-items: center;

					&-value {
						width: 137rpx;
						height: 47rpx;
						background: #287FFF;
						border-radius: 7rpx;
						font-size: 18rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #FFFFFF;

						&-go {
							border-radius: 7rpx;
							width: 137rpx;
							height: 47rpx;
							font-size: 20rpx;
							font-family: PingFangSC-Medium, PingFang SC;
							font-weight: 500;
							color: #287FFF;
							border: 2rpx solid #287FFF;
						}
					}

					&-time {
						font-size: 22rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #999999;
					}
				}

				&-top {
					display: flex;
					align-items: center;

					&-img {
						width: 180rpx;
					}

					&-content {
						height: 135rpx;
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-around;
						margin-left: 21rpx;

						&-info {
							font-size: 25rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
						}

						&-hospital {
							font-size: 22rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
						}

						&-title {
							font-size: 25rpx;
							font-family: PingFangSC-Medium, PingFang SC;
							font-weight: bold;
							color: #111111;


						}
					}
				}
			}

			&-top {
				display: flex;
				align-items: center;
				border-bottom: 1rpx solid #DCDCDC;
				padding-bottom: 28rpx;
				justify-content: space-between;

				&-left {
					font-size: 22rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #111111;
				}

				&-right {
					display: flex;
					align-items: center;
					font-size: 18rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #717171
				}
			}
		}

		&-info {
			width: 100%;
			background: linear-gradient(193deg, #E8F1FF 0%, #FFFFFF 100%);
			padding: 90rpx 68rpx 36rpx 68rpx;

			&-bottom {
				display: flex;
				flex-direction: column;

				&-people {
					display: flex;
					align-items: center;

					&-item {
						background: #F0F6FF;
						border-radius: 20rpx;
						border: 1rpx solid #207BFF;
						font-size: 18rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #207BFF;
						padding: 8rpx 12rpx;
						margin-right: 14rpx;
					}
				}

				&-tips {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 34rpx;


					&-left {
						font-size: 22rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;
					}

					&-right {
						font-size: 18rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #717171;
					}
				}
			}

			&-top {
				display: flex;
				align-items: center;
				margin-bottom: 52rpx;

				&-img {
					width: 112rpx;
					height: 112rpx;
				}

				&-money {
					background: linear-gradient(317deg, #FAF3ED 0%, #FDF9F6 100%);
					border-radius: 8rpx;
					border: 2rpx solid #F9EEE6;
					display: flex;
					align-items: flex-start;
					padding: 13rpx 16rpx 16rpx 16rpx;

					&-left {
						display: flex;
						flex-direction: column;
						margin-right: 20rpx;

						&-top {
							font-size: 24rpx;
							font-family: SourceHanSansCN-Bold, SourceHanSansCN;
							font-weight: bold;
							color: #6E3E26;
							margin-bottom: 8rpx;
						}

						&-bottom {
							font-size: 16rpx;
							font-family: SourceHanSansCN-Normal, SourceHanSansCN;
							font-weight: 400;
							color: #602A13;
						}
					}

					&-right {
						width: 36rpx;
						height: 42rpx;
					}
				}

				&-right {
					display: flex;
					flex-direction: column;
					margin-left: 18rpx;
					justify-content: space-around;
					height: 100%;
					flex: 1 1 auto;

					&-name {
						font-size: 25rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;
						margin-bottom: 26rpx;
					}

					&-phone {
						font-size: 18rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #666666;
					}
				}
			}
		}
	}
</style>