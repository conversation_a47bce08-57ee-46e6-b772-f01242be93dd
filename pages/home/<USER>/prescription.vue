<template>
	<view class="record" :style="{overflow:state.activeStyle.overhiden,height:loginStore.prescriptionList.length<5&&state.activeStyle.height}">
		<view class="record-top">
			<view class="record-top-left">
				{{state.userInfo&&state.userInfo[state.useIndex].traineeName.substring(0,10)}} <text
					style="margin: 0 20rpx;">{{state.userInfo&&state.userInfo[state.useIndex].sex?state.userInfo[state.useIndex].sex===1?'男':'女':''}}</text>
				{{state.userInfo&&state.userInfo[state.useIndex].age}}
			</view>
			<view class="record-top-right center" @click="changeShowBox">
				切换亲属 <text class="iconfont">&#xe771;</text>
			</view>
		</view>
		<view class="record-bottom" v-if="loginStore.prescriptionList.length>0">
			<view class="record-bottom-box" v-for="(item,index) in loginStore.prescriptionList" :key="item.title">
				<view class="record-bottom-box-top">
					<image class="record-bottom-box-top-img" src="../../../static/prescription-img.png" mode="widthFix"></image>
					<view class="record-bottom-box-top-content">
						<view class="record-bottom-box-top-content-title">
							{{item.categoryName}}
						</view>
						<view class="record-bottom-box-top-content-info">
							<span style="margin-right: 16rpx;">{{item.traineeInfo.traineeName}}</span>
							<span style="margin-right: 16rpx;">{{item.traineeInfo.sex===1?'男':'女'}}</span>
							<span>{{item.traineeInfo.birth}}</span>
						</view>
						<view class="record-bottom-box-top-content-hospital">
							{{item.traineeInfo.traineeHospitalName}}
						</view>
					</view>
				</view>
				<view class="record-bottom-box-btn">
					<view class="record-bottom-box-btn-time">
						生成时间：{{item.beginDate}}
					</view>
					<view class="record-bottom-box-btn-value center" @click="openPdf(item.prescriptionId)">
						查看方案
					</view>
				</view>
			</view>
		</view>
		<view class="record-noList center" v-else>
			<image class="record-noList-img" src="../../../static/no-record-p.png" mode="widthFix"></image>
		</view>
		<view class="record-mask" v-if="state.showBox" @click="changeShowBox">
			<view class="record-mask-box">
				<view class="record-mask-box-item" v-for="(item,index) in state.userInfo" :key="item.traineeId" @click.stop="()=>changeInfo(item,index)">
					{{item.traineeName.substring(0,10)}}<text v-if="index>0" style="margin-left: 8rpx;">{{item.sex===1?'男':'女'}}</text><text v-if="index>0"
						style="margin-left: 8rpx;">{{item.age}}</text>
				</view>
			</view>
		</view>
	</view>

</template>
<script setup>
	import {
		watch,
		reactive,
		onMounted,
		onUnmounted
	} from "vue";
	import {
		getQryTraineePrescripPdf
	} from "../../../service";
	setNavigationBarTitle()
	import {
		reportUrl,
		pdfUrl
	} from "../../../common/global";
	import {
		getStorageSync,
		navigateTo,
		setNavigationBarTitle,
		showLoading,
		hideLoading
	} from "../../../common/uniTool";
	import {
		useLoginStore
	} from "../../../stores/login";
	const loginStore = useLoginStore()

	onMounted(() => {
		const info = [...loginStore.visitorList]
		info.unshift({
			traineeName: '全部亲属',
			traineeId: null
		})
		state.userInfo = info
	})
	const state = reactive({
		showBox: false,
		userInfo: '',
		useIndex: 0,
		activeStyle: {
			overhiden: '',
			height: '100vh'
		},
	})
	const openPdf = (prescriptionId) => {
		getQryTraineePrescripPdf({
			prescriptionId
		}).then(res => {
			showLoading('打开方案中...')
			uni.downloadFile({
				url: `${pdfUrl}resources/quickAieve/pdf/${res.data.fileName}`,
				success: function (res) {
					var filePath = res.tempFilePath;
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function (res) {
							hideLoading()
							console.log('打开文档成功');
						}
					});
				}
			});
		}).catch(err => {
			console.log(err, '-----------err');
		})

	}

	watch(() => state.showBox, (showBox) => {
		if (showBox) {
			state.activeStyle.overhiden = 'hidden'
		} else {
			state.activeStyle.overhiden = ''
		}
	})
	const changeShowBox = () => {
		state.showBox = !state.showBox
	}
	console.log();
	const changeInfo = (e, index) => {
		loginStore.getTraineePrescripList(e.traineeId)
		state.useIndex = index
		changeShowBox()
	}
</script>
<style lang="scss">
	.record {
		width: 100vw;
		background: #F6F6F6;
		position: relative;
		padding-bottom: 20rpx;
		flex: 1;

		&-noList {
			padding-top: 288rpx;

			&-img {
				width: 188rpx;
			}
		}

		&-mask {
			width: 100%;
			height: 100%;
			position: absolute;
			z-index: 999;
			top: 76rpx;
			left: 0;


			&-box {
				position: absolute;
				right: 26rpx;
				background: #FFFFFF;
				box-shadow: -7rpx 7rpx 36rpx 0rpx rgba(0, 0, 0, 0.24);
				border-radius: 18rpx;
				filter: blur(0px);
				padding: 30rpx 26rpx;
				padding-top: 8rpx;
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
				display: flex;
				flex-direction: column;

				&-item {
					margin-top: 18rpx;
				}
			}
		}

		&-top {
			display: flex;
			align-items: center;
			padding-bottom: 28rpx;
			justify-content: space-between;
			background: #FFFFFF;
			padding: 24rpx 34rpx;

			&-left {
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
			}

			&-right {
				border-radius: 23rpx;
				border: 2rpx solid #287FFF;
				font-size: 22rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #287FFF;
				padding: 7rpx 15rpx;
			}
		}

		&-bottom {
			display: flex;
			flex-direction: column;
			padding: 0 32rpx;

			&-box {
				border-radius: 18rpx;
				padding: 18rpx 20rpx;
				background: #FFFFFF;
				margin-top: 18rpx;

				&-btn {
					display: flex;
					justify-content: space-between;
					margin-top: 16rpx;
					align-items: center;

					&-value {
						width: 137rpx;
						height: 47rpx;
						background: #287FFF;
						border-radius: 7rpx;
						font-size: 18rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #FFFFFF;
					}

					&-time {
						font-size: 22rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #999999;
					}
				}

				&-top {
					display: flex;
					align-items: center;

					&-img {
						width: 180rpx;
					}

					&-content {
						height: 135rpx;
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-around;
						margin-left: 21rpx;

						&-info {
							font-size: 25rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
						}

						&-hospital {
							font-size: 22rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
						}

						&-title {
							font-size: 25rpx;
							font-family: PingFangSC-Medium, PingFang SC;
							font-weight: bold;
							color: #111111;


						}
					}
				}
			}

		}


	}
</style>