<template>
	<view class="patient">
		<view class="patient-top">
			<uni-swipe-action>
				<uni-swipe-action-item @click="clickItem" :show="state.show" :autoClose="true"
					v-for="(item,index) in loginStore.visitorList" :key="item.idnum"
					@change="swipeChange($event, index)">
					<template v-slot:right v-if="!state.showAgain">
						<view class="patient-button">
							<view class="patient-button-edit " @click="change(index,item.traineeId)">
								<view class="iconfont" style="margin-bottom: 36rpx;">
									&#xe60c;
								</view>编辑
							</view>
							<view class="patient-button-delt " @click="change(index)">
								<view class="iconfont" style="margin-bottom: 36rpx;">
									&#xe60e;
								</view>
								删除
							</view>
						</view>

					</template>
					<template v-slot:right v-if="state.showAgain&&index===state.nowindex">
						<view class="patient-button">
							<view class="patient-button-again center" @click="detailInfo(item.traineeId)">删除亲属信息</view>
						</view>
					</template>

					<view class="patient-box" :style="index===state.isIndex&&state.noBorder" @click="noclick(index)">
						<view class="patient-box-left center">
							{{item.traineeName.length>=3?item.traineeName.charAt(0):item.traineeName}}
						</view>
						<view class="patient-box-right">
							<view class="patient-box-right-top">
								<view style="margin-right: 8rpx;margin-bottom: 8rpx;">
									{{item.traineeName.substring(0,10)}}</view>
								<text style="margin-right: 8rpx;">{{item.sex=='1'?'男':'女'}}</text>
								<text style="margin-right: 8rpx;">{{item.age}}</text>
								<text class="patient-box-right-top-icon center">{{getRela(item.relation)}}</text>
							</view>
							<view class="patient-box-right-bottom">
								身份证：{{item.idnum.length>20?rsaDecrypt(item.idnum):item.idnum}}
							</view>
						</view>
					</view>
				</uni-swipe-action-item>
			</uni-swipe-action>
		</view>

		<view class="patient-tips">
			<view class="patient-tips-title">
				温馨提示
			</view>
			<view class="patient-tips-value">
				1.亲属姓名、证件信息用户平台审核，不会外传。请输入正确的亲属姓名和证号。
				<view class="">
					2.根据相关政策，现亲属及监护人需进行实名认证，请您及时认证，以便使用平台的服务功能。
				</view>
			</view>
			<view class="patient-tips-btn center" @click="navigateTo('/pages/informationForm/index?type=1')">
				<view class="patient-tips-btn-text center"> <text class="iconfont">&#xe62c;</text>添加亲属</view>
			</view>
		</view>
	</view>
</template>


<script setup>
	import {
		reactive,
		watch,
		onMounted
	} from "vue";

	import {
		getRela
	} from "../../../common/method";
	import {
		navigateTo,
		showToast
	} from "../../../common/uniTool";
	import {
		deleteVisitorDetail
	} from "../../../service";
	import {
		useLoginStore
	} from "../../../stores/login";
	import {
		rsaDecrypt
	} from "../../../utils/encrypt";
	const loginStore = useLoginStore()
	onMounted(() => {
		loginStore.getVisitor()
	})
	const clickItem = () => {
		console.log('1');
	}
	const state = reactive({
		show: 'none',
		showAgain: false,
		nowindex: 0,
		isShow: '',
		noBorder: {},
		isIndex: '',
		activeStyle: {
			height: '100%'
		}
	})
	const noclick = (index) => {
		if (index !== state.nowindex) {
			again()
		}
	}
	const swipeChange = (e, index) => {
		state.isIndex = index
		state.isShow = e
	}
	const again = (traineeId) => {
		state.showAgain = false
		state.nowindex = 0
		state.show = 'none'
	}
	const detailInfo = (traineeId) => {
		deleteVisitorDetail({
			traineeId
		}).then(res => {
			again()
			loginStore.getVisitor()
			showToast('删除成功')
		}).catch(err => {
			again()
			showToast('删除失败')
		})
	}
	watch(() => state.isShow, (isShow) => {
		if (isShow === 'right') {
			state.noBorder = {
				borderRadius: 0
			}
		} else {
			state.noBorder = {
				borderRadius: '18rpx'
			}
		}
	})

	const change = (index, traineeId) => {
		if (traineeId) {
			again(traineeId)
			navigateTo(`/pages/informationForm/index?traineeId=${traineeId}&type=1`)
			return
		}
		state.showAgain = true
		state.nowindex = index
		state.show = 'right'

	}
</script>


<style lang="scss">
	.patient {
		background: #F6F6F6;
		padding: 33rpx 33rpx 350rpx 33rpx;
		width: 100vw;
		flex: 1;
		position: relative;
		display: flex;
		flex-direction: column;
		overflow-y: hidden;

		&-top {
			height: 66vh;
			overflow-y: scroll;

		}

		&-tips {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;

			&-title {
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #999999;
				margin-left: 33rpx;
			}

			&-value {
				font-size: 18rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 25rpx;
				margin-left: 33rpx;
				margin-top: 24rpx;
			}

			&-btn {
				display: flex;
				align-items: center;
				width: 100%;
				background: #FFFFFF;
				padding: 26rpx 0 38rpx 0;
				margin-top: 34rpx;

				&-text {
					width: 616rpx;
					height: 81rpx;
					background: linear-gradient(360deg, #217CFF 0%, #5EBAFF 100%);
					border-radius: 14rpx;
					font-size: 25rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
				}
			}
		}

		&-title {
			font-size: 25rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #111111;
			margin-bottom: 22rpx;
		}

		&-box {
			background: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 18rpx;
			border-radius: 18rpx;
			height: 163rpx;
			padding: 32rpx;

			&-right {
				flex: 1;
				margin-left: 22rpx;
				display: flex;
				height: 100%;
				flex-direction: column;
				justify-content: space-between;

				&-bottom {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #666666;
				}

				&-top {
					font-size: 29rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					display: flex;

					&-icon {
						background: #F2F8FF;
						border-radius: 7rpx;
						border: 1rpx solid #287FFF;
						font-size: 22rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #287FFF;
						width: 92rpx;
						height: 38rpx;
						margin-left: 32rpx;
					}
				}
			}

			&-left {
				width: 67rpx;
				height: 67rpx;
				background: linear-gradient(90deg, #DFEEFF 0%, #C4DFFF 100%);
				border-radius: 50%;
				font-size: 16rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #287FFF;
			}
		}

		&-button {
			display: flex;
			align-items: center;
			height: 163rpx;
			font-size: 18rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			position: relative;

			&-edit {
				height: 100%;
				background: #FFA42F;
				padding: 0 22rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}

			&-delt {
				height: 100%;
				background: #F94B4A;
				padding: 0 22rpx;
				border-radius: 0rpx 18rpx 18rpx 0rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}

			&-again {
				width: 162rpx;
				height: 100%;
				background: #F94B4A;
				padding: 0 22rpx;
				border-radius: 0rpx 18rpx 18rpx 0rpx;
				font-size: 18rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				line-height: 25rpx;
			}
		}
	}
</style>