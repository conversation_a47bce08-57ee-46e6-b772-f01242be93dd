<template>
	<view class="grun">
		<!-- <image class="grun-ani1" :src="`/static/game/game-debrisGifUrl-${gameResult.debrisNum}.gif`" v-if="showani===1"></image> -->
		<video :loop="true" v-if="videoMusic&&audioUrl" style="position: fixed;top: -100vh;left: -200vw;" id="myVideo" :src="`/static/audio/${audioUrl}`" :autoplay="true" />
		<view class="renderjs" :speed="speed" :change:speed="animate.receiveMsg" :targetImg="targetImg" :change:targetImg="animate.receiveTargetImgMsg" :level="level"
			:change:level="animate.receiveLevel" :courseLevel="courseLevel" :change:courseLevel="animate.receiveCourseLevel" />
		<GameObstacleVue :speed="speed" :jumpList="jumpList" v-if="level>1" @event="addAnswer" :targetImg="targetImg" :level="level" />
		<view class="grun-bg" :style="{backgroundImage:`url('/static/game/game-bg/bg_${jumpImg[gifIndex]&&jumpImg[gifIndex].split('/')[2].split('_')[0]}.png')`}">
			<GameInfoVue :total="total" :gameIsOver="gameIsOver" @gameOver="gameOver" :userInfo="userInfo" :speed="speed" :showCount="showCount" :level="level"
				:signalNormalSuccCount="signalNormalSuccCount" @changeMusic="changeMusic" />
			<view class="grun-oblist">
			</view>
		</view>
		<uni-popup ref="popup" :mask-click="false">
			<GameResultVue :gameResult="gameResult" :queTrainDetail="queTrainDetail" :userInfo="userInfo" :level="level" :courseLevel="courseLevel" />
		</uni-popup>
		<uni-popup ref="finishPopup" :mask-click="false" :animation="false" class="grun-finish">
			<!-- <image :src="gameResult.isGainDebris?'/static/game/game-finish-debris.png':'/static/game/game-finish.png'" class="grun-finish" mode="widthFix"></image> -->
			<image :src="gameResult.allSkinFlag==='0'?'/static/game/game-finish-debris.png':'/static/game/game-finish.png'" class="grun-finish" mode="widthFix"></image>
			<view class="grun-finish-box" v-if="gameResult.allSkinFlag==='0'">
				<image class="grun-finish-box-img" v-if="gameResult.gainSummonFlag==='1'" @click="openTreatment" src="/static/game/game-finish-box.gif" mode="widthFix"></image>
				<image class="grun-finish-box-img" v-else src="/static/game/game-finish-box.png" mode="widthFix"></image>
				<text class="grun-finish-box-text" :style="{color:gameResult.points===15?'#0089FF':''}">{{gameResult.points===15?'点击获取召唤卡':'召唤进度'}}</text>
				<view class="grun-finish-box-pre">
					<uv-line-progress style="width: 100rpx;" activeColor="#71DFFF" inactiveColor="#005274" :percentage="gameResult.points>=15?100:100/15*gameResult.points" height="10"
						:showText="false"></uv-line-progress>
				</view>
			</view>
			<!-- <image :src="`${pdfUrl}resources/${gameResult.debrisPngUrl}`" v-if="gameResult.isGainDebris" class="grun-ani2" mode="widthFix"></image> -->
			<view class="grun-finish-money">
				<view class="grun-finish-money-top" :class="gameResult.allSkinFlag==='0'?'grun-finish-money-top-full':''">
					<image class="grun-finish-money-top-right" src='/static/game/game-finish-icon.png' mode="widthFix"></image>
					<view class="grun-finish-money-top-box">
						<view class="grun-finish-money-top-box-num center">{{`已获得${gameResult.points}个脑力值`}}</view>
						<view class="grun-finish-money-top-box-text">
							训练越久，获得脑力值越多
						</view>
					</view>
					<!-- <view class="grun-finish-money-top-left">
						脑力值
					</view> -->
					<!-- <view class="grun-finish-money-top-center">
						<view class="grun-finish-money-top-center-text" :style="`left: ${(gameResult.points>=30?30:gameResult.points)*8}rpx;`">
							{{gameResult.points>=30?'已计入':'已累计'}}{{gameResult.points>=30?30:gameResult.points}}个
						</view>
						<view class="grun-finish-money-top-center-value" :style="`width: ${(gameResult.points>=30?30:gameResult.points)*9.9}rpx;`">

						</view>
					</view>
					<view class="grun-finish-money-top-center-num">
						{{gameResult.points>=30?30:gameResult.points}}
					</view> -->
				</view>
				<!-- 	<view class="grun-finish-money-center">
					{{`已获得${gameResult.points}个脑力值`}}
				</view>
					<view class="grun-finish-money-bottom">
					{{gameResult.isGainDebris?'获得碎片可在成长进阶进行合成～':gameResult.points>=30?'训练已满30分钟':'需要训练20-30分，请不要半途而废哦～'}}
				</view> -->
				<view class="grun-finish-money-box" v-if="gameResult.isTurnQuest">
					<view class="grun-finish-money-box-left center" @click="()=>popArithmetic('que')">
						前往奖励空间
					</view>
					<view class="grun-finish-money-box-right center" @click="()=>popArithmetic('finish')">
						查看训练小结
					</view>
				</view>
				<view class="grun-finish-money-btn center" @click="()=>popArithmetic('finish')" v-else>
					查看训练小结
				</view>
			</view>
		</uni-popup>
		<GameProcessVue :focus="focus" />
		<GameCountDownVue v-if="showCount" isStart="true" count="3" @show="show" />
		<view class="grun-wrap" :style="{backgroundImage:`url('/static/game/game-bg/ground_${jumpImg[gifIndex]&&jumpImg[gifIndex].split('/')[2].split('_')[0]}.png')`}">
			<GroundObstacles :courseLevel="courseLevel" :speed="speed" @gameOver="gameOver" v-if="courseLevel>1&&courseLevel<5" :isGroundRun="isGroundRun" />
			<image class="grun-wrap-animal" :src="`/static/game/carriergameskin/${jumpImg[gifIndex]&&jumpImg[gifIndex].split('/')[2]}`" mode="widthFix" v-show="!jump"></image>
			<view class="grun-wrap-jump" v-show="jump">
				<image :src="`${pdfUrl}resources/${jumpImg[0]}`" mode="widthFix" class="grun-wrap-jump-img"></image>
			</view>
			<image :src="jump?'/static/game/game-btn-click.png':'/static/game/game-btn.png'" class="grun-wrap-btn" mode="widthFix" @click="animate.onClick" v-if="courseLevel>1"></image>
			<view class="grun-wrap-target" v-if="targetImg.length&&level>1&&hideTargetCount>0" :style="styles.wrapTarget">
				<image class="grun-wrap-target-img" :src="item" mode="widthFix" v-for="(item,index) in targetImg"></image>
				<view class="grun-wrap-target-countdown" v-if="level>2">
					<view class="grun-wrap-target-countdown-top">
						{{hideTargetCount}}<text class="grun-wrap-target-countdown-top-text">s</text>
					</view>
					<text class="grun-wrap-target-countdown-top-text">倒计时</text>
				</view>
			</view>
		</view>
		<GmaeArithmetic :open="openReward" @change="addQuestionData" :speed="speed" />
		<GameDropoutReminder @change="close" />
		<BluConnect :status="openBlu" @change="close" :isSocket="isSocket" :eceuId="eceuId" />
		<GameTip :total="total" v-if="level==1" />
		<uni-popup ref="rewardPopup" :mask-click="false">
			<image class="grun-treatment" src="/static/game/game-treatment.gif" mode="widthFix"></image>
			<view class="grun-treatment-btn">
				<view class="grun-treatment-btn-left center" @click="closeTreatment">
					取消
				</view>
				<view class="grun-treatment-btn-right center" @click="goCenter">
					前往召唤
				</view>
			</view>
		</uni-popup>
	</view>
</template>
//背景图动画
<script module="animate" lang="renderjs">
	export default {
		data() {
			return {
				speedBg: 0.001, //速度
				groundMoveInterval: null, //动画
				isClick: false, //控制跳跃
				targetImgList: [], //下面正确照片
				grade: 1,
				groundBgLeft: 0,
				courLevel: 0
			}
		},
		mounted() {

			// this.groundScroll()
		},
		methods: {
			// 接收逻辑层发送的数据
			receiveCourseLevel(newValue, oldValue, ownerVm, vm) {
				this.courLevel = newValue
			},
			receiveLevel(newValue, oldValue, ownerVm, vm) {
				this.grade = newValue
			},
			// 接收逻辑层发送的数据
			receiveMsg(newValue, oldValue, ownerVm, vm) {
				this.speedBg = newValue
				this.groundMoveInterval && cancelAnimationFrame(this.groundMoveInterval)
				this.groundScroll()
			},
			// 接收逻辑层发送的数据
			receiveTargetImgMsg(newValue, oldValue, ownerVm, vm) {
				this.targetImgList = newValue
			},
			// 发送数据到逻辑层
			emitData(e, ownerVm) {
				ownerVm.callMethod('receiveRenderData', 1)
			},
			groundScroll() {
				let ground = document.querySelector('.grun-wrap')
				let groundBg = document.querySelector('.grun-bg')
				let _left = this.groundBgLeft
				let groundBgLeft = this.groundBgLeft //背景图位置
				ground.style.backgroundPositionX = _left + 'px'
				groundBg.style.backgroundPositionX = groundBgLeft + 'px';
				let cityMove = () => {
					_left -= this.speedBg * 1000
					groundBgLeft -= this.speedBg * 1000
					this.groundBgLeft = _left
					ground.style.backgroundPositionX = _left + 'px';
					groundBg.style.backgroundPositionX = groundBgLeft + 'px';
					this.groundMoveInterval = requestAnimationFrame(cityMove)
				}
				cityMove()
			},
			debounce(fn, delay) {
				let time = null; //time用来控制事件的触发
				return function () {
					if (time !== null) {
						clearTimeout(time);
					}
					time = setTimeout(() => {
						fn.call(this);
						//利用call(),让this的指针从指向window 转成指向input
					}, delay)
				}
			},
			//摘水果
			clickJump(ownerInstance) {
				if (this.isClick) {
					return
				}
				this.isClick = true
				let obsList = document.querySelector('.grun-oblist')
				let obItem = document.querySelectorAll('.grun-oblist-item')
				let animal = document.querySelector('.grun-wrap-animal')
				let toast = document.createElement('uni-view')
				let jumpAnimal = document.querySelector('.grun-wrap-jump')
				let showtime = ''
				let contactTrue = false
				toast.className = 'grun-oblist-item-toast'
				toast.style.background = `center center  url(${`./static/game/click-false.png`}) no-repeat`
				toast.style.backgroundSize = "150px 36px";
				toast.style.width = '200px'
				toast.style.height = '48px'
				toast.style.position = 'absolute'
				toast.style.top = '-48px'
				toast.style.left = '50%'
				toast.style.transform = 'translateX(-50%)'
				if (obItem.length > 0) {
					let click
					let contact = false
					let contactIndex = ''
					for (let i = 0; i < obItem.length; i++) {
						obItem[i].state = 'JumpTrue' //已经跳了
						showtime = obItem[i].showtime
						if (obItem[i].offsetLeft < animal.offsetLeft + animal.offsetWidth && obItem[i].offsetLeft > animal.offsetLeft) {
							let index = getComputedStyle(obItem[i]).background.indexOf('fruit-')
							let index1 = getComputedStyle(obItem[i]).background.indexOf('.png')
							click = 'fruit-' + getComputedStyle(obItem[i]).background.substring(index + 6, index1)
							if (this.grade == 3) {
								if (this.targetImgList[0].indexOf(click) != -1) {
									contactTrue = true
								}
							} else {
								for (let j = 0; j < this.targetImgList.length; j++) {
									if (this.targetImgList[j].indexOf(click) != -1) {
										contactTrue = true
										break
									}
								}
							}

							contactIndex = i
							contact = true
							break
						}
					}
					if (!contact) {
						toast.style.background = `center center  url(${`./static/game/jump-false.png`}) no-repeat`
						toast.style.backgroundSize = "108px 36px";
						toast.style.width = '108px'
						toast.style.height = '58px'
						toast.style.top = '-50px'
						jumpAnimal.appendChild(toast)
						// 调用 service 层的方法
						ownerInstance.callMethod('addAnswer', {
							type: 'signalStopFailCount'
						})
						setTimeout(() => {
							jumpAnimal.removeChild(toast)
						}, 500)
					} else if (contactTrue) {
						ownerInstance.callMethod('changeTargetImgList', `./static/game/${click}.png`)
						toast.style.background = `center center  url(${`./static/game/click-true.png`}) no-repeat`
						toast.style.backgroundSize = "88px 38px";
						toast.style.width = '108px'
						toast.style.height = '58px'
						obItem[contactIndex].appendChild(toast)
						// 调用 service 层的方法
						ownerInstance.callMethod('addAnswer', {
							type: 'signalNormalSuccCount',
							correctTime: new Date().getTime() - showtime
						})
						setTimeout(() => {
							obsList.removeChild(obItem[contactIndex])
						}, 500)
					} else {
						obItem[contactIndex].appendChild(toast)
						// 调用 service 层的方法
						ownerInstance.callMethod('addAnswer', {
							type: 'signalStopFailCount'
						})
						setTimeout(() => {
							obsList.removeChild(obItem[contactIndex])
						}, 500)
					}
				} else {
					toast.style.background = `center center  url(${`./static/game/jump-false.png`}) no-repeat`
					toast.style.backgroundSize = "108px 36px";
					toast.style.width = '108px'
					toast.style.height = '58px'
					toast.style.top = '-50px'
					jumpAnimal.appendChild(toast)
					// 调用 service 层的方法
					ownerInstance.callMethod('addAnswer', {
						type: 'signalStopFailCount'
					})
					setTimeout(() => {
						jumpAnimal.removeChild(toast)
					}, 500)
				}
				setTimeout(() => {
					this.isClick = false
				})
				this.debounce(ownerInstance.callMethod('jumpClick', {
					contact: contactTrue,
					showtime
				}), 500)

			},
			//地上障碍
			clickJumpGround(ownerInstance) {
				if (this.isClick) {
					return
				}
				this.isClick = true
				let grunWrap = document.querySelector(".grun-ground")
				let animal = document.querySelector('.grun-wrap-animal')
				let animlJump = document.querySelector('.grun-wrap-jump')
				if (grunWrap) {
					let animalOffsetLeft = animal.offsetLeft || animlJump.offsetLeft
					if (this.speedBg === 0) {
						grunWrap.state = 'JumpTrue'
						grunWrap.style.left = grunWrap.offsetLeft - 20 + 'px'
						ownerInstance.callMethod('addAnswer', {
							type: 'run'
						})
					} else if (grunWrap.offsetLeft > animalOffsetLeft && grunWrap.offsetLeft < (animal.offsetWidth + animalOffsetLeft)) {
						grunWrap.state = 'JumpTrue'
					}
				}

				setTimeout(() => {
					this.isClick = false
				})
				this.debounce(ownerInstance.callMethod('jumpClick', {
					time: 600
				}), 600)

			},
			onClick(event, ownerInstance) {

				if (this.courLevel > 1 && this.courLevel < 5) {
					this.debounce(this.clickJumpGround(ownerInstance), 600)
				} else {
					this.debounce(this.clickJump(ownerInstance), 500)
				}
				// this.debounce(this.eliminate(ownerInstance), 500) 消除模式取消
				// return
			}
		}
	}
</script>

<script>
	import {
		mapState,
		mapActions
	} from 'pinia'
	import GameResultVue from './GameResult.vue';
	import GameObstacleVue from './GameObstacle.vue';
	import GroundObstacles from './GroundObstacles.vue';
	import GameInfoVue from './GameInfo.vue';
	import GameProcessVue from './GameProcess.vue';
	import GameCountDownVue from './GameCountDown.vue';
	import GameDropoutReminder from './GameDropoutReminder.vue';
	import BluConnect from './BluConnect.vue';
	import GameTip from './GameTip.vue';
	import GmaeArithmetic from './GmaeArithmetic.vue';
	import {
		setKeepScreenOn,
		redirectTo,
		showLoading,
		hideLoading
	} from "@/common/uniTool";
	import {
		addRealEndTime,
		resetValue
	} from '@/utils/getFakeTime';
	import {
		addCourseTrainData,
		getGameCarrierSkin,
		addQuestionTrainData,
		getQryTrainDetail,
		subCourseTrainBeginDate
	} from "@/service/game";
	import {
		getVisitorDetail
	} from "@/service";
	import {
		useHelper
	} from '@/stores/helper.js'
	import {
		useLoginStore
	} from "@/stores/login";
	import {
		wsUrl,
		pdfUrl
	} from "@/common/global";
	import {
		onBackPress
	} from '@dcloudio/uni-app'
	import {
		randomNum
	} from '@/common/method.js';
	import {
		useSocketStore
	} from "@/stores/socket";
	import ws from '@/utils/websocket.js'

	let timeRef = null //距离定时器
	let hideTargetCountRef = null ///目标水果提示隐藏定时器
	let jumpAudioContextRef = null //跳跃音频
	let summoningCardAudioRef = null //召唤卡音频
	export default {
		components: {
			GameObstacleVue,
			GameInfoVue,
			GameProcessVue,
			GameResultVue,
			GameCountDownVue,
			GameDropoutReminder,
			BluConnect,
			GameTip,
			GroundObstacles,
			GmaeArithmetic
		},
		data() {
			return {
				// showani: -1, //开启动画碎片动画
				openReward: false, //开启算术空间
				rewardTime: '',
				queTrainDetail: null, //算术结果
				videoMusic: true, //背景音乐
				styles: {
					wrapTarget: {}
				},
				openBlu: false, //打开蓝牙弹窗
				gameStartTime: new Date().getTime(), //游戏开始时间
				level: 1, //关卡
				speed: 0, //脑电档次对应的速度
				total: 0, //距离
				jump: false, //能否跳跃
				windowWidth: 0, //屏幕宽度
				aniWidth: 0, //小兔子宽度 
				aniLeft: 0, //小兔子距离屏幕左边距离  
				imglist: ['./static/game/fruit-1.png', './static/game/fruit-2.png', './static/game/fruit-3.png', './static/game/fruit-4.png', './static/game/fruit-5.png', './static/game/fruit-6.png',
					'./static/game/fruit-7.png', './static/game/fruit-8.png', './static/game/fruit-9.png', './static/game/fruit-10.png', './static/game/fruit-11.png', './static/game/fruit-12.png',
					'./static/game/fruit-13.png'
				],
				targetImg: [], //目标的水果
				jumpList: [], //跳跃的水果
				targetIndex: 0, //目标水果个数
				interfereIndex: 4, //非目标水果个数
				gifIndex: 1,
				focus: 0,
				gameMin: false, //游戏暂停
				signalNormalSuccCount: 0, //反应成功数
				signalNormalFailCount: 0, //反应失败数
				signalStopSuccCount: 0, //抑制成功数
				signalStopFailCount: 0, //抑制失败数
				userInfo: null, //亲属姓名
				gameResult: null,
				showCount: true, //是否展示321倒计时
				gameStop: false, //游戏暂停
				courseLevel: 1, //课程数
				hideTargetCount: 3, //目标水果提示隐藏倒计时
				jumpImg: '', //跳跃兔子图片
				pdfUrl: pdfUrl,
				isGroundRun: false, //底部障碍物是否消除
				gameIsOver: false, //是否游戏结束
				rightRspTime: [], //正确反应时
				audioUrl: '', //背景音乐
				evaluatId: '', //测评编号
				eceuId: '', //脑电采集事件唯一识别
				isSocket: true, //是否开始传输数据
			}
		},
		onBackPress() {
			summoningCardAudioRef && summoningCardAudioRef.destroy()
			summoningCardAudioRef = null
			jumpAudioContextRef && jumpAudioContextRef.destroy()
			jumpAudioContextRef = null
		},
		onUnload() {
			clearInterval(timeRef)
			timeRef = null
			clearInterval(hideTargetCountRef)
			hideTargetCountRef = null
			if (this.socketTask) {
				resetValue()
				this.eceuId = ''
				this.socketTask.closeSocket()
			}
		},
		onReady() {
			console.log('加载完成');
		},
		onLoad(options) {
			console.log('加载');
			console.log(options);
			this.level = Number(options.level)
			this.courseLevel = Number(options.courseLevel)
			//#ifdef APP-PLUS
			setKeepScreenOn()
			plus.navigator.setFullscreen(true);
			this.getSocketTask(new ws(wsUrl, this.address))
			//#endif
			this.init()
			this.socketTask && this.socketTask.getWebSocketMsg((data) => {
				const {
					focus
				} = data
				let newSpeed
				switch (true) {
					case this.gameStop:
						newSpeed = 0
						break;
					case focus > 0 && focus < 40:
						this.gifIndex = 1
						newSpeed = 0.001
						break;
					case focus >= 40 && focus <= 49:
						this.gifIndex = 2
						newSpeed = 0.002
						break;
					case focus > 49 && focus <= 59:
						this.gifIndex = 3
						newSpeed = 0.003
						break;
					case focus >= 60 && focus <= 69:
						this.gifIndex = 4
						newSpeed = 0.004
						break;
					case focus >= 70 && focus <= 79:
						this.gifIndex = 5
						newSpeed = 0.005
						break;
					case focus > 79 && focus <= 89:
						this.gifIndex = 6
						newSpeed = 0.006
						break;
					case focus > 89 && focus <= 100:
						this.gifIndex = 7
						newSpeed = 0.007
						break;
					default:
						newSpeed = 0.001
						break;
				}
				this.focus = focus
				this.speed = newSpeed
			})

			if (this.courseLevel > 4) {
				switch (this.courseLevel) {
					case 5:
						this.targetIndex = 3
						this.interfereIndex = 4
						break;
					case 6:
						this.targetIndex = 3
						this.interfereIndex = 5
						break;
					case 7:
						this.targetIndex = 3
						this.interfereIndex = 6
						break;
					case 8:
						this.targetIndex = 3
						this.interfereIndex = 7
						break;
					case 9:
						this.targetIndex = 4
						this.interfereIndex = 5
						break;
					case 10:
						this.targetIndex = 4
						this.interfereIndex = 6
						break;
					case 11:
						this.targetIndex = 4
						this.interfereIndex = 7
						break;
					case 12:
						this.targetIndex = 5
						this.interfereIndex = 6
						break;
					case 13:
						this.targetIndex = 5
						this.interfereIndex = 7
						break;
					case 14:
						this.targetIndex = 6
						this.interfereIndex = 7
						break;
					case 15:
						this.targetIndex = 3
						this.interfereIndex = 4
						break;
					case 16:
						this.targetIndex = 3
						this.interfereIndex = 5
						break;
					case 17:
						this.targetIndex = 3
						this.interfereIndex = 6
						break;
					case 18:
						this.targetIndex = 3
						this.interfereIndex = 7
						break;
					case 19:
						this.targetIndex = 4
						this.interfereIndex = 5
						break;
					case 20:
						this.targetIndex = 4
						this.interfereIndex = 6
						break;
					case 21:
						this.targetIndex = 4
						this.interfereIndex = 7
						break;
					case 22:
						this.targetIndex = 5
						this.interfereIndex = 6
						break;
					case 23:
						this.targetIndex = 5
						this.interfereIndex = 7
						break;
					case 24:
						this.targetIndex = 6
						this.interfereIndex = 7
						break;
					default:
						break;
				}
				this.getTargetImgList(this.targetIndex, this.interfereIndex)
			}
		},
		computed: {
			...mapState(useHelper, ['helperBlu', 'address']),
			...mapState(useSocketStore, ['socketTask']),
			...mapState(useLoginStore, ['qryActiveTrainee'])
		},
		watch: {
			// 目标水果背景图
			level(newQuestion, oldQuestion) {
				if (newQuestion == 3) {
					this.styles.wrapTarget = {
						backgroundImage: `url('/static/game/game-fruit-bg${newQuestion}.png')`,
						backgroundSize: 'auto 60rpx',
						width: '410rpx',
						paddingLeft: '90rpx'
					}
				}
			},
			targetIndex(newQuestion, oldQuestion) {
				console.log(newQuestion);
				console.log(oldQuestion);
				if (newQuestion == 4) {
					this.hideTargetCount = 4
				} else if (newQuestion == 5) {
					this.hideTargetCount = 5
				} else if (newQuestion == 6) {
					this.hideTargetCount = 6
				} else {
					this.hideTargetCount = 3
				}
			},
		},
		methods: {
			...mapActions(useSocketStore, ['getSocketTask']),
			//关掉蓝牙连接页面
			close(value) {
				this.openBlu = value
			},
			//打开算术空间
			popArithmetic(type) {
				// console.log(this.gameResult.isTurnQuest, '------this.gameResult.isTurnQuest');
				if (this.gameResult.isTurnQuest && type === 'que') {
					this.$refs.finishPopup.close()
					this.rewardTime = new Date()
					this.openReward = true
				} else {
					this.$refs.finishPopup.close()
					this.getResult()
				}
			},
			addQuestionData(value) {
				this.openReward = false
				addQuestionTrainData({
					traineeId: this.qryActiveTrainee,
					grade: this.level,
					courseLevel: this.courseLevel,
					beginDate: this.rewardTime,
					questionDataList: value
				}).then(res => {
					this.getResult()
				}).catch(err => {
					console.log(err, '-----------');
				})
			},
			getResult() {
				showLoading('数据结算中...')
				// console.log("this.gameResult.isTurnQuest: " + JSON.stringify(this.gameResult.isTurnQuest));
				this.isSocket = false
				getQryTrainDetail({
					isTurnQuest: this.gameResult.isTurnQuest,
					traineeId: this.qryActiveTrainee,
					grade: this.level,
					courseLevel: this.courseLevel,
				}).then(res => {
					hideLoading()
					// console.log(res, '-------res');
					this.gameResult = res.data.courTrainDetail
					this.queTrainDetail = res.data.queTrainDetail
					this.$refs.popup.open('center')
					addRealEndTime(this.eceuId)
				}).catch(err => {
					hideLoading()
					console.log(err, '-----------');
				})
			},
			changeMusic(value) {
				this.videoMusic = value
			},
			//5.四舍五入保留2位小数（不够位数，则用0替补）
			keepTwoDecimalFull(num) {
				let result = parseFloat(num);
				if (isNaN(result)) {
					alert('传递参数错误，请检查！');
					return false;
				}
				result = Math.round(num * 100) / 100;
				let s_x = result.toString(); //将数字转换为字符串

				let pos_decimal = s_x.indexOf('.'); //小数点的索引值


				// 当整数时，pos_decimal=-1 自动补0  
				if (pos_decimal < 0) {
					pos_decimal = s_x.length;
					s_x += '.';
				}

				// 当数字的长度< 小数点索引+2时，补0  
				while (s_x.length <= pos_decimal + 2) {
					s_x += '0';
				}
				return s_x;
			},
			init() {
				getGameCarrierSkin({
					traineeId: this.qryActiveTrainee
				}).then(res => {
					this.jumpImg = res.data.skinGameUrlList
					this.audioUrl = res.data.audioUrl
				}).catch(err => {
					console.log(err);
				})
				getVisitorDetail({
					traineeId: this.qryActiveTrainee
				}).then(res => {
					this.userInfo = res.data
				}).catch(err => {
					console.log(err);
				})
				subCourseTrainBeginDate({
					traineeId: this.qryActiveTrainee,
					grade: this.level,
					courseLevel: this.courseLevel,
					beginDate: new Date().getTime()
				}).then(res => {
					console.log(res);
					this.evaluatId = res.data.evaluatId
					this.eceuId = res.data.eceuId
				}).catch(err => {
					console.log(err);
				})
			},
			addAnswer(value) {
				const {
					type,
					tarIndex,
					correctTime
				} = value
				if (type == 'signalNormalSuccCount') {
					this.signalNormalSuccCount++
					if (this.level == 2) {
						this.rightRspTime.push(correctTime)
					}
				} else if (type == 'signalStopCount') {
					if (this.level == 2) { //如果是等级2的时候 水果错过了
						if (this.targetImg.indexOf(tarIndex) != -1) {
							this.signalNormalFailCount++
						} else {
							this.signalStopSuccCount++
						}
					}
					if (this.level == 3) { //如果是等级3的时候 水果不是接下来正确的水果
						if (this.targetImg[0] == tarIndex) {
							this.signalNormalFailCount++
						} else {
							this.signalStopSuccCount++
						}
					}
				} else if (type == 'signalStopFailCount') {
					this.signalStopFailCount++
				} else if (type == 'run') {
					this.gameOver('continueGroundObstacles')
					console.log('移动');
				}
			},
			openTreatment() {
				if (summoningCardAudioRef) {
					summoningCardAudioRef.destroy()
				}
				summoningCardAudioRef = uni.createInnerAudioContext()
				summoningCardAudioRef.src = '/static/audio/shop_show.mp3'
				summoningCardAudioRef.play()
				this.$refs.finishPopup.close()
				this.$refs.rewardPopup.open('center')
			},
			closeTreatment() {
				this.$refs.finishPopup.open('center')
				this.$refs.rewardPopup.close()
			},
			goCenter() {
				redirectTo('/pages/game/growthPath')
			},
			postAnswer() {
				let _that = this
				let options = {
					traineeId: this.qryActiveTrainee,
					grade: this.level,
					runLength: Number(this.total),
					evaluatId: this.evaluatId,
					courseLevel: this.courseLevel,
					signalNormalSuccCount: this.signalNormalSuccCount,
					signalNormalFailCount: this.signalNormalFailCount,
					signalStopSuccCount: this.signalStopSuccCount,
					signalStopFailCount: this.signalStopFailCount,
					rightRspTime: this.rightRspTime
				}
				showLoading('数据结算中...')
				addCourseTrainData(options).then(res => {
					hideLoading()
					this.videoMusic = false //关闭音乐 如果有召唤卡 则打卡召唤卡音效
					this.gameResult = res.data
					//如果有碎片奖励则弹出碎片动画，否则直接打开奖励结果页面 去掉碎片逻辑 4.9
					// if (res.data.isGainDebris) {
					// 	this.showani = 1
					// 	let that = this
					// 	setTimeout(function () {
					// 		that.showani = -1
					// 		that.$refs.finishPopup.open('center')
					// 	}, 1150);
					// } else {
					// 	this.$refs.finishPopup.open('center')
					// }
					this.$refs.finishPopup.open('center')
					this.gameIsOver = true
				}).catch(err => {
					hideLoading()
					console.log(err);
				})
			},
			gameOver(value) {
				if (value === 'over') {
					clearInterval(timeRef)
					this.gameStop = true
					this.speed = 0
					this.postAnswer()
				} else if (value === 'stop') { //点击暂停
					this.isGroundRun = true
					this.gameStop = true
					this.speed = 0
					clearInterval(hideTargetCountRef)
					clearInterval(timeRef)
				} else if (value === 'stopGround') { //暂停障碍物
					this.gameStop = true
					this.speed = 0
					clearInterval(hideTargetCountRef)
					clearInterval(timeRef)
				} else if (value === 'continue') { //点击继续
					this.isGroundRun = false
					this.gameStop = false
					clearInterval(hideTargetCountRef)
					clearInterval(timeRef)
					this.showCount = true //展示倒计时
				} else if (value === 'continueGroundObstacles') { //障碍模式点击跳跃
					this.gameStop = false
					clearInterval(timeRef)
					this.show()
				} else if (value === 'reset') {
					uni.redirectTo({
						url: `/pages/game/index?level=${this.level}&courseLevel=${this.courseLevel}&&t=` + new Date().getTime()
					})
				}
			},
			getRandomArr(arr, length, type) {
				let result = [];
				while (true) {
					let num = 12
					if (type === 'jump') {
						num = length - 1
					}
					const random = randomNum(0, num);
					if (!result.includes(arr[random])) {
						result.push(arr[random]);
					}
					if (result.length == length) {
						break;
					}
				}
				return result
			},
			getIndex(x, y) {
				let x1
				let y1

				if (x1 == 3 && y1 == 7) {
					x1 = 4
					y1 = 5
				} else if (x1 == 4 && y1 == 7) {
					x1 = 5
					y1 = 6
				} else {
					x1 = x
					y1 = y + 1
				}
				this.targetIndex = x1
				this.interfereIndex = y1
			},
			getTargetImgList(tar, int) {
				let tarArr = this.getRandomArr(this.imglist, tar, 'tar')
				let screenList = []
				this.imglist.forEach(item => {
					if (!tarArr.includes(item)) {
						screenList.push(item)
					}
				})
				let nonTarget = this.getRandomArr(screenList, int, 'jump')
				this.targetImg = tarArr
				this.jumpList = [...nonTarget, ...tarArr]
			},
			start() {
				timeRef = setInterval(() => {
					const total = this.speed * 50 + Number(this.total)
					let value = this.keepTwoDecimalFull(total)
					this.total = value
				}, 50)
				if (this.courseLevel > 14) {
					this.targetImgHide()
				}
			},
			//开启隐藏水果提示倒计时
			targetImgHide() {
				hideTargetCountRef = setInterval(() => {
					if (this.hideTargetCount) {
						this.hideTargetCount -= 1
					} else {
						clearInterval(hideTargetCountRef)
						this.hideTargetCount = 0
					}
				}, 1000)
			},
			changeTargetImgList(value) {
				let index = this.targetImg.indexOf(value)
				this.targetImg.splice(index, 1)
				if (this.targetImg.length == 0) {
					// this.getIndex(this.targetIndex, this.interfereIndex)
					this.hideTargetCount = this.targetIndex
					this.getTargetImgList(this.targetIndex, this.interfereIndex)
					if (this.courseLevel > 14) {
						this.targetImgHide()
					}
				}

			},
			jumpClick(value) {
				if (!this.jump) {
					if (jumpAudioContextRef) {
						jumpAudioContextRef.pause();
						jumpAudioContextRef.destroy()
						jumpAudioContextRef = null
					}
					jumpAudioContextRef = uni.createInnerAudioContext()
					let time = value.time || 300
					if (value.contact) {
						jumpAudioContextRef.src = '/static/audio/game-jump.mp3'
					} else {
						jumpAudioContextRef.src = '/static/audio/game-jfalse.MP3'
					}
					jumpAudioContextRef.play()
					this.jump = true
					setTimeout(() => {
						this.jump = false
					}, time)
				}
			},
			show(value) {

				this.showCount = false
				this.speed = 0.001
				this.start() //游戏开始
			},
			receiveRenderData(val) {
				console.log('renderjs返回的值-->', val);
			}
		}
	}
</script>
<style lang="scss">
	@keyframes right-small {
		0% {
			left: 50%;
			transform: translateX(-50%);
			width: 200rpx;
		}

		50% {
			left: 70%;
			width: 150rpx;
		}

		100% {
			top: 38%;
			left: 82%;
			width: 114rpx;
		}
	}

	.grun {
		position: relative;
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;

		&-treatment {
			width: 100vw;
			position: relative;

			&-btn {
				position: absolute;
				bottom: 54rpx;
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				align-items: center;

				&-left {
					width: 133rpx;
					height: 43rpx;
					background: #FF7A4C;
					box-shadow: inset 0rpx 0 1rpx 0rpx #FFED80;
					border-radius: 10rpx;
					border: 1rpx solid #DF9C51;
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: 500;
					font-size: 20rpx;
					color: #FFFFFF;
					margin-right: 26rpx;
				}

				&-right {
					width: 233rpx;
					height: 43rpx;
					background: #FDDC55;
					box-shadow: inset 0rpx 0 1rpx 0rpx #FFED80;
					border-radius: 10rpx;
					border: 1rpx solid #DF9C51;
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: 500;
					font-size: 20rpx;
					color: #AF6317;
				}
			}
		}

		&-ani2 {
			top: 30%;
			width: 114px;
			height: 50px;
			position: absolute;
			animation: right-small 1s forwards linear;
			z-index: 2;
		}

		&-ani1 {
			width: 100vw;
			height: 100vh;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 999;
		}

		&-finish {
			width: 100vw;
			position: relative;

			&-box {
				position: absolute;
				right: 92rpx;
				top: 28%;
				display: flex;
				flex-direction: column;
				align-items: center;
				z-index: 1;

				&-pre {
					border-radius: 19rpx;
					border: 2rpx solid #FFFFFF;
					background: #005274;
				}

				&-img {
					width: 164rpx;
				}

				&-text {
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 15rpx;
					color: #111111;
					margin-bottom: 8rpx;
				}
			}

			&-money {
				position: absolute;
				top: 36%;
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100%;

				&-box {
					display: flex;
					align-items: center;
					margin-top: 70rpx;

					&-left {
						width: 170rpx;
						height: 40rpx;
						background: #FAE613;
						border-radius: 20rpx;
						border: 2rpx solid #E35830;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #E35830;
						margin-right: 25rpx;
					}

					&-right {
						font-size: 20rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #FFFFFF;
						width: 170rpx;
						height: 40rpx;
						background: #E35830;
						border-radius: 20rpx;
						border: 2rpx solid rgba(255, 97, 40, 1);
					}
				}

				&-btn {
					font-size: 20rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #FFFFFF;
					width: 170rpx;
					height: 40rpx;
					background: #E35830;
					border-radius: 20rpx;
					margin-top: 70rpx;
					border: 2rpx solid rgba(255, 97, 40, 1);
				}

				&-center {
					font-size: 15rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #E35830;
				}

				&-bottom {
					margin-top: 16rpx;
					font-size: 15rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #000000;
				}

				&-top {
					display: flex;
					align-items: center;

					&-full {
						margin-right: 220rpx;
						margin-top: 10rpx;
					}

					&-left {
						font-size: 15rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #A6622B;
					}

					&-center {
						width: 300rpx;
						height: 16rpx;
						background: #B1740E;
						border-radius: 8rpx;
						border: 1rpx solid #FEC38D;
						position: relative;
						margin: 0 16rpx 0 10rpx;

						&-num {
							font-size: 15rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: 500;
							color: #A6622B;
							margin-right: 10rpx;
						}

						&-text {
							position: absolute;
							font-size: 15rpx;
							font-family: SourceHanSansCN-Bold, SourceHanSansCN;
							font-weight: bold;
							color: #E35830;
							bottom: -22rpx;
							left: 0;
							width: 100%;
						}

						&-value {
							width: 30rpx;
							height: 12rpx;
							background: linear-gradient(180deg, #FFE97C 0%, #FE8C14 100%);
							border-radius: 8rpx;
							position: absolute;
							left: 1rpx;
							top: 50%;
							transform: translateY(-50%);
						}
					}

					&-right {
						width: 84rpx;
						height: 91rpx;
						margin-right: 6rpx;
					}

					&-box {
						margin-left: 22rpx;

						&-num {
							width: 223rpx;
							height: 35rpx;
							background: #FFF1ED;
							border-radius: 19rpx;
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: bold;
							font-size: 15rpx;
							color: #FF3900;
						}

						&-text {
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: bold;
							font-size: 18rpx;
							color: #111111;
							margin-top: 22rpx;
						}
					}
				}
			}
		}

		&-oblist {
			margin-top: 44rpx;
			width: 100%;
			height: 94rpx;
			position: relative;

			&-item {
				width: 94rpx;
				height: 94rpx;
				background: rgba(20, 122, 181, 0.51);
				position: absolute;
				top: 0;
				right: 0;
			}

		}

		&-bg {
			height: 66vh;
			box-sizing: border-box;
			background-repeat: repeat-x;
			background-size: auto 66vh;
		}

		&-wrap {
			height: 34vh;
			box-sizing: border-box;
			background-repeat: repeat-x;
			background-size: auto 34vh;
			position: relative;

			&-target {
				height: 60rpx;
				width: 325rpx;
				position: absolute;
				background: url('~@/static/game/game-fruit-bg2.png');
				background-repeat: no-repeat;
				background-size: 325rpx 60rpx;
				bottom: 14rpx;
				left: 50%;
				transform: translateX(-50%);
				display: flex;
				justify-content: space-around;
				padding: 18rpx 28rpx 0 28rpx;

				&-countdown {
					width: 35rpx;
					height: 35rpx;
					background: #B26034;
					border-radius: 5rpx;
					display: flex;
					align-items: center;
					flex-direction: column;

					&-top {
						font-size: 16rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #FFFFFF;

						&-text {
							font-size: 8rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: 500;
							color: #FFFFFF;
						}

					}


				}

				&-img {
					background: #F6FFF4;
					width: 35rpx;
					height: 35rpx;
					border-radius: 5rpx;
					border: 1rpx solid #F17450;
				}
			}

			&-animal {
				width: 200rpx;
				position: absolute;
				top: -134rpx;
				left: 140rpx;
				z-index: 1;
			}

			&-btn {
				width: 125rpx;
				height: 125rpx;
				position: absolute;
				right: 10rpx;
				top: 10rpx;
				z-index: 1;
			}

			&-jump {
				width: 200rpx;
				position: absolute;
				top: -168rpx;
				left: 140rpx;

				&-img {
					width: 100%;
				}

			}
		}

		&-box {
			background-color: red;
			position: absolute;
			width: 100px;
			height: 100px;
			top: 0;
			right: 0;
		}
	}
</style>