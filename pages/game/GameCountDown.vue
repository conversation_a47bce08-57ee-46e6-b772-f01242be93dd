<template>
	<view class="count-mask">
		<image src="~@/static/game/count.png" class="count-mask-text"></image>
		<image :src="`../../static/game/count-${state.time}.png`" class="count-mask-img" mode="widthFix"></image>
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		watch
	} from "vue";
	const props = defineProps(['count', 'isStart'])
	const emit = defineEmits(['show'])
	const state = reactive({
		timeRef: null,
		time: props.count
	})
	onMounted(() => {
		if (props.isStart) {
			setTimer() // 调用函数
		}
	})
	onUnmounted(() => {
		clearInterval(state.timeRef)
	})
	watch(() => state.time, (time) => {
		if (!time) {
			emit('show', time)
		}
	})
	const setTimer = () => {
		console.log('创建倒计时');
		// 3 创建倒计时
		state.timeRef = setInterval(() => {
			if (state.time) {
				state.time -= 1
			} else {
				clearInterval(state.timeRef)
				state.time = 0
			}
		}, 1000)
	}
</script>

<style lang="scss">
	.count-mask {
		position: absolute;
		width: 100%;
		height: 100vh;
		z-index: 999;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.8);

		&-text {
			position: absolute;
			top: 30vh;
			left: 50%;
			transform: translateX(-50%);
			width: 390rpx;
			height: 30rpx;

		}

		&-img {
			position: absolute;
			top: 50vh;
			left: 50%;
			transform: translateX(-50%);
			width: 40rpx;
		}
	}
</style>