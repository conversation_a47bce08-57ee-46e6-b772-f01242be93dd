<template>
	<view class="gameResult">
		<view class="gameResult-title font-game">
			本次训练小结
		</view>
		<view class="gameResult-content">
			<view class="gameResult-content-left">
				<view class="gameResult-content-left-bottom">
					<!-- 	<view class="gameResult-content-left-bottom-title">
						课程周期：第 <text class="gameResult-content-left-bottom-title-num">{{props.gameResult&&props.gameResult.currentDay}}</text> 天 剩余<text
							class="gameResult-content-left-bottom-title-num">{{props.gameResult&&props.gameResult.residueDay}}</text> 天
					</view> -->
					<view class="gameResult-content-left-bottom-box">
						<view class="gameResult-content-left-bottom-box-left">
							<view class="gameResult-content-left-bottom-box-left-title">
								今日累计：
							</view>
							<view class="gameResult-content-left-bottom-box-left-value DINAlternate-Bold" style="margin-bottom: 5rpx;">
								{{props.gameResult&&props.gameResult.sumConsumTime}}
							</view>
							<view class="gameResult-content-left-bottom-box-left-title">
								还需训练：
							</view>
							<view class="gameResult-content-left-bottom-box-left-value DINAlternate-Bold">
								{{props.gameResult&&props.gameResult.needConsumTime}}
							</view>
						</view>
						<view class="gameResult-content-left-bottom-box-line">

						</view>
						<view class="gameResult-content-left-bottom-box-right">
							<view class="gameResult-content-left-bottom-box-right-title">
								上回合训练
							</view>
							<view class="gameResult-content-left-bottom-box-right-value" v-if="props.gameResult">
								{{props.gameResult.lastAvgFocusLevel?props.gameResult.lastAvgFocusLevel:'暂无'}}
							</view>
							<view class="gameResult-content-left-bottom-box-right-text">
								脑电专注
							</view>
						</view>
					</view>
				</view>
				<!-- <GameQuesEchartsVue :gameTimeDate="props.gameResult&&props.gameResult.focusDate" /> -->
				<view class="gameResult-content-left-top">
					<view class="gameResult-content-left-top-title">
						<image src="../../static/game/game-result-gift.png" mode="widthFix" class="gameResult-content-left-top-title-img"></image>
						<view class="gameResult-content-left-top-title-left">
							训练奖励
						</view> <text class="gameResult-content-left-top-title-text">脑力值：<text
								style="font-size: 20rpx;color: #FE3307;font-weight: bold;">{{props.queTrainDetail&&props.queTrainDetail.points===null?'-':props.queTrainDetail.points}}</text>
							个</text>
					</view>
					<view class="gameResult-content-left-top-tip">
						<text>做题专注力</text>
						<view class="gameResult-content-left-top-tip-time center" :class="props.queTrainDetail&&props.queTrainDetail.isTimeOut?'gameResult-content-left-top-tip-time-out':''">
							{{props.queTrainDetail&&props.queTrainDetail.isTimeOut===false?'未超时':'已超时'}}
						</view>
					</view>
					<GameQuesEchartsVue :gameTimeDate="props.queTrainDetail&&props.queTrainDetail.focusDate" />
					<view class="gameResult-content-left-top-focus" style="margin-bottom: 4rpx;">
						<view class="gameResult-content-left-top-focus-left">
							最高专注力 <text>{{props.queTrainDetail&&props.queTrainDetail.maxFocus===null?'-':props.queTrainDetail.maxFocus}}</text>
						</view>
						<view class="gameResult-content-left-top-focus-right">
							平均专注力 <text>{{props.queTrainDetail&&props.queTrainDetail.avgFocus===null?'-':props.queTrainDetail.avgFocus}}</text>
						</view>
					</view>
					<view class="gameResult-content-left-top-focus">
						<view class="gameResult-content-left-top-focus-left" style="border-bottom: none;width:72rpx ;border-radius: 5rpx 0rpx 0rpx 0rpx;">
							正确 <text>{{props.queTrainDetail&&props.queTrainDetail.rightCount===null?'-':props.queTrainDetail.rightCount}}题</text>
						</view>
						<view class="gameResult-content-left-top-focus-right" style="border-bottom: none;;width:72rpx;border-radius: 0%;">
							错误 <text>{{props.queTrainDetail&&props.queTrainDetail.failCount===null?'-':props.queTrainDetail.failCount}}题</text>
						</view>
						<view class="gameResult-content-left-top-focus-right" style="border-bottom: none;;width:72rpx ;border-radius: 0rpx 5rpx 0rpx 0rpx;">
							剩余 <text>{{props.queTrainDetail&&props.queTrainDetail.undoneCount===null?'-':props.queTrainDetail.undoneCount}}题</text>
						</view>
					</view>
					<view class="gameResult-content-left-top-focus" style="margin-bottom: 8rpx;">
						<view class="gameResult-content-left-top-focus-left" style="width:72rpx ;border-radius: 0rpx 0rpx 0rpx 5rpx;">
							累计 <text>{{props.queTrainDetail&&props.queTrainDetail.consumTime===null?'-':props.queTrainDetail.consumTime}}</text>
						</view>
						<view class="gameResult-content-left-top-focus-right" style="width:72rpx;border-radius: 0%;">
							平均 <text>{{props.queTrainDetail&&props.queTrainDetail.avgConsumTime===null?'-':props.queTrainDetail.avgConsumTime}}</text>
						</view>
						<view class="gameResult-content-left-top-focus-right" style="width:72rpx ;border-radius: 0rpx 0rpx 5rpx 0rpx;;">
							最快 <text>{{props.queTrainDetail&&props.queTrainDetail.minConsumTime===null?'-':props.queTrainDetail.minConsumTime}}</text>
						</view>
					</view>
				</view>

			</view>
			<view class="gameResult-content-right">
				<view class="gameResult-content-right-center">
					<view class="gameResult-content-right-center-text">
						训练课程：{{props.gameResult&&props.gameResult.courseName}}
					</view>
					<view class="gameResult-content-right-center-text">
						课程等级： 课程{{props.gameResult&&props.gameResult.courseLevel}}
					</view>
					<view class="gameResult-content-right-center-text">
						课程时间： 第{{props.gameResult&&props.gameResult.courseLevel}}天
					</view>
					<view class="gameResult-content-right-center-img">
						<view class="gameResult-content-right-center-img-box" v-show="props.level>1">
							<view class="gameResult-content-right-center-img-box-text">{{props.gameResult&&props.gameResult.signalStopLevel}}</view>
							<view class="gameResult-content-right-center-img-box-tip">反应抑制</view>
							<image class="gameResult-content-right-center-img-box-value"
								:src="props.gameResult&&props.gameResult.signalStopLevel=='A'||props.gameResult&&props.gameResult.signalStopLevel=='B'?'../../static/game/game-result-icon-1.png':'../../static/game/game-result-icon-2.png'"
								mode="heightFix"></image>
						</view>
						<view class="gameResult-content-right-center-img-box" style="background-color:  #6169DF;">
							<view class="gameResult-content-right-center-img-box-text">{{props.gameResult&&props.gameResult.avgFocusLevel}}</view>
							<view class="gameResult-content-right-center-img-box-tip">脑电专注</view>
							<image class="gameResult-content-right-center-img-box-value"
								:src="props.gameResult&&props.gameResult.avgFocusLevel=='A'||props.gameResult&&props.gameResult.avgFocusLevel=='B'?'../../static/game/game-result-icon-1.png':'../../static/game/game-result-icon-2.png'"
								mode="heightFix"></image>
						</view>
					</view>
				</view>
				<view class="gameResult-content-right-top">
					训练数据
					<view class="gameResult-content-right-top-money">
						脑力值：
						<view class="gameResult-content-right-top-money-value">
							<view class="gameResult-content-right-top-money-value-num" v-if="props.gameResult" :style="{ width:props.gameResult.todayPoints*4.1 + 'rpx' }">

							</view>
						</view>
						{{props.gameResult&&props.gameResult.todayPoints}}
					</view>
				</view>
				<view class="gameResult-content-right-bottom">

					<view class="gameResult-content-right-bottom-left">
						<view class="" v-if="props.level>1">
							<view class="gameResult-content-right-bottom-left-value">
								反应成功：{{props.gameResult&&props.gameResult.signalNormalSuccCount}}
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								反应失败：{{props.gameResult&&props.gameResult.signalNormalFailCount}}
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								抑制成功：{{props.gameResult&&props.gameResult.signalStopSuccCount}}
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								抑制失败：{{props.gameResult&&props.gameResult.signalStopFailCount}}
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								正确抑制率：{{props.gameResult&&props.gameResult.signalStopRspRate}}%
							</view>
							<view class="gameResult-content-right-bottom-left-value" v-if="props.level==2">
								正确反应时：{{props.gameResult&&props.gameResult.rightRspTime}}
							</view>
						</view>
						<view class="gameResult-content-right-bottom-left-box" v-else>
							<view class="gameResult-content-right-bottom-left-value">
								跑动距离：{{props.gameResult&&props.gameResult.runLength}}m
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								未完成距离：{{props.gameResult&&props.gameResult.noRunLength}}m
							</view>
						</view>
						<view>
							<view class="gameResult-content-right-bottom-left-value">
								最高专注力：{{props.gameResult&&props.gameResult.maxFocus}}
							</view>
							<view class="gameResult-content-right-bottom-left-value">
								平均专注力：{{props.gameResult&&props.gameResult.avgFocus}}
							</view>
						</view>
					</view>
					<GameEchartsVue :gameTimeDate="props.gameResult&&props.gameResult.focusDate" />
				</view>
			</view>
		</view>
		<view class="gameResult-btn">
			<view class="gameResult-btn-left center" @click="goCourse">
				回到主页
			</view>
			<view class="gameResult-btn-center center" @click="next">
				继续下一回合
			</view>
			<view class="gameResult-btn-left center" @click="go">
				返回首页
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		switchTab,
		redirectTo
	} from "../../common/uniTool";
	import GameEchartsVue from './GameEcharts.vue';
	import GameQuesEchartsVue from "./GameQuesEcharts.vue";

	const props = defineProps(['gameResult', 'userInfo', 'level', 'courseLevel', 'queTrainDetail'])
	const goCourse = () => {
		redirectTo('/pages/game/course')
	}
	const go = () => {
		switchTab('/pages/index/index')
	}
	const next = () => {
		uni.redirectTo({
			url: `/pages/game/index?level=${props.level}&courseLevel=${props.courseLevel}&&t=` + new Date().getTime()
		})
	}
	const getSch = (stanOfCul) => {
		let text
		switch (stanOfCul) {
			case '0':
				text = '学龄前'
				break;
			case '1':
				text = '幼儿园'
				break;
			case '2':
				text = '小学'
				break;
			case '3':
				text = '初中'
				break;
			case '4':
				text = '高中'
				break;
			default:
				break;
		}
		return text
	}
</script>

<style lang="scss">
	.gameResult {
		width: 94vw;
		height: 95vh;
		background: url('~@/static/game/game-result-bg.png');
		background-repeat: no-repeat;
		background-size: 94vw 95vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		padding-bottom: 30rpx;

		&-btn {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: 80%;

			&-left {
				font-size: 20rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #FF943B;
				width: 159rpx;
				height: 40rpx;
				border-radius: 20rpx;
				border: 1rpx solid #FF943B;
			}

			&-center {
				font-size: 20rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;
				width: 214rpx;
				height: 40rpx;
				background: #FF943B;
				border-radius: 20rpx;
			}
		}

		&-title {
			font-size: 25rpx;
			font-weight: normal;
			color: #8D3F1E;

		}

		&-content {
			margin-top: 8rpx;
			margin-bottom: 8rpx;
			display: flex;
			width: 100%;
			justify-content: center;
			padding: 0 28rpx 0 69rpx;
			flex: 1;

			&-right {
				font-size: 13rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #111111;
				flex: 1;
				margin-left: 6rpx;
				border-radius: 5rpx;

				&-top {
					width: 100%;
					background: #FFFFFF;
					border-bottom: 3rpx solid #F0F0F0;
					padding: 10px 15rpx;
					border-radius: 5rpx 5rpx 0 0;
					font-size: 15rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #111111;
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-money {
						display: flex;
						align-items: center;
						font-size: 13rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #A6622B;

						&-value {
							width: 125rpx;
							height: 13rpx;
							background: #A6622B;
							border-radius: 8rpx;
							margin: 0 5rpx;
							padding: 1rpx;
							position: relative;

							&-num {
								position: absolute;
								top: 50%;
								transform: translateY(-50%);
								left: 1rpx;
								height: 10rpx;
								background: linear-gradient(180deg, #FFE97C 0%, #FE8C14 100%);
								border-radius: 8rpx;
							}
						}
					}
				}

				&-bottom {
					padding: 6rpx;
					padding-left: 15rpx;
					display: flex;
					align-items: center;
					flex-wrap: nowrap;
					background: #FFFFFF;
					border-radius: 0 0 5rpx 5rpx;

					&-left {
						width: 124rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						height: 148rpx;

						&-value {
							margin-bottom: 4rpx;
							font-size: 12rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #111111;
						}

						&-value:last-child {
							margin-bottom: 0;
						}
					}
				}

				&-center {
					padding: 10rpx 14rpx;
					position: relative;
					background: #FFFFFF;
					border-radius: 5rpx;
					margin-bottom: 4rpx;

					&-img {
						right: 10rpx;
						top: 50%;
						transform: translateY(-50%);
						position: absolute;
						display: flex;
						align-items: center;

						&-box {
							width: 59rpx;
							height: 59rpx;
							background: #FB4705;
							border-radius: 50%;
							font-size: 10rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #FFFFFF;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							position: relative;
							padding-bottom: 18rpx;
							margin-right: 28rpx;
							padding-top: 8rpx;

							&-text {
								font-size: 35rpx;
								font-family: DINAlternate-Bold, DINAlternate;
								font-weight: bold;
								color: #FFFFFF;
								line-height: 57px;
							}

							&-tip {
								font-size: 10rpx;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #FFFFFF;
							}

							&-value {
								height: 48rpx;
								position: absolute;
								top: 6rpx;
								right: -20rpx;
							}
						}
					}

					&-text {
						margin-top: 6rpx;
					}
				}

				&-title {
					height: 38rpx;
					background: #FFDFB4;
					border-radius: 5rpx 5rpx 0rpx 0rpx;
					font-size: 18rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #8D3F1E;
					line-height: 38rpx;
					padding-left: 15rpx;
				}

			}

			&-left {
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				&-bottom {
					width: 100%;
					background: #FFFFFF;
					border-radius: 5rpx;
					margin-bottom: 5rpx;

					&-box {
						display: flex;
						align-items: center;
						padding: 10rpx 15rpx;
						justify-content: space-between;

						&-line {
							width: 1rpx;
							height: 63rpx;
							background: #F0F0F0;
							margin-left: 25rpx;
						}

						&-left {
							display: flex;
							flex-direction: column;
							font-size: 9rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #111111;

							&-value {
								font-size: 15rpx;
								font-weight: bolder;
							}
						}

						&-right {
							display: flex;
							flex-direction: column;
							align-items: center;
							font-size: 13rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #111111;
							flex: 1;


							&-value {
								font-size: 20rpx;
								font-family: DINAlternate-Bold, DINAlternate;
								font-weight: bold;
								color: #6169DF;
								margin: 8rpx;
							}

							&-text {
								font-size: 9rpx;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #111111;
							}
						}
					}
				}

				&-top {
					display: flex;
					flex-direction: column;
					background: #FFFFFF;
					border-radius: 5rpx;
					flex: 1;

					&-focus {
						display: flex;
						align-items: center;
						padding: 0 10rpx;
						/*  */
						font-size: 10rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #111111;

						&-left {
							padding-left: 5rpx;
							width: 108rpx;
							height: 20rpx;
							line-height: 20rpx;
							background: #FFFFFF;
							border-radius: 5rpx 0rpx 0rpx 5rpx;
							border: 1rpx solid #FECA7E;
						}

						&-right {
							padding-left: 5rpx;
							line-height: 20rpx;
							width: 108rpx;
							height: 20rpx;
							background: #FFFFFF;
							border-radius: 0rpx 5rpx 5rpx 0rpx;
							border: 1rpx solid #FECA7E;
							border-left: none;
						}
					}

					&-tip {
						display: flex;
						align-items: center;
						font-size: 10rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #111111;
						padding: 0 10rpx;
						justify-content: space-between;
						margin-top: 6rpx;

						&-time {
							width: 40rpx;
							height: 15rpx;
							background: #51C48F;
							border-radius: 3rpx;
							font-size: 10rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #FFFFFF;

							&-out {
								background: #FF0000;
								color: #FFFFFF;
							}
						}
					}

					&-title {
						display: flex;
						justify-content: space-between;
						font-size: 15rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #111111;
						width: 235rpx;
						height: 38rpx;
						background: #FFFFFF;
						border-radius: 5rpx 5rpx 0rpx 0rpx;
						line-height: 38rpx;
						padding-left: 10rpx;
						border-bottom: 3rpx solid #F0F0F0;
						padding-right: 11rpx;
						align-items: center;

						&-left {
							font-size: 15rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: bold;
							flex: 1;
							margin-left: 5rpx;
						}

						&-img {
							width: 15rpx;
							height: 15rpx;

						}

						&-text {
							font-size: 13rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: bold;
							color: #111111;
						}
					}




				}

			}
		}
	}
</style>