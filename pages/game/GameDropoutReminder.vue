<template>
	<uni-popup ref="bluErr" type="center">
		<image src="../../static/game/game-reminder-img.png" mode="widthFix" class="bluErr-reminder" @click="open" v-if="helper.bluData.type==5">
		</image>
		<view class="bluErr-signal" v-else>
			<view class="bluErr-signal-tip center">
				状态：信号脱落
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		watch,
		ref,
		reactive
	} from "vue";
	import {
		useHelper
	} from "../../stores/helper";
	import BluConnect from "./BluConnect.vue";
	const bluErr = ref(null)
	const helper = useHelper(); //设备仓库
	const event = defineEmits(['change'])

	watch(() => helper.bluData, (bluData) => {
		if (bluData.type == 5 || bluData.signal) {
			bluErr.value.open('center')
		} else {
			bluErr.value.close()
		}
	})

	//打开蓝牙连接页面
	const open = () => {
		bluErr.value.close()
		event('change', true)
	}
</script>

<style lang="scss">
	.bluErr-signal {
		width: 410rpx;
		height: 220rpx;
		background: url('~@/static/game/blu-reminder-img.png');
		background-repeat: no-repeat;
		background-size: 410rpx auto;
		position: relative;

		&-tip {
			position: absolute;
			bottom: 26rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 200rpx;
			height: 40rpx;
			background: #FFE7E7;
			border-radius: 10rpx;
			font-size: 15rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FF4747;
		}
	}

	.bluErr-reminder {
		width: 400rpx;
	}
</style>