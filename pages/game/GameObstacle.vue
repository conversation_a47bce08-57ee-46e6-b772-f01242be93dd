<template>
	<!-- #ifdef APP-PLUS || H5 -->
	<view :speed="speed" :jumpList="jumpList" :targetImg="targetImg" :grade="level" :change:speed="fruit.updateValue" :change:jumpList="fruit.updateJumpList" :change:targetImg="fruit.updateTargetImg"
		:change:grade="fruit.updateGrade" id="fruit" class="fruit" />
	<!-- #endif -->
	<!-- #ifndef APP-PLUS || H5 -->
	<view>非 APP、H5 环境不支持</view>
	<!-- #endif -->
</template>


<script>
	export default {
		name: 'GameObstacle',
		props: {
			speed: {
				required: true
			},
			jumpList: {
				type: Array,
				required: true
			},
			targetImg: {
				type: Array,
				required: true
			},
			level: {
				required: true
			},
		},
		emits: ['event'],
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			addAnswer(value) {
				this.$emit('event', value)
			}
		}
	}
</script>
<script module="fruit" lang="renderjs">
	export default {
		data() {
			return {
				grade: '', //课程等级
				bluLevel: '', //脑电等级
				obsItem: [], //水果dom
				obsMoveInterval: null, //水果移动
				jumList: [], //移动的水果
				creatInner: null,
				targetImgList: [], //目标水果
				boxGoImgNum: 0, //三个go存在个数
				boxNoGoImgNum: 0, //三个go存在个数
			}
		},
		mounted() {
			// this.obsMove()
		},
		methods: {
			updateTargetImg(newValue, oldValue, ownerInstance, instance) {
				this.targetImgList = newValue
				this.obsMoveInterval && cancelAnimationFrame(this.obsMoveInterval)
				this.obsMove(ownerInstance)
			},
			updateGrade(newValue, oldValue, ownerInstance, instance) {
				this.grade = newValue
			},
			updateValue(newValue, oldValue, ownerInstance, instance) {
				this.bluLevel = newValue
				// this.creatInner && clearInterval(this.creatInner)
				// this.createObstruction(ownerInstance)
				this.obsMoveInterval && cancelAnimationFrame(this.obsMoveInterval)
				this.obsMove(ownerInstance)
			},
			updateJumpList(newValue, oldValue, ownerInstance, instance) {
				this.jumList = newValue
				this.creatInner && clearInterval(this.creatInner)
				this.createObstruction(ownerInstance)
			},
			createObstruction(ownerInstance) {
				let obsList = document.querySelector(".grun-oblist")
				this.creatInner = setInterval(() => {
					let obsItem = document.createElement('uni-view')
					if (this.grade === 3) {
						let num = Math.floor(Math.random() * (this.jumList.length))
						let url = this.jumList[num]
						let arr = []
						this.jumList.forEach(item => {
							if (this.targetImgList.indexOf(item) == -1) {
								arr.push(item)
							}
						})

						if (this.targetImgList[0] == url) { //存在在目标水果中
							this.boxGoImgNum++
						} else {
							this.boxNoGoImgNum++
						}
						//出现两次nogo则出现go
						if (this.boxNoGoImgNum > 1) {
							let num = Math.round(Math.random())
							if (this.targetImgList.length == 1) {
								num = 0
							}
							url = this.targetImgList[num]
							this.boxNoGoImgNum = 0
						}
						//出现三次go则出现nogo
						if (this.boxGoImgNum > 3) {
							let num = Math.floor(Math.random() * (arr.length))
							url = arr[num]
							this.boxGoImgNum = 0
						}
						obsItem.style.background = `center center url(${url})  no-repeat rgba(20,122,181,0.51) `
					} else {
						obsItem.style.background = 'center center url(./static/game/game-blind-box.png)  no-repeat rgba(20,122,181,0.51) '
					}
					obsItem.className = 'grun-oblist-item'
					obsItem.style.width = '120px'
					obsItem.style.height = '120px'
					obsItem.style.borderRadius = '50%'
					obsItem.style.position = 'absolute'
					obsItem.style.top = 0
					obsItem.state = 'JumpFalse'
					obsItem.style.left = document.documentElement.clientWidth + 'px'
					obsItem.timeShow = new Date().getTime()
					let childrens = document.querySelectorAll(".grun-oblist-item")
					if (Object.keys(childrens).length == 0) {
						obsList.appendChild(obsItem)
					} else {
						obsItem.remove()
					}
				}, 1500);

			},
			transferFruit(ownerInstance, arr) {
				// 调用 service 层的方法
				ownerInstance.callMethod('getFruitList', {
					arr
				})

			},
			obsMove(ownerInstance) {
				if (this.bluLevel) {
					let obsList = document.querySelector(".grun-oblist")
					let animal = document.querySelector('.grun-wrap-animal')
					let animlJump = document.querySelector('.grun-wrap-jump')
					let cityMove = () => {
						let obsDoms = document.querySelector(".grun-oblist").children
						if (obsDoms.length > 0 && this.bluLevel) {
							// 给每个障碍物添加移动
							for (let index = 0; index < obsDoms.length; index++) {
								let item = obsDoms[index]
								if (item.offsetLeft < -item.offsetWidth) {
									obsList.removeChild(item)
								} else {
									let animalOffsetLeft = animal.offsetLeft || animlJump.offsetLeft
									//水果距离
									if (item.offsetLeft <= (animalOffsetLeft + item.offsetWidth + 500) && item.offsetLeft > (animalOffsetLeft + item.offsetWidth + 485) && item.children.length ===
										0 && this.grade == 2) {
										let num = Math.floor(Math.random() * (this.jumList.length))
										let url = this.jumList[num]
										let arr = []
										this.jumList.forEach(item => {
											if (this.targetImgList.indexOf(item) == -1) {
												arr.push(item)
											}
										})
										if (this.targetImgList.indexOf(url) != -1) { //存在在目标水果中
											this.boxGoImgNum++
											this.boxNoGoImgNum = 0
										} else {
											this.boxNoGoImgNum++
											this.boxGoImgNum = 0
										}
										//出现两次nogo则出现go
										if (this.boxNoGoImgNum > 1) {
											let num = Math.floor(Math.random() * (this.targetImgList.length))
											url = this.targetImgList[num]
											this.boxNoGoImgNum = 0
										}
										//出现三次go则出现nogo
										if (this.boxGoImgNum > 3) {
											let num = Math.floor(Math.random() * (arr.length))
											url = arr[num]
											this.boxGoImgNum = 0
										}
										item.style.background = `center center url(${url})  no-repeat rgba(20,122,181,0.51) `
										item.showtime = new Date().getTime()
									}
									if (item.offsetLeft < animal.offsetLeft - item.offsetWidth - 40 && item.children.length === 0) {
										let index = getComputedStyle(item).background.indexOf('fruit-')
										let index1 = getComputedStyle(item).background.indexOf('.png')
										let targetImg = 'fruit-' + getComputedStyle(item).background.substring(index + 6, index1)
										let tarIndex = `./static/game/${targetImg}.png`
										if (item.state === 'JumpFalse') {
											// 调用 service 层的方法
											ownerInstance.callMethod('addAnswer', {
												type: 'signalStopCount',
												tarIndex
											})
										}
										let toast = document.createElement('uni-view')
										toast.className = 'grun-oblist-item-toast'
										toast.style.background = `center center  url(${`./static/game/game-miss.png`}) no-repeat`
										toast.style.backgroundSize = "120px 36px";
										toast.style.width = '200px'
										toast.style.height = '48px'
										toast.style.position = 'absolute'
										toast.style.top = '-48px'
										toast.style.left = '50%'
										toast.style.transform = 'translateX(-50%)'
										if ((this.targetImgList.indexOf(tarIndex) == -1 && this.grade == 2) || (this.grade == 3 && this.targetImgList[0] != tarIndex)) {
											toast.style.background = `center center  url(${`./static/game/click-true.png`}) no-repeat`
											toast.style.backgroundSize = "88px 38px";
											toast.style.width = '108px'
											toast.style.height = '58px'
										}
										item.appendChild(toast)
									}
									if (this.bluLevel == 0) {
										this.bluLevel = 0.001
									}
									item.style.left = item.offsetLeft - 4.4 - (this.bluLevel * 1000) + 'px'
								}
							}
						}
						this.obsMoveInterval = requestAnimationFrame(cityMove)
					}
					cityMove()
				}

			}
		}
	}
</script>
<style>

</style>