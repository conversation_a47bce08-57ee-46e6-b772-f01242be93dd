<template>
	<view class="course">
		<swiper class="course-barrage " :disable-touch="true" :circular="true" :indicator-dots="false" :vertical="true" :autoplay="true" :interval="5000" :duration="1000">
			<swiper-item class="center" v-for="(item,index) in state.descList" :key="index">
				{{item}}
				<!-- xx今天训练15分钟，注意力排名 <text style="color: #FFFF43;">第1名</text> -->
			</swiper-item>
		</swiper>
		<image @click="back" src="/static/game/course-back.png" class="course-back" mode="widthFix"></image>
		<view class="course-top center" v-if="state.nextInfo&&!state.nextInfo.isComplete">
			<image class="course-top-img" :src="`${pdfUrl}resources/${state.nextInfo.skinUrl}`" mode="widthFix"></image>
			距离等级{{state.nextInfo.nextLevel}}还差{{state.nextInfo.time}}分钟
		</view>
		<view class="course-img" @click="goMoney">
			<image :src="`${pdfUrl}resources/${state.iconUrl}`" style="width: 96%;" mode="widthFix"></image>
			<image class="course-img-text" src="/static/game/game-iconUrl.png" mode="widthFix"></image>
			<image class="course-img-gif" v-if="state.debrisIdList.length>0" src="/static/game/game-gif.gif" mode="widthFix"></image>
		</view>
		<image src="/static/game/course-blu.png" mode="widthFix" class="course-blu" @click="open"></image>
		<image style="right: 144rpx;width: 116rpx;" src="/static/game/course-renk.png" mode="widthFix" class="course-blu" @click="navigateTo('/pages/train/rankingList')"></image>
		<view class="course-left">
			<image class="course-left-level" src="/static/game/course-level.png" mode="widthFix" @click="openLevel"></image>
			<view class="" v-if="state.trainDetail">
				<view v-for="(item,index) in state.introduceList" :key="item.minLevel">
					<view class="course-left-introduce " v-if="state.trainDetail.courseLevel<=item.maxLevel&&state.trainDetail.courseLevel>=item.minLevel">
						<view class="course-left-introduce-text1" @click="()=>openVideo(item.video)">
							<view class="course-left-introduce-text1-box center">
								<text class="course-left-introduce-text1-box-icon iconfont">&#xe614;</text>演示视频
							</view>
							<text>{{item.text1}}</text>
						</view>
						<view class="course-left-introduce-text2">
							{{item.text2}}
						</view>
					</view>
				</view>
			</view>
			<view class="course-left-list">
				<!-- 		<view class="course-left-list-data center">
					第一周
				</view> -->
				<view class="course-left-list-item">
					<view v-for="(item,index) in state.courseList.slice(0,6)" :key="item.courseLevel" class="course-left-list-item-box"
						:class="item.courseLevel===state.clickIndex?'course-left-list-item-box-click':''" @click="()=>click(item)">
						<view :class="item.grade=='2'?'course-left-list-item-box-level1':item.grade=='3'?'course-left-list-item-box-level2':''"
							:style="{color: item.courseLevel===state.clickIndex?'#fff':''}">
							课程{{item.courseLevel}}
						</view>
						<view class="course-left-list-item-box-icon">
							<!-- <text v-if="item.isCurrent==1">当前</text>
							<image src="/static/game/course-isLock.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.isLock!=1"></image> -->
							<text class="course-left-list-item-box-icon-true iconfont" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-true-click':''"
								v-if="item.state==='DONE'">&#xe619;</text>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else-if="item.state==='TODO'">&#xe602;</text>
							<text class="course-left-list-item-box-icon-text" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-text-click':''"
								v-else>{{item.sumConsumTime}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="course-left-list">
				<!-- 	<view class="course-left-list-data center">
					第二周
				</view> -->
				<view class="course-left-list-item">
					<view v-for="(item,index) in state.courseList.slice(6,12)" :key="item.dateTime" class="course-left-list-item-box"
						:class="item.courseLevel===state.clickIndex?'course-left-list-item-box-click':''" @click="()=>click(item)">
						<view :class="item.grade=='2'?'course-left-list-item-box-level1':item.grade=='3'?'course-left-list-item-box-level2':''"
							:style="{color: item.courseLevel===state.clickIndex?'#fff':''}">
							课程{{item.courseLevel}}
						</view>
						<view class="course-left-list-item-box-icon">
							<!-- 		<text v-if="item.isCurrent==1">当前</text>
							<image src="/static/game/course-isLock.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.isLock!=1"></image>
							<image src="/static/game/course-true.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.state==='DONE'"></image>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else>&#xe602;</text> -->
							<text class="course-left-list-item-box-icon-true iconfont" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-true-click':''"
								v-if="item.state==='DONE'">&#xe619;</text>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else-if="item.state==='TODO'">&#xe602;</text>
							<text class="course-left-list-item-box-icon-text" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-text-click':''"
								v-else>{{item.sumConsumTime}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="course-left-list">
				<!-- 		<view class="course-left-list-data center">
					第三周
				</view> -->
				<view class="course-left-list-item">
					<view v-for="(item,index) in state.courseList.slice(12,18)" :key="item.dateTime" class="course-left-list-item-box"
						:class="item.courseLevel===state.clickIndex?'course-left-list-item-box-click':''" @click="()=>click(item)">
						<view :class="item.grade=='2'?'course-left-list-item-box-level1':item.grade=='3'?'course-left-list-item-box-level2':''"
							:style="{color: item.courseLevel===state.clickIndex?'#fff':''}">
							课程{{item.courseLevel}}
						</view>
						<view class="course-left-list-item-box-icon">
							<!-- 	<text v-if="item.isCurrent==1">当前</text>
							<image src="/static/game/course-isLock.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.isLock!=1"></image>
							<image src="/static/game/course-true.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.state==='DONE'"></image>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else>&#xe602;</text> -->
							<text class="course-left-list-item-box-icon-true iconfont" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-true-click':''"
								v-if="item.state==='DONE'">&#xe619;</text>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else-if="item.state==='TODO'">&#xe602;</text>
							<text class="course-left-list-item-box-icon-text" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-text-click':''"
								v-else>{{item.sumConsumTime}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="course-left-list">
				<!-- 		<view class="course-left-list-data center">
					第四周
				</view> -->
				<view class="course-left-list-item">
					<view v-for="(item,index) in state.courseList.slice(18,24)" :key="item.dateTime" class="course-left-list-item-box"
						:class="item.courseLevel===state.clickIndex?'course-left-list-item-box-click':''" @click="()=>click(item)">
						<view :class="item.grade=='2'?'course-left-list-item-box-level1':item.grade=='3'?'course-left-list-item-box-level2':''"
							:style="{color: item.courseLevel===state.clickIndex?'#fff':''}">
							课程{{item.courseLevel}}
						</view>
						<view class="course-left-list-item-box-icon">
							<!-- 	<text v-if="item.isCurrent==1">当前</text>
							<image src="/static/game/course-isLock.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.isLock!=1"></image>
							<image src="/static/game/course-true.png" class="course-left-list-item-box-icon-img" mode="widthFix" v-else-if="item.state==='DONE'"></image>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else>&#xe602;</text> -->
							<text class="course-left-list-item-box-icon-true iconfont" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-true-click':''"
								v-if="item.state==='DONE'">&#xe619;</text>
							<text :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-value-click':''" class="course-left-list-item-box-icon-value iconfont"
								v-else-if="item.state==='TODO'">&#xe602;</text>
							<text class="course-left-list-item-box-icon-text" :class="item.courseLevel===state.clickIndex?'course-left-list-item-box-icon-text-click':''"
								v-else>{{item.sumConsumTime}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="course-right">
			<!-- <view class="course-right-other center" v-if="state.trainDetail&&state.trainDetail.state!='DONE'&&state.trainDetail.courseLevel!=state.trainDetail.currentDay">
				补
			</view> -->
			<view class="course-right-title">
				课程{{state.trainDetail&&state.trainDetail.courseLevel}}
				<text class="course-right-title-money">奖励脑力值：{{!state.trainDetail||!state.trainDetail.points?'暂无':state.trainDetail.points+'个'}}</text>
				<!-- <text
					class="course-right-title-text">{{!state.trainDetail?'暂无':state.trainDetail.state!='DONE'&&state.trainDetail.courseLevel!=state.trainDetail.currentDay?'未完成':state.trainDetail.state=='DONE'?'已完成':'开放中'}}</text> -->
			</view>
			<view class="course-right-time">
				已训练时长：{{!state.trainDetail||!state.trainDetail.sumConsumTime?'暂无':state.trainDetail.sumConsumTime}}
				<view class="course-right-time-update">
					更新时间
					<view class="course-right-time-update-value" @click="showOther">
						{{!state.trainDetail||!state.trainDetail.updateDateList?'暂无':state.trainDetail.updateDateList[0]}}
						<view v-if="state.showTimeOther" class="course-right-time-update-value-other" :class="state.trainDetail?'course-right-time-update-value-other-ovso':''"
							:style="{height:state.trainDetail.updateDateList.length>=6?122+'rpx':''}">
							<view v-for="(item,index) in state.trainDetail.updateDateList" :key="index">
								<text>{{item}}</text>
							</view>
						</view>
					</view>

				</view>
			</view>
			<view class="course-right-box">
				<view class="course-right-box-item">
					<view class="course-right-box-item-text">
						最高注意力
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.maxFocus?'暂无':state.trainDetail.maxFocus}}</view>
				</view>
				<view class="course-right-box-item">
					<view class="course-right-box-item-text">
						平均注意力
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.avgFocus?'暂无':state.trainDetail.avgFocus}}</view>
				</view>
				<view class="course-right-box-item" v-if="state.trainDetail&&state.trainDetail.grade>1">
					<view class="course-right-box-item-text center">
						反应成功
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.signalNormalSuccCount?'暂无':state.trainDetail.signalNormalSuccCount}}</view>
				</view>
				<view class="course-right-box-item" v-if="state.trainDetail&&state.trainDetail.grade>1">
					<view class="course-right-box-item-text center">
						反应失败
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.signalNormalFailCount?'暂无':state.trainDetail.signalNormalFailCount}}</view>
				</view>
				<view class="course-right-box-item" v-if="state.trainDetail&&state.trainDetail.grade>1">
					<view class="course-right-box-item-text center">
						抑制成功
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.signalStopSuccCount?'暂无':state.trainDetail.signalStopSuccCount}}</view>
				</view>
				<view class="course-right-box-item" v-if="state.trainDetail&&state.trainDetail.grade>1">
					<view class="course-right-box-item-text center">
						抑制失败
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.signalStopFailCount?'暂无':state.trainDetail.signalStopFailCount}}</view>
				</view>
				<view class="course-right-box-item" v-if="state.trainDetail&&state.trainDetail.grade>1">
					<view class="course-right-box-item-text">
						正确抑制率
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.signalStopRspRate?'暂无':state.trainDetail.signalStopRspRate+'%'}}</view>
				</view>
				<view class="course-right-box-item">
					<view class="course-right-box-item-text">
						做题正确率
					</view>
					<view class="course-right-box-item-value">{{!state.trainDetail||!state.trainDetail.rightRate?'暂无':state.trainDetail.rightRate+'%'}}</view>
				</view>
			</view>
			<view class="course-right-btn center" @click="go">
				开始{{state.trainDetail&&state.trainDetail.courseLevel==state.trainDetail.currentDay?'今日':''}}训练
			</view>
		</view>
		<uni-popup ref="videoPop" :is-mask-click="false" @maskClick="closePop" :animation="false">
			<video style="width: 500rpx;height: 250rpx;" :src="state.nowVideo" show-center-play-btn="true" initialTime="0" autoplay="true"></video>
		</uni-popup>
		<uni-popup ref="levelPop" :animation="false">
			<Grade @close="closeLevel" @openVide="openVideo" />
		</uni-popup>
		<GameDropoutReminder @change="close" />
		<BluConnect :status="state.openBlu" @change="close" :isSocket="false" />
	</view>
</template>

<script setup>
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		onMounted,
		reactive,
		ref,
		onUnmounted
	} from "vue";
	import {
		wsUrl,
		pdfUrl
	} from "../../common/global";
	import ws from '@/utils/websocket.js'
	import {
		navigateTo,
		showToast,
		redirectTo,
		navigateBack,
		switchTab
	} from "../../common/uniTool";
	import {
		getQryTrainDayList,
		getQryCourseTrainDetail,
		getQryNewSkinDebris,
		updateRead,
		getQryNextSkinDay,
		getqryNews,
		getqryNextLevelTime,
		getqryBroadcastList,
		initTrainPlain
	} from "../../service/game";
	import GameDropoutReminder from "./GameDropoutReminder.vue";
	import Grade from './Grade.vue'
	import BluConnect from "./BluConnect.vue";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		getqryUseCarrierSkinIcon
	} from '../../service/shopping';

	const videoPop = ref(null)
	const levelPop = ref(null)
	const helper = useHelper(); //设备仓库
	const loginStore = useLoginStore() //用户参数
	const state = reactive({
		clickIndex: '',
		openBlu: false, //打开蓝牙
		courseList: [],
		trainDetail: null,
		showTimeOther: false,
		iconUrl: '',
		introduceList: [{
			text1: '初级模式-规则：佩戴脑电设备，通过专注力驱动小动',
			text2: '物加速，当出现障碍物，请点击跳跃；专注力越高，速度越快。',
			minLevel: 1,
			maxLevel: 4,
			video: '../../static/video/game01_v.mp4'
		}, {
			text1: '中级模式-规则：当水果盲盒从屏幕右侧飘入，会在临',
			text2: '近动物头顶时打开，然后请立即完成是否起跳摘取水果的操作。',
			minLevel: 5,
			maxLevel: 14,
			video: '../../static/video/game02_v.mp4'
		}, {
			text1: '高级模式-规则：请记住目标水果和顺序，在倒数计时',
			text2: '后会隐藏，然后小动物需要按照顺序摘取目标水果。',
			minLevel: 15,
			maxLevel: 24,
			video: '../../static/video/game03_v.mp4'
		}],
		nowVideo: '',
		debrisIdList: [], //返回的皮肤碎片Ids为空代表没有新的皮肤碎片,不为空代表有
		nextInfo: null,
		descList: []
	})

	onShow(() => {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('landscape-primary');
		//#endif
		getSkin()
		getSkinDeb()
	})
	onMounted(() => {
		getList()
		getNext()
		getBroadList()
		state.clickIndex = 0
	})
	const back = () => {
		switchTab('/pages/index/index')
	}
	const getBroadList = () => {
		getqryBroadcastList({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.descList = res.data.descList
		})
	}
	const closeLevel = () => {
		levelPop.value.close()
	}
	const getSkin = () => {
		getqryUseCarrierSkinIcon({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.iconUrl = res.data.iconUrl
		})
	}
	const getNext = () => {
		getqryNextLevelTime({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.nextInfo = res.data
		})
	}
	const getSkinDeb = () => {
		getqryNews({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.debrisIdList = res.data.idList
		})
	}
	const goMoney = () => {
		if (state.debrisIdList.length > 0) {
			updateRead({
				traineeId: loginStore.qryActiveTrainee,
				idList: state.debrisIdList
			}).then(res => {
				redirectTo('/pages/game/growthPath')
				console.log(res);
			})
		} else {
			redirectTo('/pages/game/growthPath')
		}
	}
	const openVideo = (item) => {
		levelPop.value.close()
		state.nowVideo = item
		videoPop.value.open('center')
	}
	const closePop = () => {
		videoPop.value.close()
	}
	async function getList() {
		await initTrainPlain({
			traineeId: loginStore.qryActiveTrainee
		})
		getQryTrainDayList({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.courseList = res.data.trainDayDetail
			state.clickIndex = res.data.currentDay
			getDetail({
				traineeId: loginStore.qryActiveTrainee,
				grade: res.data.grade,
				courseLevel: res.data.currentDay
			})
		})
	}

	const openLevel = () => {
		levelPop.value.open()
	}
	//关掉蓝牙连接页面
	const close = (value) => {
		state.openBlu = value
	}
	//打开蓝牙连接页面
	const open = () => {
		console.log('打开');
		state.openBlu = true
	}
	const go = () => {
		if (!helper.address) {
			open()
			return
		}
		const level = state.trainDetail.grade
		const courseLevel = state.trainDetail.courseLevel
		redirectTo(`/pages/game/index?level=${level}&courseLevel=${courseLevel}`)
	}
	const getDetail = (param) => {
		getQryCourseTrainDetail(param).then(res => {
			state.trainDetail = res.data
			state.clickIndex = param.courseLevel
		})
	}
	const showOther = () => {
		if (!state.trainDetail.updateDateList) {
			return
		}
		state.showTimeOther = !state.showTimeOther
	}
	const click = (value) => {
		if (value.isLock == 0) {
			showToast('当前课程未解锁~')
			return
		}
		state.showTimeOther = false
		let param = {
			traineeId: loginStore.qryActiveTrainee,
			grade: value.grade,
			courseLevel: value.courseLevel
		}
		getDetail(param)
	}
</script>

<style lang="scss">
	.course {
		width: 100vw;
		height: 100vh;
		background: url('~@/static/game/course-bg.png');
		background-repeat: no-repeat;
		background-size: 100vw auto;
		padding: 100rpx 54rpx 80rpx 68rpx;
		display: flex;
		justify-content: space-between;
		position: relative;

		&-back {
			position: absolute;
			top: 12rpx;
			left: 2%;
			width: 30rpx;
		}

		&-barrage {
			position: absolute;
			bottom: 10rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 449rpx;
			height: 30rpx;
			background: linear-gradient(rgba(30, 91, 149, 1) 0%, rgba(25, 80, 133, 1) 100%);
			box-shadow: inset 0rpx 1 5rpx 0rpx #074178;
			border-radius: 5rpx;
			border: 1rpx solid #A7D9FF;
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: 500;
			font-size: 15rpx;
			color: #FFFFFF;
		}

		&-top {
			width: 313rpx;
			height: 41rpx;
			background: linear-gradient(90deg, rgba(50, 147, 225, 0) 0%, #3293E1 13%, #3293E1 85%, rgba(50, 147, 225, 0) 100%);
			position: absolute;
			top: 12rpx;
			left: 10%;
			font-weight: bold;
			font-size: 20rpx;
			color: #FFFFFF;

			&-img {
				width: 96px;
				height: 50px;
				margin-right: 12rpx;
			}
		}

		&-money {
			width: 54rpx;
			position: absolute;
			bottom: 14rpx;
			left: 120rpx;
		}

		&-list {
			display: flex;
			flex-direction: column;
			width: 514rpx;
			height: 271rpx;
			background: url('~@/static/game/course-pop-img.png');
			background-repeat: no-repeat;
			background-size: 514rpx 271rpx;
			border-radius: 25rpx;
			padding: 36rpx;

			&-title {
				font-size: 20rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
				margin-bottom: 32rpx;

				&-icon {
					color: #FF4222;
					margin-left: 10rpx;
				}
			}

			&-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 32rpx;

				&-left {
					display: flex;
					align-items: center;
					flex: 1;

					&-img {
						width: 34rpx;
						height: 34rpx;
					}

					&-address {
						font-size: 15rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						margin-left: 24rpx;
						margin-right: 16rpx;

					}

					&-status {
						border-radius: 4rpx;
						border: 1rpx solid #111111;
						font-size: 11rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						padding: 2rpx 5px;
					}
				}

				&-rignt {
					background: #FFE4BE;
					border-radius: 27rpx;
					font-size: 15rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #974E2B;
					padding: 10rpx 28rpx;
				}
			}
		}

		&-blu {
			width: 120rpx;
			height: 40rpx;
			position: absolute;
			top: 12rpx;
			right: 14rpx;
			z-index: 1;
		}

		&-img {
			width: 104rpx;
			position: absolute;
			bottom: 0rpx;
			left: 11rpx;
			z-index: 1;

			&-gif {
				position: absolute;
				width: 64px;
				right: -14rpx;
				top: 20rpx;
			}

			&-text {
				width: 62rpx;
				position: absolute;
				bottom: 5rpx;
				left: 50%;
				transform: translateX(-50%);
				z-index: 999;
			}
		}

		&-right {
			// padding-left: 20rpx;
			width: 48%;
			position: relative;
			height: 296rpx;

			&-other {
				position: absolute;
				right: 20rpx;
				top: -6rpx;
				width: 37rpx;
				height: 37rpx;
				background: #CFEEFF;
				border-radius: 18rpx;
				font-size: 20rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #267EC4;
			}

			&-btn {
				width: 186rpx;
				height: 43rpx;
				background: #FF9C1B;
				box-shadow: 0rpx 3rpx 0rpx 0rpx rgba(255, 98, 4, 1);
				border-radius: 15rpx;
				font-size: 25rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #FFFFFF;
				line-height: 38rpx;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 6rpx;
			}

			&-time {
				font-size: 12rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #B26034;
				margin-top: 10rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-right: 14rpx;

				&-update {
					display: flex;

					&-value {
						padding: 0 3rpx;
						margin-left: 4rpx;
						border-radius: 3rpx;
						border: 1rpx solid #B26034;
						position: relative;

						&-other {
							background: #582912;
							border-radius: 5rpx;
							bottom: 24rpx;
							left: 0;
							position: absolute;
							width: 114rpx;
							background: #582912;
							border-radius: 5rpx;
							z-index: 999;
							display: flex;
							flex-direction: column;
							padding: 4rpx 6rpx;
							font-size: 11rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #FFFFFF;
							line-height: 20rpx;

							&-ovso {
								overflow: hidden;
								overflow-y: auto;
							}
						}

						&-other::after {
							position: absolute;
							bottom: -13rpx;
							left: 50%;
							transform: translateX(-50%);
							content: ' ';
							border: 10px solid transparent;
							border-top-color: #582912;
						}
					}
				}
			}

			&-box {
				display: flex;
				flex-wrap: wrap;
				font-size: 15rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #111111;

				&-item {
					width: 50%;
					margin-top: 20rpx;
					display: flex;
					align-items: center;

					&-text {
						width: 75rpx;
					}

					&-value {
						width: 56rpx;
						height: 23rpx;
						background: #FDD9A8;
						box-shadow: 0rpx 1rpx 1rpx 0rpx rgba(255, 253, 253, 1), inset 0rpx 1rpx 2rpx 0rpx rgba(221, 165, 88, 1);
						border-radius: 5rpx;
						line-height: 23rpx;
						padding-left: 5rpx;
						margin-left: 5rpx;
						font-size: 13rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #A3663B;
					}
				}
			}

			&-title {
				font-size: 20rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: bold;
				color: #111111;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-right: 14rpx;

				&-money {
					font-size: 15rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #111111;
				}

				&-text {
					width: 50rpx;
					height: 20rpx;
					background: #FFF4E5;
					border-radius: 5rpx;
					border: 1rpx solid #B26034;
					font-size: 13rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #B26034;
					margin-left: 10rpx;
					text-align: center;
				}
			}
		}

		&-left {
			display: flex;
			flex-direction: column;
			width: 300rpx;
			position: relative;

			// margin-top: 34rpx;
			&-level {
				position: absolute;
				bottom: -16rpx;
				right: 0;
				width: 190rpx;
			}

			&-introduce {
				width: 100%;
				height: 34rpx;
				background: #E8F7FF;
				border-radius: 3rpx;
				font-size: 10rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #268BDE;
				display: flex;
				flex-direction: column;
				justify-content: center;
				padding-left: 5rpx;

				&-text1 {
					display: flex;
					align-items: center;

					&-box {
						width: 50rpx;
						height: 15rpx;
						background: linear-gradient(180deg, #FFB85C 0%, #FF6F18 100%);
						border-radius: 3rpx;
						font-size: 9rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #FFFFFF;
						margin-right: 2rpx;
					}
				}

				&-video {
					display: flex;
					align-items: center;
				}
			}

			&-list {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				border: 1rpx solid #E3E3E3;
				border-radius: 5rpx;
				overflow: hidden;
				margin-top: 8rpx;

				&-data {
					font-size: 13rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
					width: 100%;
					height: 18rpx;
					background: #EEEEEE;
				}

				&-item {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-around;
					font-size: 13rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					height: 46rpx;


					&-box {
						display: flex;
						flex-direction: column;
						align-items: center;
						color: #007BD9;
						justify-content: space-around;
						height: 100%;
						width: 44rpx;
						border-radius: 5rpx;

						&-icon {
							font-size: 10rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: bold;
							color: #909090;

							&-text {
								padding: 2rpx 5rpx;
								height: 15rpx;
								border-radius: 5rpx;
								border: 1rpx solid #111111;
								font-size: 10rpx;
								font-family: SourceHanSansCN-Medium, SourceHanSansCN;
								font-weight: 500;
								color: #111111;

								&-click {
									color: #FFFFFF;
									border-color: #FFFFFF;
								}
							}

							&-true {
								font-size: 14rpx;
								color: #00B83C;

								&-click {
									color: #FFFFFF;
								}
							}

							&-value {
								font-size: 20rpx;

								&-click {
									color: #FFFFFF;
								}
							}

							&-img {
								width: 12rpx;
							}
						}

						&-level1 {
							color: #FF9C1B;
						}

						&-level2 {
							color: #FF5656;
						}

						&-click {
							background: #111111;
							color: #FFFFFF;
						}
					}
				}
			}
		}
	}
</style>