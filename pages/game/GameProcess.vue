<template>
	<view class="gameProcess">
		<view class="gameProcess-value" :style="{height: 42+props.focus*3.9+'px'}">
		</view>
		<view class="gameProcess-bg">
			{{props.focus}}
		</view>
	</view>
</template>

<script setup>
	const props = defineProps(['focus'])
</script>

<style lang="scss">
	.gameProcess {
		width: 54px;
		height: 500px;
		position: absolute;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		z-index: 1;
		top: 50%;
		transform: translateY(-40%);
		background: #fff;
		border-radius: 28rpx;
		left: 20rpx;
		overflow: hidden;

		&-value {
			width: 16rpx;
			height: 0;
			background: #FF943B;
			border-radius: 28rpx;
			position: absolute;
			bottom: 10px;
			left: 50%;
			transform: translateX(-50%);
		}

		&-bg {
			font-size: 20rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #8D3F1E;
			width: 54px;
			height: 500px;
			background: url('~@/static/game/game-process.png');
			background-repeat: no-repeat;
			background-size: 54px 500px;
			position: absolute;
			left: 0;
			top: 0;
			text-align: center;
			padding-top: 16px;
		}
	}
</style>