<template>
	<view class="grade">
		<image src="/static/game/grade-close.png" class="grade-close" mode="widthFix" @click="close"></image>
		<view class="grade-list" @click="hideTip">
			<view class="grade-list-item">
				<image class="grade-list-item-img" @click="open(item.video)" :src="item.img" v-for="(item,index) in state.gradelist" :key="item.title" mode="widthFix"></image>
			</view>
			<view class="grade-list-box">
				<view class="grade-list-box-item" :class="!index?'grade-list-box-item-blue':index===2?'grade-list-box-item-red':''" v-for="(item,index) in state.tip" :key="item">
					<view class="grade-list-box-item-title" :class="!index?'grade-list-box-item-title-blue':index===2?'grade-list-box-item-title-red':''">
						<text style="font-size: 14rpx;margin-right: 2rpx;" class="iconfont">&#xe62e;</text> {{item.title}}
					</view>
					<view class="grade-list-box-item-tip">
						<view class="grade-list-box-item-tip-item center" @click.stop="()=>showTip(item1)" v-for="(item1,index1) in state.tip[index].tip" :key="item1.index">
							<view class="grade-list-box-item-tip-item-other" :style="`top: ${state.tipHeight}px`" v-if="item1.index===state.showIndex" :id="`tip-${item1.index}`">
								<view class="grade-list-box-item-tip-item-other-title">
									{{item1.name}}
								</view>
								<view class="grade-list-box-item-tip-item-other-value">
									{{item1.value}}
								</view>
							</view>
							{{item1.name}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive
	} from "vue";
	import {
		navigateTo,
		showToast
	} from "../../common/uniTool";
	import {
		initTrainPlain
	} from "../../service/game";
	import {
		useLoginStore
	} from "../../stores/login";
	const loginStore = useLoginStore() //用户参数
	const event = defineEmits(['close', 'openVide'])
	const close = () => {
		event('close', true)
	}
	const open = (item) => {
		event('openVide', item)
	}
	const state = reactive({
		visible: false,
		tipHeight: -27,
		showIndex: -1,
		gradelist: [{
				img: '/static/game/grade-one.png',
				video: '/static/video/game01_v.mp4'
			},
			{
				img: '/static/game/grade-two.png',
				video: '/static/video/game02_v.mp4'
			},
			{
				img: '/static/game/grade-three.png',
				video: '/static/video/game03_v.mp4'
			}
		],
		tip: [{
				title: '初级—训练2个技能',
				tip: [{
						name: '注意力集中',
						value: '集中在我们周围的刺激上，使我们保持专注',
						index: '1'
					},
					{
						name: '估测',
						value: '有助于更准确、更快的计算距离、时间、速度、空间等，及时我们没有明确的坐标数值参考',
						index: '2'
					}
				]
			},
			{
				title: '中级—训练8个技能',
				tip: [{
						name: '反应时',
						value: '良好的反应时与充分的反应有关，有助于我们有效应对不可预见的或新的情况',
						index: '3'
					},

					{
						name: '估测',
						value: '有助于更准确、更快的计算距离、时间、速度、空间等，及时我们没有明确的坐标数值参考',
						index: '4'
					},
					{
						name: '识别',
						value: '有助于更准确的记住最近学到的信息，对于考试和日常生活中必不可少',
						index: '5'
					},
					{
						name: '非语言记忆',
						value: '使大脑能够保留视觉或听觉信息，有助于管理一般不包含文字或语言内容的信息',
						index: '6'
					},

					{
						name: '手眼协调',
						value: '良好的手眼协调，能帮助我们进行精确的动作和物体操纵，这与运动中的良好表现有关',
						index: '7'
					},
					{
						name: '抑制',
						value: '与更好的运动，注意力和行为水平的自我调节有关，这可以使执行功能更好地发挥作用',
						index: '8'
					},
					{
						name: '注意力分配',
						value: '使人能够充分执行高度复杂的活动，允许我们一次完成多项任务，或者一次完成多项刺激',
						index: '9'
					},
					{
						name: '扫视',
						value: '良好的视觉扫描有助于我们定位周围的物体或文本中的文字，有助于搜索和查找事物时更有效率',
						index: '10'
					}
				],
			},
			{
				title: '高级—训练9个技能',
				tip: [{
						name: '短期记忆',
						value: '良好的短期记忆，可以让大脑在有限但灵活的时间段内保留大量信息',
						index: '11'
					},
					{
						name: '瞬时记忆',
						value: '使大脑能够积极操纵保留的信息，对于读、说、计算或作出决定至关重要',
						index: '12'
					},
					{
						name: '反应时',
						value: '良好的反应时与充分的反应有关，有助于我们有效应对不可预见的或新的情况',
						index: '13'
					},
					{
						name: '更新',
						value: '使大脑能够监控自己的行为，有助于发信错误，以便及时纠正',
						index: '14'
					},
					{
						name: '扫视',
						value: '良好的视觉扫描有助于我们定位周围的物体或文本中的文字，有助于搜索和查找事物时更有效率',
						index: '15'
					},
					{
						name: '短期视觉记忆',
						value: '使大脑储存更多的视觉信息，有助于学习通过眼睛接收到的信息',
						index: '16'
					},
					{
						name: '非语言记忆',
						value: '使大脑能够保留视觉或听觉信息，有助于管理一般不包含文字或语言内容的信息',
						index: '17'
					},
					{
						name: '视野宽度',
						value: '有助于我们一目了然的感知更多的视觉信息，使我们在环境中寻找某些东西时更有效率',
						index: '18'
					},
					{
						name: '注意力分配',
						value: '使人能够充分执行高度复杂的活动，允许我们一次完成多项任务，或者一次完成多项刺激',
						index: '19'
					}
				]
			}
		]
	})
	const hideTip = () => {
		state.showIndex = -1
	}
	const getTipHeight = (showIndex) => {
		//获取元素信息
		let info = uni.createSelectorQuery().select(`#tip-${showIndex}`);
		info.boundingClientRect(function (data) { //data - 各种参数
			// console.log(data.height) // 获取元素宽度
			state.tipHeight = -data.height - 15
		}).exec()

	}
	const showTip = (item) => {
		state.showIndex = item.index
		setTimeout(() => getTipHeight(item.index), 1)
	}
	const go = () => {
		initTrainPlain({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			console.log(res);
			navigateTo('/pages/game/course')
		})
	}
	// onMounted(() => {
	// 	// #ifdef APP-PLUS
	// 	plus.screen.unlockOrientation();
	// 	plus.screen.lockOrientation('landscape-primary');
	// 	//#endif
	// })
</script>

<style lang="scss">
	.grade {
		width: 90vw;
		height: 88vh;
		background: url('~@/static/game/grade-bg.png');
		background-repeat: no-repeat;
		background-size: 100vw;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
		position: relative;

		&-close {
			position: absolute;
			right: -25rpx;
			top: -25rpx;
			width: 50rpx;
			z-index: 1;
		}

		&-btn {
			width: 180rpx;
			right: 58rpx;
			top: 18rpx;
			position: absolute;
		}

		&-list {
			width: 94%;
			display: flex;
			align-items: center;
			flex-direction: column;

			&-item {
				width: 100%;
				display: flex;
				justify-content: space-around;

				&-img {
					width: 192rpx;
				}
			}

			&-box {
				width: 100%;
				display: flex;
				justify-content: space-around;
				margin-top: 6rpx;

				&-item {
					width: 192rpx;
					padding: 4rpx;
					background: #F9D4A6;
					border-radius: 5rpx;
					padding-bottom: 0;

					&-blue {
						background: #D3E4F5;
					}

					&-red {
						background: #FADFDF;
					}

					&-title {
						font-size: 13rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #582912;
						display: flex;
						align-items: center;
						margin-bottom: 4rpx;

						&-blue {
							color: #007BD9;
						}

						&-red {
							color: #E41818;
						}
					}

					&-tip {
						display: flex;
						flex-wrap: wrap;
						width: 100%;

						&-item {
							padding: 0 10rpx;
							height: 20rpx;
							background: #FFFFFF;
							border-radius: 3rpx;
							font-size: 13rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: 500;
							color: #111111;
							margin-right: 5rpx;
							margin-bottom: 4rpx;
							position: relative;

							&-other {
								display: flex;
								flex-direction: column;
								flex-wrap: wrap;
								padding: 10rpx;
								position: absolute;
								left: 50%;
								transform: translateX(-50%);
								background: #582912;
								border-radius: 5rpx;
								font-size: 10rpx;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #FFFFFF;
								width: 162rpx;
								z-index: 99;

								&-title {
									font-size: 13rpx;
									font-family: SourceHanSansCN-Medium, SourceHanSansCN;
									font-weight: 500;
									color: #FFFFFF;
									margin-bottom: 10rpx;
								}
							}

							&-other::after {
								content: ' ';
								position: absolute;
								left: 50%;
								bottom: -16rpx;
								transform: translateX(-50%);
								border: 8rpx solid transparent;
								border-top-color: #582912;
							}
						}


					}
				}
			}
		}
	}
</style>