<template>
	<uni-popup ref="reward" :mask-click="false" :animation="false" class="reward" v-if="state.questionIndex<10&&state.countdown>0">
		<image class="reward-bg" src="../../static/game/game-arithmetic-bg.png"></image>
		<view class="reward-box">
			<view class="reward-box-info">
				<view class="reward-box-info-text center">
					本题计时：{{state.nowTime}}秒
				</view>
				<view class="reward-box-info-text center">
					平均用时：{{state.avgTime}}秒
				</view>
				<view class="reward-box-info-text center">
					答题数：{{state.questionIndex+1}}/10题
				</view>
				<view class="reward-box-info-text center">
					倒计时：{{state.countdown}}秒
				</view>
			</view>
			<view class="reward-box-question">
				<view v-for="(item,index) in state.questionList[state.questionIndex]['question']" :key="index">
					<view class="reward-box-question-value center" v-if="item==='x'">
						<text class="center" :style="{width:state.clicked.length*12+100+'rpx'}" v-if="state.questionList[state.questionIndex]['option'].length===4">{{state.clicked}}</text>
						<text v-else class="center"
							:style="{width:state.clickedList.length>0?state.clickedList[0].length*12+100+'rpx':100+'rpx'}">{{index===0?state.clickedList[0]:state.clickedList[1]}}</text>
					</view>
					<view class="" v-else>
						{{item}}
					</view>
				</view>
			</view>
			<view class="reward-box-answer" v-if="state.questionList[state.questionIndex]['option'].length===4">
				<view class="reward-box-answer-btn center" :class="state.clicked===item?'reward-box-answer-btn-click':''" @click="()=>getAnswer(item)"
					v-for="(item,index) in state.questionList[state.questionIndex]['option']" :key="item">
					{{item}}
				</view>
			</view>
			<view class="reward-box-answer" v-else>
				<view class="reward-box-answer-btn center" :class="state.clickedList.includes(item)?'reward-box-answer-btn-click':''" @click="()=>getAnswerList(item)"
					v-for="(item,index) in state.questionList[state.questionIndex]['option']" :key="item">
					{{item}}
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		getRandomQuestion
	} from "../../service/game";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		useHelper
	} from "../../stores/helper";
	const helper = useHelper(); //设备仓库

	const loginStore = useLoginStore()
	const props = defineProps(['open', 'speed'])
	const reward = ref(null)
	const event = defineEmits(['change'])
	const milliseconds = ref(0)

	const timerRef = ref(null) //当前时间计时器
	const countdownRef = ref(null) //倒计时计时器
	const state = reactive({
		answer: [], //答案
		nowTime: 0, //本题计时
		avgTime: 0, //平均时间
		nowTimeList: [], //计时列表
		questionList: [], //题目列表
		questionIndex: 0, //题号
		clicked: '',
		countdown: 90,
		isClick: true,
		isFirstDrop: 0, //第一次脱落暂停 连接好继续计时
		clickedList: [], //当8个答案时候会出现两个选项
	})
	onMounted(() => {
		getQuestion()
	})
	onUnmounted(() => {
		console.log('销毁');
		if (countdownRef.value) {
			clearInterval(countdownRef.value)
			countdownRef.value = null
		}
		if (timerRef.value) {
			clearInterval(timerRef.value)
			timerRef.value = null
		}
	})
	watch([() => helper.bluData, () => props.open], ([bluData, open]) => {
		if (bluData) {
			if (bluData.type == 5 || bluData.signal) {
				// console.log('信号暂停');
				state.isFirstDrop = 0
				if (countdownRef.value) {
					clearInterval(countdownRef.value)
					countdownRef.value = null
				}
				if (timerRef.value) {
					clearInterval(timerRef.value)
					timerRef.value = null
				}
			} else {
				if (open) {
					state.isFirstDrop++
				}
				if (state.isFirstDrop === 1) {
					//开始计时器
					getCountdown()
					getTimer()
				}
			}
		} else {
			getCountdown()
			getTimer()
		}

	})
	watch(() => props.open, (open) => {
		if (open) {
			reward.value.open()
		} else {
			reward.value.close()
		}
	})
	const getCountdown = () => {
		countdownRef.value = setInterval(() => {
			state.countdown--
		}, 1000)
	}
	watch(() => state.countdown, (countdown) => {
		if (countdown === 0) {
			// reward.value.close()
			clearInterval(countdownRef.value)
			countdownRef.value = null
			if (timerRef.value) {
				clearInterval(timerRef.value)
				timerRef.value = null
			}
			event('change', state.answer)
			console.log('结束');
		}
	})
	const getQuestion = () => {
		getRandomQuestion({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.questionList = res.data
		})
	}
	const getAnswerList = (value) => {
		if (!state.isClick) {
			return
		}
		state.isClick = false
		state.clickedList.push(value)
		if (state.clickedList.length === 2) {
			state.answer.push({
				questionId: state.questionList[state.questionIndex]['questionId'],
				doneFlage: 1,
				select: state.clickedList.join(','),
				consumTime: Number(state.nowTime)
			})
			state.nowTimeList.push(Number(state.nowTime))
			setTimeout(() => {
				reset()
				state.isClick = true
			}, 250)
		} else {
			state.isClick = true
		}
	}
	const getAnswer = (value) => {
		if (!state.isClick) {
			return
		}
		state.isClick = false
		state.answer.push({
			questionId: state.questionList[state.questionIndex]['questionId'],
			doneFlage: 1,
			select: value,
			consumTime: Number(state.nowTime)
		})
		state.nowTimeList.push(Number(state.nowTime))
		state.clicked = value
		setTimeout(() => {
			reset()
			state.isClick = true
		}, 250)

	}
	const getTimer = () => {
		timerRef.value = setInterval(() => {
			// 将计时器的间隔时间累加到总毫秒数上
			milliseconds.value += 1;

			// 将毫秒数转换为秒数并保留两位小数
			const seconds = (milliseconds.value / 1000).toFixed(2);

			// 获取整数部分的秒数和小数部分的毫秒数
			const [intPart, decimalPart] = seconds.split('.');

			// 格式化为 秒.毫秒 的字符串形式
			const formattedTime = `${intPart}.${decimalPart.padEnd(2, '0')}`;
			state.nowTime = formattedTime
			// 输出格式化后的时间
		}, 1);
	}
	const reset = () => {
		if (timerRef.value) {
			clearInterval(timerRef.value)
			timerRef.value = null
		}
		state.questionIndex++
		milliseconds.value = 0
		state.nowTime = 0
		state.clicked = ''
		state.clickedList = []
		getTimer()
	}
	watch(() => state.questionIndex, (questionIndex) => {
		console.log(questionIndex);
		if (questionIndex === 10) {
			clearInterval(countdownRef.value)
			countdownRef.value = null
			if (timerRef.value) {
				clearInterval(timerRef.value)
				timerRef.value = null
			}
			// reward.value.close()
			event('change', state.answer)
		}
	})
	watch(() => state.nowTime, (nowTime) => {
		if (nowTime === '15.00') {
			state.answer.push({
				questionId: state.questionList[state.questionIndex]['questionId'],
				doneFlage: 0,
				select: null,
				consumTime: Number(state.nowTime)
			})
			state.nowTimeList.push(Number(state.nowTime))
			reset()
		}
	})
	watch(() => state.nowTimeList, (nowTimeList) => {
		// 计算数组元素的总和
		const sum = nowTimeList.reduce((acc, current) => acc + current, 0);
		// 计算平均数
		const average = sum / nowTimeList.length;
		state.avgTime = average.toFixed(2)
	}, {
		deep: true
	})
</script>

<style lang="scss">
	.reward {
		height: 100vh;
		width: 100vw;

		&-bg {
			width: 100vw;
			height: 100vh;
		}

		&-box {
			position: absolute;
			top: 20%;
			left: 50%;
			transform: translateX(-50%);
			width: 80%;
			height: 64%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			&-answer {
				display: flex;
				width: 80%;
				justify-content: space-between;
				flex-wrap: wrap;

				&-btn {
					width: 24%;
					height: 45rpx;
					background: #FFFFFF;
					border-radius: 9rpx;
					border: 1rpx solid #216932;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #3D7D4C;
					margin-bottom: 20rpx;

					&-click {
						background: #E3FFEA;
						border: 3rpx solid #239E40;
						color: #3D7D4C;
					}
				}
			}

			&-question {
				flex: 1;
				font-size: 63rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 94rpx;
				display: flex;
				align-items: center;

				&-value {
					height: 75rpx;
					border-radius: 9rpx;
					border: 3rpx solid #FFFFFF;
					font-size: 63rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #FFFFFF;
					margin-left: 10rpx;
					padding: 0 10rpx;
				}
			}

			&-info {
				width: 96%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 15rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;

				&-text {
					width: 25%;

				}
			}
		}

	}
</style>