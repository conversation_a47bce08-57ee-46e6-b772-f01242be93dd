<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="gameQuesEcharts.onClick" :gameTimeDate="gameTimeDate" :change:gameTimeDate="gameQuesEcharts.updateEcharts" id="gameQuesEcharts" class="gameQuesEcharts"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: 'Echarts',
		props: {
			gameTimeDate: {
				type: Object
			}
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="gameQuesEcharts" lang="renderjs">
	let data = []
	let dataX = []
	let myChart
	export default {
		data() {
			return {
				option: {
					grid: {
						left: '8%',
						right: '4%',
						bottom: '10%',
						top: '8%',
						height: "70%",
					},
					xAxis: {
						type: 'category',
						data: dataX
					},
					yAxis: {
						type: 'value',
						nameTextStyle: {
							fontSize: 6
						},
						min: 0,
						splitNumber: 2
					},
					series: [{
						type: 'line',
						smooth: true,
						data: data,
					}]
				}
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('gameQuesEcharts'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				console.log(":newValue " + JSON.stringify(newValue));

				if (newValue) {
					this.option.xAxis.data = newValue.time
					this.option.series[0].data = newValue.data
					// 监听 service 层数据变更
					myChart && myChart.setOption(this.option)
				}
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		flex: 1;
	}

	.gameQuesEcharts {
		width: 100%;
		height: 58rpx;
	}
</style>