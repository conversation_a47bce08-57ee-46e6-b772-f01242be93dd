<template>
	<!-- <image class="path-back" @click="back" src="/static/game/game-path-back.png" mode="widthFix"></image> -->
	<scroll-view scroll-x="true" scroll-y="false" v-if="state.show">
		<image class="path-money" src="/static/game/growthPath-start.png" mode="widthFix" @click="goCour"></image>
		<view class="path">
			<image src="/static/game/growth-path-bg.png" class="path-bg"></image>
			<image src="/static/game/growth-path-line.png" class="path-line" mode="heightFix"></image>
			<view class="path-box" v-if="state.defaultSkin" style="position: absolute;margin-top: 70rpx;margin-left: 38rpx;">
				<image :src="`${pdfUrl}resources/${state.defaultSkin.skinUrl}`" class="path-box-ani path-box-ani-rab" mode="widthFix"></image>
				<image src="/static/game/growth-path-stage.png" class="path-box-sta " mode="widthFix"></image>
				<image :src="`/static/game/level/level-${state.defaultSkin.level}.png`" class="path-box-level" mode="widthFix"></image>
				<image v-if="state.firstIn" src="/static/game/growth-tips.png" class="path-box-tips" mode="widthFix"></image>
				<view class="path-box-btn  center" :class="state.defaultSkin.useFlag==0?'path-box-btn-use':''" @click="()=>changeSkin(state.defaultSkin)">
					{{state.defaultSkin.useFlag==0?'使用':'当前'}}
				</view>
			</view>
			<view class="path-list path-list-top" :style="{marginLeft:windowWidth>1140&&windowWidth<=1146?'290rpx':''}">
				<view class="path-box "
					:class="[index>8&&index<11?'path-box-right1':(index>3&&index<=8)||index>=11?'path-box-right':'',windowWidth>1140&&windowWidth<=1146&&index>8&&index<11?'path-box-right2':(windowWidth>1140&&windowWidth<=1146&&index>=0&&index<=8)||(windowWidth>1140&&windowWidth<=1146&&index>=11)?'path-box-right3':'']"
					v-for="(item,index) in state.skinList.filter((item, index) => index % 2 !== 0)" :key="item.carrierId">
					<!-- 	<view class="path-box-img" v-if="item.skinDebrisList.length>0&&item.gainState==='DOING'">
						<image class="path-box-img-item" v-for="(item1,index) in item.skinDebrisList" @click="()=>convert(item1,item)" :src="`${pdfUrl}resources/${item1.debrisUrl}`" mode="widthFix">
						</image>
					</view>
					<image :src="`${pdfUrl}resources/${item.skinUrl}`" v-else class="path-box-ani path-box-ani-rab" mode="widthFix"></image> -->
					<image :src="`${pdfUrl}resources/${item.skinUrl}`" v-if="item.skinUrl" class="path-box-ani" mode="widthFix"></image>
					<image src="/static/game/path-default.png" v-else class="path-box-ani" mode="widthFix"></image>
					<image src="/static/game/growth-path-stage.png" class="path-box-sta" mode="widthFix"></image>
					<image :src="`/static/game/level/level-${item.level}.png`" class="path-box-level" mode="widthFix"></image>
					<view class="path-box-btn path-box-btn-wite center" v-if="item.summonFlag==='0'&&!item.summonCardId">
						等待召唤
					</view>
					<view class="center path-box-btn" v-else :class="item.useFlag==='1'?'path-box-btn':'path-box-btn-use'" @click="()=>changeSkin(item)">
						{{item.summonFlag==='0'&&item.summonCardId?'点击召唤':item.useFlag==0&&item.summonFlag?'使用':'当前'}}
					</view>
					<!-- 	<view class="path-box-btn  center" v-if="item.gainState==='DONE'" :class="item.useFlag==0?'path-box-btn-use':''" @click="()=>changeSkin(item)">
						{{item.useFlag==0?'使用':'当前'}}
					</view>
					<view class="path-list-text center" v-else>
						累计<text style="color: #FF0000;">{{item.planDay}}天</text>，且每天{{item.planTrainTime}}分钟以上自动解锁，并奖励<text style="color: #FF8E00;">{{item.point}}脑力值</text>
					</view>
					<image src="../../static/game/growth-path-five.gif" v-if="item.handFlage==1" mode="widthFix" class="path-list-five"></image> -->
				</view>
			</view>
			<view class="path-list path-list-bottom" :style="{marginLeft:windowWidth>1140&&windowWidth<=1146?'148rpx':''}">
				<view class="path-box "
					:class="[index>8&&index<11?'path-box-right1':(index>3&&index<=8)||index>=11?'path-box-right':'',windowWidth>1140&&windowWidth<=1146&&index>8&&index<11?'path-box-right2':(windowWidth>1140&&windowWidth<=1146&&index>=0&&index<=8)||(windowWidth>1140&&windowWidth<=1146&&index>=11)?'path-box-right3':'']"
					v-for="(item,index) in state.skinList.filter((item, index) => index % 2 === 0)" :key="item.carrierId">
					<!-- 			<view class="path-box-img" v-if="item.skinDebrisList.length>0&&item.gainState==='DOING'">
						<image class="path-box-img-item" v-for="(item1,index) in item.skinDebrisList" @click="()=>convert(item1,item)" :src="`${pdfUrl}resources/${item1.debrisUrl}`" mode="widthFix">
						</image>
					</view> -->
					<!-- <image :src="`${pdfUrl}resources/${item.skinUrl}`"   class="path-box-ani path-box-ani-rab" mode="widthFix"></image> -->
					<image :src="`${pdfUrl}resources/${item.skinUrl}`" v-if="item.skinUrl" class="path-box-ani" mode="widthFix"></image>
					<image src="/static/game/path-default.png" v-else class="path-box-ani" mode="widthFix"></image>
					<image src="/static/game/growth-path-stage.png" class="path-box-sta " mode="widthFix"></image>
					<image :src="`/static/game/level/level-${item.level}.png`" class="path-box-level" mode="widthFix"></image>
					<view class="path-box-btn path-box-btn-wite center" v-if="item.summonFlag==='0'&&!item.summonCardId">
						等待召唤
					</view>
					<view class="center path-box-btn" v-else :class="item.useFlag==='1'?'path-box-btn':'path-box-btn-use'" @click="()=>changeSkin(item)">
						{{item.summonFlag==='0'&&item.summonCardId?'点击召唤':item.useFlag==0&&item.summonFlag?'使用':'当前'}}
					</view>
					<!-- <view class="path-box-btn  center" v-if="item.gainState==='DONE'" :class="item.useFlag==0?'path-box-btn-use':''" @click="()=>changeSkin(item)">
						{{item.useFlag==0?'使用':'当前'}}
					</view>
					<view class="path-list-text center" v-else>
						累计<text style="color: #FF0000;">{{item.planDay}}天</text>，且每天{{item.planTrainTime}}分钟以上自动解锁，并奖励<text style="color: #FF8E00;">{{item.point}}脑力值</text>
					</view>
					<image src="../../static/game/growth-path-five.gif" v-if="item.handFlage==1" mode="widthFix" class="path-list-five"></image> -->
				</view>
			</view>
		</view>
	</scroll-view>
	<uni-popup ref="popup" type="center" class="reward" mask-background-color="rgba(0,0,0,0.7)">
		<image class="reward-bg" src="/static/game/growth-light.png" mode="widthFix"></image>
		<view class="reward-box" :class="state.debrisOpen.convertCount>=3?'reward-box-top':''">
			<image :src="`${pdfUrl}resources/${state.debrisOpen.debrisUrl}`" :class="state.debrisOpen.convertCount>=3?'reward-box-img-fin':''" class="reward-box-img" mode="widthFix"></image>
			<view class="reward-box-text">
				{{state.debrisOpen.desc}}
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		onShow,
		onReady
	} from '@dcloudio/uni-app'
	import {
		onMounted,
		reactive,
		ref,
		onUnmounted
	} from "vue";
	import {
		pdfUrl
	} from '@/common/global';
	import {
		navigateBack,
		showToast,
		redirectTo,
		navigateTo,
		getStorageSync,
		setStorage,
		showLoading,
		hideLoading
	} from '@/common/uniTool';
	import {
		getQryDefaultSkinDebris,
		getQrySkinDebrisHistory,
		convertSkinDebris,
		changeCarrierSkin,
		getqrySkinSummonHistory
	} from '@/service/game';
	import {
		useLoginStore
	} from "@/stores/login";
	const innerAudioContext = ref(null)
	const loginStore = useLoginStore() //用户参数
	const windowWidth = ref() //屏幕宽度
	const popup = ref(null)
	const state = reactive({
		skinList: [],
		defaultSkin: null,
		debrisOpen: '',
		firstIn: false,
		show: false,

	})
	onShow(() => {
		setTimeout(() => {
			getDefault()
			getList()
		}, 1200)
	})
	onUnmounted(() => {
		if (innerAudioContext.value) {
			innerAudioContext.value.stop()
			innerAudioContext.value.destroy()
			innerAudioContext.value = null
		}
	})
	onMounted(() => {
		uni.getSystemInfo({
			success: function (res) {
				windowWidth.value = res.windowWidth
			}
		});
		// #ifdef APP-PLUS
		showLoading('正在进入成长路径...')
		setTimeout(() => {
			plus.navigator.setFullscreen(true);
			plus.screen.lockOrientation('landscape-primary');
			let lastDate = getStorageSync('firstGrowth')
			let currentDate = new Date().toDateString();
			if (lastDate != currentDate) {
				innerAudioContext.value = uni.createInnerAudioContext()
				innerAudioContext.value.src = '/static/audio/growthPath-audio.MP3'
				innerAudioContext.value.play()
				state.firstIn = true
				setStorage('firstGrowth', currentDate)
			}
			state.show = true
			hideLoading()
			uni.onWindowResize(windowResizeCallback)
		}, 1200)
		//#endif
	})
	const windowResizeCallback = (res) => {
		windowWidth.value = res.size.windowWidth
		uni.offWindowResize(windowResizeCallback)
	}
	const open = () => {
		popup.value.open()
	}
	const back = () => {
		navigateBack()
	}
	const goCour = () => {
		redirectTo('/pages/game/course')
	}
	const changeSkin = (value) => {
		if (value.useFlag === '1') {
			return
		}
		if ((value.useFlag === '0' && value.summonFlag === '1') || value.level === 1) {
			changeCarrierSkin({
				traineeId: loginStore.qryActiveTrainee,
				skinId: value.skinId,
			}).then(res => {
				showToast('使用皮肤成功~')
				getList()
				getDefault()
			})
		} else {
			navigateTo(`/pages/shopping/index?level=${value.level}&summonCardId=${value.summonCardId}&carrierId=${value.carrierId}`)
		}
	}
	const convert = (value, value2) => {
		if (value.lockFlage === '1' && value.convertFlage === '0') {
			convertSkinDebris({
				traineeId: loginStore.qryActiveTrainee,
				skinId: value2.skinId,
				debrisIdList: [value.debrisId]
			}).then(res => {
				state.debrisOpen = res.data
				open()
				getList()
			})
		} else {
			return
		}
	}

	const getList = () => {
		getqrySkinSummonHistory({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.skinList = res.data
		})
	}
	const getDefault = () => {
		getQryDefaultSkinDebris({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.defaultSkin = res.data
		})
	}
</script>



<style lang="scss">
	.reward {
		position: relative;
		width: 100vw;
		height: 100vh;

		&-bg {
			width: 56vw;
		}

		&-box {
			position: absolute;
			z-index: 999;
			top: 30%;
			left: 50%;
			transform: translateX(-50%);
			display: flex;
			flex-direction: column;
			align-items: center;

			&-top {
				top: 10%;
			}

			&-img {
				width: 206rpx;

				&-fin {
					width: 188rpx;
				}
			}

			&-text {
				font-size: 24rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #fff;
				line-height: 24rpx;
				margin-top: 26rpx;
				white-space: nowrap;
			}
		}
	}

	.path {
		height: 100vh;
		display: flex;
		position: relative;
		flex-direction: column;
		padding-top: 68rpx;

		&-money {
			position: fixed;
			right: 10rpx;
			top: 6rpx;
			width: 130rpx;
			z-index: 1;
		}

		&-back {
			position: fixed;
			top: 0;
			left: 0;
			width: 113rpx;
			height: 48rpx;
			z-index: 1;
		}

		&-list {
			width: 100%;
			display: flex;
			position: relative;

			&-five {
				position: absolute;
				width: 58rpx;
				top: 15%;
				right: -34rpx;
			}

			&-text {
				height: 40rpx;
				width: 170rpx;
				background: rgba(0, 0, 0, 0.6);
				border-radius: 8rpx;
				font-size: 12rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: bold;
				color: #FFFFFF;
				display: inline;
				padding-top: 4rpx;
				padding: 4rpx;

			}

			&-top {
				margin-left: 302rpx;
			}

			&-bottom {
				margin-left: 144rpx;
				margin-top: 20rpx;
			}
		}

		&-bg {
			position: fixed;
			top: 0;
			width: 100vw;
			height: 100vh;
			left: 0;
			z-index: -1;
		}

		&-line {
			position: absolute;
			bottom: 60rpx;
			height: 54%;
			left: 14rpx;
			z-index: -1;
		}

		&-box {
			position: relative;
			display: flex;
			flex-direction: column;
			height: 160rpx;
			width: 100rpx;
			align-items: center;
			justify-content: space-between;
			margin-right: 192rpx;

			&-right {
				margin-right: 182rpx;
			}

			&-right1 {
				margin-right: 202rpx;
			}

			&-right2 {
				margin-right: 172rpx;
			}

			&-right3 {
				margin-right: 177rpx;
			}

			&-tips {
				width: 226rpx;
				position: absolute;
				top: -34%;
				right: -24%;
				transform: translateX(50%);
			}

			&-level {
				width: 84rpx;
				flex-shrink: 0;
			}

			&-img {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-item {
					position: absolute;
					width: 84rpx;
				}

				&-item:nth-child(2) {
					top: 18%;
				}

				&-item:nth-child(3) {
					top: 34%;
				}
			}

			&-btn {
				width: 85rpx;
				height: 40rpx;
				background: #FF4F1B;
				box-shadow: 0rpx 3rpx 0rpx 0rpx #FF6204;
				border-radius: 10rpx;
				border: 1rpx solid #FFD6C9;
				font-size: 16rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #FFFFFF;
				flex-shrink: 0;

				&-wite {
					background: #999999;
					box-shadow: none;
					border: 1rpx solid #FFFFFF;
				}

				&-use {
					background: #FF9C1B;
					box-shadow: 0rpx 3rpx 0rpx 0rpx #FF6204;
				}
			}

			&-ani {
				left: 50%;
				transform: translateX(-50%);
				width: 89rpx;
				flex-shrink: 0;

				&-rab {
					left: 36%;
				}
			}

			&-sta {
				width: 83rpx;
				height: 40rpx;
				position: absolute;
				top: 46%;
				width: 94rpx;
				z-index: -1;

			}
		}

	}
</style>