<template>
	<!-- #ifdef APP-PLUS || H5 -->
	<view :speed="speed" :isGroundRun="isGroundRun" :courseLevel="courseLevel" :change:isGroundRun="groundObs.updateIsGroundRun" :change:courseLevel="groundObs.updateCourseLevel"
		:change:speed="groundObs.updatesSpeed" id="groundObs" class="ground" />
	<!-- #endif -->
	<!-- #ifndef APP-PLUS || H5 -->
	<view>非 APP、H5 环境不支持</view>
	<!-- #endif -->
</template>


<script>
	export default {
		name: 'GroundObstacles',
		props: {
			courseLevel: {
				required: true
			},
			speed: {
				required: true
			},
			isGroundRun: {
				required: true
			},
		},
		emits: ['gameOver'],
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			changeGame(value) {
				this.$emit('gameOver', value)
			}
		}
	}
</script>
<script module="groundObs" lang="renderjs">
	export default {
		data() {
			return {
				course: '', //课程
				isRun: '',
				obsMoveInterval: null, //障碍物移动
				creatInner: null,
				isDelet: false
			}
		},
		mounted() {
			// this.obsMove()
		},
		methods: {
			updateIsGroundRun(newValue, oldValue, ownerInstance, instance) {
				this.isDelet = newValue
				this.obsMoveInterval && cancelAnimationFrame(this.obsMoveInterval)
				this.obsMove(ownerInstance)
			},
			updateCourseLevel(newValue, oldValue, ownerInstance, instance) {
				this.course = newValue
				if (newValue > 1 && newValue < 5) {
					this.createObstruction(ownerInstance)
				}
			},
			updatesSpeed(newValue, oldValue, ownerInstance, instance) {
				this.isRun = newValue
				this.obsMoveInterval && cancelAnimationFrame(this.obsMoveInterval)
				this.obsMove(ownerInstance)
			},
			createObstruction(ownerInstance) {
				let time = 30000
				switch (this.course) {
					case 3:
						time = 15000
						break;
					case 4:
						time = 10000
						break;
					default:
						break;
				}
				let grunWrap = document.querySelector(".grun-wrap")
				this.creatInner = setInterval(() => {
					let groundObstacles = document.createElement('div')
					groundObstacles.className = 'grun-ground'
					groundObstacles.style.background = `center center url('./static/game/game-ground-obstacle${this.course-1}.png')  no-repeat`
					groundObstacles.style.width = '120px'
					groundObstacles.style.height = '120px'
					groundObstacles.style.position = 'absolute'
					groundObstacles.style.top = 0
					groundObstacles.style.zInedx = -1
					groundObstacles.style.left = document.documentElement.clientWidth + 'px'
					let childrens = document.querySelectorAll(".grun-ground")
					if (Object.keys(childrens).length == 0) {
						grunWrap.appendChild(groundObstacles)
						this.obsMoveInterval && cancelAnimationFrame(this.obsMoveInterval)
						this.obsMove(ownerInstance)
					} else {
						groundObstacles.remove()
					}


				}, time)
			},
			obsMove(ownerInstance) {
				if (this.isRun) {
					let grunWrap = document.querySelector(".grun-ground")
					let animal = document.querySelector('.grun-wrap-animal')
					let animlJump = document.querySelector('.grun-wrap-jump')

					let cityMove = () => {
						// 给每个障碍物添加移动
						if (grunWrap) {
							if (grunWrap.offsetLeft < -grunWrap.offsetWidth) {
								grunWrap.remove()
							} else {
								let animalOffsetLeft = animal.offsetLeft || animlJump.offsetLeft
								console.log();
								if (grunWrap.state != 'JumpTrue' && grunWrap.offsetLeft <= (animal.offsetWidth + animalOffsetLeft - (Math.floor(animal.offsetWidth / 3))) && grunWrap.offsetLeft > (
										animal.offsetWidth + animalOffsetLeft - 6 - (Math.floor(animal.offsetWidth / 3)))) {
									console.log('卡住');
									ownerInstance.callMethod('changeGame', 'stopGround')
								}

								grunWrap.style.left = (grunWrap.offsetLeft - this.isRun * 1000) + 'px'
								this.obsMoveInterval = requestAnimationFrame(cityMove)
							}
						}
					}
					cityMove()
				} else {
					let grunWrap = document.querySelector(".grun-ground")
					let animal = document.querySelector('.grun-wrap-animal')
					if (grunWrap && this.isDelet) {
						grunWrap.remove()
					}
				}
			}
		}
	}
</script>