<template>
	<uni-popup ref="popup" type="center" :animation="false" @maskClick="onClick_scanStop">
		<view class="blu-list">
			<view class="blu-list-title">
				设备列表 <text @click="onClick_scanDevice" class="blu-list-title-icon iconfont">&#xe631;</text>
			</view>
			<view class="blu-list-box" v-if="state.bluList[0].address">
				<view v-for="(item,index) in state.bluList" :key="item.address" class="blu-list-box-item">
					<view class="blu-list-box-item-left">
						<image class="blu-list-box-item-left-img" src="../../static/device/blu-icon.png" mode="widthFix"></image>
						<text class="blu-list-box-item-left-address">{{item.name}}</text>
						<text class="blu-list-box-item-left-status" v-show="helper.address&&helper.address==item.address">已连接</text>
					</view>
					<view class="blu-list-box-item-rignt" @click="()=>onClick_connectDevice(item)">
						{{helper.address&&helper.address==item.address?'断开' :'连接'}}
					</view>
				</view>
			</view>
			<view class="center" v-else>
				正在加载设备...
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		showToast,
		showLoading,
		hideLoading
	} from "../../common/uniTool";
	import {
		Blue
	} from '@/utils/bluConfig';
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		getFakeTimeSW
	} from "../../utils/getFakeTime";
	import {
		changeAddress
	} from "../../common/global";
	const helper = useHelper(); //设备仓库
	const socket = useSocketStore(); //websocket仓库
	const props = defineProps(['status', 'isSocket', 'eceuId'])
	const event = defineEmits(['change'])
	const popup = ref(null)
	const state = reactive({
		bluList: [{
			address: "",
			name: ''
		}],
	})
	watch(() => props.status, (status) => {
		if (status) {
			popup.value.open('center')
			onClick_scanDevice()
		} else {
			popup.value.close()
		}
	})
	onUnmounted(() => {
		// #ifdef APP-PLUS
		onClick_scanStop()
		// #endif
	})
	onMounted(() => {
		// #ifdef APP-PLUS

		if (helper.address) {
			Blue.bleConnectDeviceID = helper.address
			state.bluList = [helper.connectingDevice]
		}
		ownBluInit()
		// #endif
	})
	const onClick_scanDevice = () => {
		console.log('扫描');

		Blue.start()
	}
	const ownBluInit = () => {
		Blue.start()
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			// console.log(1);
			helper.bluData = JSON.parse(state)
			if (props.isSocket) {
				if (socket.socketTask && !socket.socketTask.userClose && props.eceuId && socket.socketTask.ws.readyState === 1) {
					socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(state, props.eceuId, '', socket.socketTask)))
				}
			}
		})
		BleController.addConnectStateListen(state => {
			// console.log(state);
			if (state.code === 200 && state.deviceInfo) {
				connectSuccess(state.deviceInfo.address, state.deviceInfo.name) //连接成功
				Blue.isInit = true
			} else if (state.code === -1) {
				uni.showModal({
					title: state.label,
					showCancel: false,
				});
			} else if (state.code === -2) {
				showToast(state.label)
			} else if (state.code === 500) {
				changeAddress('')
				helper.address = ''
				helper.connectingDevice = null
				Blue.bleConnectDeviceID = null
				showToast(state.label)
			}
		})
		BleController.addDeviceListListen(res => {
			const bList = [{
				"name": res.name,
				"address": res.deviceId,
			}]
			if (!state.bluList[0].address) {
				state.bluList = bList
			}
			state.bluList = [...filterList(state.bluList, bList)]
		})
	}
	const onClick_scanStop = () => {
		event('change', false)

		Blue.stopBluetoothDevicesDiscovery()
	}
	//连接成功
	const connectSuccess = (value, name) => {
		helper.address = value
		changeAddress(name)
		helper.connectingDevice = {
			"name": name,
			"address": value,
		}
		onClick_scanStop()
		hideLoading()
		showToast('设备连接成功，请开始你的测评吧~')
	}
	//去掉重复数组
	const filterList = (arr1, arr2) => {
		let values = [...arr1, ...arr2]
		let map = new Map()
		for (let item of values) {
			if (!map.has(item.address)) {
				map.set(item.address, item)
			}
		}
		return map.values()
	}

	//断连
	const disconnectDevice = (device) => {
		console.log(device, 'disconnectDevice');
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.closeBLEConnection(device.address)
			Blue.bleConnectDeviceID = null
		}
	}
	//连接
	const connectDevice = (device) => {
		console.log(device, 'connectDevice');
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.createBLEConnection(device)
		}
	}
	const onClick_connectDevice = (device) => {
		helper.resetBox()
		helper.bluData = null
		if (helper.address === device.address) {
			disconnectDevice(device) //断连
			changeAddress('')
			helper.address = ''
			helper.connectingDevice = null
			showToast('设备蓝牙已断开')
			return
		}
		if (helper.address && helper.address !== device.address) {
			disconnectDevice(helper.connectingDevice) //断连
		}
		changeAddress('')
		helper.address = ''
		helper.connectingDevice = null
		showLoading('设备连接中...')
		connectDevice(device) //连接
	}
</script>

<style lang="scss">
	.blu-list {
		display: flex;
		flex-direction: column;
		width: 450rpx;
		height: 271rpx;
		background: url('~@/static/game/course-pop-img.png');
		background-repeat: no-repeat;
		background-size: 450rpx 271rpx;
		border-radius: 25rpx;
		padding: 36rpx;

		&-title {
			font-size: 20rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #111111;
			margin-bottom: 22rpx;

			&-icon {
				color: #FF4222;
				margin-left: 10rpx;
			}
		}

		&-box {
			width: 100%;
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			overflow: hidden;
			overflow-y: auto;

			&-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12rpx;

				&-left {
					display: flex;
					align-items: center;
					flex: 1;

					&-img {
						width: 34rpx;
						height: 34rpx;
					}

					&-address {
						font-size: 15rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						margin-left: 24rpx;
						margin-right: 16rpx;

					}

					&-status {
						border-radius: 4rpx;
						border: 1rpx solid #111111;
						font-size: 11rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						padding: 2rpx 5px;
					}
				}

				&-rignt {
					background: #FFE4BE;
					border-radius: 27rpx;
					font-size: 15rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #974E2B;
					padding: 10rpx 28rpx;
				}
			}


		}
	}
</style>