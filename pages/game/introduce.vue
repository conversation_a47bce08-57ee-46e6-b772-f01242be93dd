<template>
	<view class="gameIntroduce" v-if="state.show">
		<image src="../../static/game/introduce-bg.png" mode="widthFix" class="gameIntroduce-bg"></image>
		<image src="../../static/game/game-introduce.gif" mode="widthFix" class="gameIntroduce-img"></image>
		<view class="gameIntroduce-btn center" @click="go">
			我已知晓
		</view>
	</view>
</template>

<script setup>
	import {
		navigateTo
	} from '../../common/uniTool';
	import {
		onMounted,
		onUnmounted,
		reactive
	} from "vue";
	import {
		useSocketStore
	} from '../../stores/socket';
	const socket = useSocketStore(); //websocket仓库
	const state = reactive({
		show: false
	})
	import {
		onShow,
	} from '@dcloudio/uni-app'
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(false);
		//#endif
	})
	onMounted(() => {
		// #ifdef APP-PLUS
		uni.showLoading({
			title: "正在进入脑电反训练系统..."
		})
		setTimeout(() => {
			plus.screen.unlockOrientation();
			plus.screen.lockOrientation('landscape-primary');
			uni.hideLoading();
			state.show = true
		}, 1200)
		//#endif
	})
	onUnmounted(() => {
		console.log('卸载');
		if (socket.socketTask) {
			socket.socketTask.closeSocket()
		}
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary');
		// #endif
	})
	const go = () => {
		navigateTo('/pages/game/grade')
	}
</script>

<style lang="scss">
	.gameIntroduce {
		height: 100vh;
		width: 100vw;
		position: relative;
		padding-top: 105px;
		padding-left: 124px;

		&-bg {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}

		&-img {
			width: 414px;
			height: 314px;
			border-radius: 10px;
		}

		&-box {
			width: 264px;
			height: 164px;
			border-radius: 10px;

		}


		&-btn {
			width: 230px;
			height: 80px;
			background: #FF9C1B;
			box-shadow: 0px 3px 0px 0px rgba(255, 98, 4, 1);
			border-radius: 15px;
			font-size: 28px;
			font-family: SourceHanSansCN-Bold, SourceHanSansCN;
			font-weight: bold;
			color: #FFFFFF;
			position: absolute;
			right: 160px;
			bottom: 140px;
		}
	}
</style>