<template>
	<view class="gameInfo">
		<view class="gameInfo-name center">
			<image class="gameInfo-name-icon" src="../../static/game/game-owner.png" mode="heightFix"></image>
			{{props.userInfo&&props.userInfo.traineeName}}
		</view>
		<view class="gameInfo-name center" v-if="props.level>1">
			<image src="../../static/game/game-true-icon.png" mode="heightFix" class="gameInfo-name-icon"></image>
			正确水果
			{{props.signalNormalSuccCount||0}}个
		</view>
		<view class="gameInfo-name center">
			<image src="../../static/game/game-time-icon.png" mode="heightFix" class="gameInfo-name-icon"></image>
			{{state.timeText}}
		</view>
		<view class="gameInfo-name center" style="justify-content: center;width: 120rpx;">
			{{(typeof props.total=='string')&&props.total.split(".")[0]||0}} m
		</view>
		<view class="gameInfo-music" @click="change">
			<text class="gameInfo-music-icon iconfont">&#xe668;</text>
			<view class="gameInfo-music-box">
				<view class="gameInfo-music-box-slider center" v-if="state.music">
					开
				</view>
				<view class="gameInfo-music-box-slider gameInfo-music-box-slider-close center" v-else>
					关
				</view>
			</view>
		</view>
		<image src="../../static/game/game-stop.png" class="gameInfo-stop" mode="widthFix" @click="open"></image>
		<uni-popup ref="popup" :mask-click="false">
			<view class="gameInfo-pop">
				<view class="gameInfo-pop-title font-game">
					课程已暂停
				</view>
				<view class="gameInfo-pop-control">
					<view v-for="(item,index) in state.controlList" class="gameInfo-pop-control-box" :key="item.value" @click="()=>onClick(item.value)">
						<image class="gameInfo-pop-control-box-icon" :src="item.icon" mode="widthFix"></image>
						<text>{{item.text}}</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		formatSeconds,
		throttle
	} from '@/common/method.js'
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		onMounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		redirectTo
	} from "../../common/uniTool";
	import {
		useHelper
	} from "../../stores/helper";
	const helper = useHelper(); //设备仓库
	const loginStore = useLoginStore()
	const popup = ref(null)
	const props = defineProps(['total', 'userInfo', 'speed', 'showCount', 'level', 'signalNormalSuccCount', 'gameIsOver'])
	const event = defineEmits(['gameOver', 'changeMusic'])
	const coutDownInner = ref(null)
	const state = reactive({
		controlList: [{
				icon: '../../static/game/control-next.png',
				text: '继续',
				value: 'continue'
			},
			{
				icon: '../../static/game/control-reset.png',
				text: '重新开始',
				value: 'reset'
			},
			{
				icon: '../../static/game/control-home.png',
				text: '主页',
				value: 'home'
			},
		],
		time: 180,
		timeText: '03:00',
		music: true
	})
	watch(() => props.showCount, (showCount) => {
		if (!showCount) {
			start()
		} else {
			clearInterval(coutDownInner.value)
			coutDownInner.value = null
		}
	})

	const change = () => {
		state.music = !state.music
		event('changeMusic', state.music)
	}
	watch(() => helper.bluData, (bluData) => {
		if ((bluData.type == 5 || bluData.signal) && !props.gameIsOver) {
			open()
		}
	})
	const start = () => {
		coutDownInner.value = setInterval(() => {
			state.time--
			state.timeText = formatSeconds(state.time, false)
			if (state.time == 0) {
				clearInterval(coutDownInner.value)
				coutDownInner.value = null
				event('gameOver', 'over')
			}
		}, 1000)
	}
	const onClick = (value) => {
		switch (value) {
			case 'continue':
				event('gameOver', 'continue')
				clearInterval(coutDownInner.value)
				start()
				popup.value.close()
				break;
			case 'reset':
				state.time = 180
				state.timeText = '03:00'
				clearInterval(coutDownInner.value)
				start()
				popup.value.close()
				event('gameOver', 'reset')
				break;
			case 'home':
				redirectTo('/pages/game/course')
				break;
			default:
				break;
		}
	}
	const open = () => {
		// console.log('打开课程暂停弹窗');
		event('gameOver', 'stop')
		clearInterval(coutDownInner.value)
		popup.value.open('center')
	}
</script>

<style lang="scss">
	.gameInfo {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
		padding: 0 20rpx;

		&-music {
			width: 88rpx;
			background: #FFF3E3;
			border-radius: 23rpx;
			border: 1rpx solid #BD774B;
			display: flex;
			align-items: center;
			height: 32rpx;
			justify-content: center;

			&-icon {
				font-size: 16rpx;
				color: #8D3F1E;
				margin-right: 6rpx;
			}

			&-box {
				width: 50rpx;
				height: 15rpx;
				background: #FFCE8A;
				box-shadow: inset 0rpx 1rpx 2rpx 0rpx rgba(248, 166, 55, 1);
				border-radius: 8rpx;
				border: 1rpx solid #FFE4BF;
				position: relative;

				&-slider {
					width: 25rpx;
					height: 20rpx;
					background: #FF9C1B;
					box-shadow: 0rpx 1rpx 0rpx 0rpx rgba(255, 98, 4, 1);
					border-radius: 5rpx;
					position: absolute;
					top: -5rpx;
					left: 0;
					font-size: 15rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #FFFFFF;
					animation: slide 1s ease-in-out 1;
					animation-fill-mode: forwards;

					&-close {
						animation: slideClose 1s ease-in-out 1;
						animation-fill-mode: forwards;
						top: -5rpx;
						left: 25rpx;
					}
				}
			}
		}

		&-name {
			height: 32rpx;
			font-size: 18rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: bold;
			color: #8D3F1E;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			background: #FFF3E3;
			border-radius: 23px;
			border: 2px solid #BD774B;
			position: relative;
			padding-left: 15rpx;
			padding-right: 20rpx;

			&-text {
				height: 32rpx;
				position: absolute;
				right: 20rpx;
				top: 50%;
				line-height: 32rpx;
				transform: translateY(-50%);
			}

			&-icon {
				margin-right: 10rpx;
				height: 23px;

			}
		}



		&-stop {
			width: 32rpx;
			height: 32rpx;
		}

		&-pop {
			width: 514rpx;
			height: 271rpx;
			background: url('~@/static/game/game-stop-bg.png');
			background-repeat: no-repeat;
			background-size: 514rpx 271rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			padding: 22rpx;

			&-title {
				font-size: 35rpx;
				font-weight: normal;
				color: #8D3F1E;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-around;
			}

			&-control {
				display: flex;
				align-items: center;
				justify-content: space-around;
				width: 100%;

				&-box {
					display: flex;
					flex-direction: column;
					align-items: center;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #B26033;

					&-icon {
						width: 98rpx;
						height: 98rpx;
						margin-bottom: 15rpx;
					}
				}

			}
		}
	}

	@keyframes slideClose {
		from {
			transform: translateX(0);
		}

		to {
			transform: translateX(-25rpx);
		}
	}

	@keyframes slide {
		from {
			transform: translateX(0);
		}

		to {
			transform: translateX(25rpx);
		}
	}
</style>