<template>
	<view class="game-tip center">
		<image v-if="state.show" class="game-tip-img " :src="state.img" mode="widthFix"></image>
	</view>
</template>

<script setup>
	import {
		reactive,
		watch
	} from "vue";
	import {
		debounce
	} from '@/common/method.js'
	const props = defineProps(['total'])
	const state = reactive({
		img: '',
		show: false
	})
	const showImg = (index) => {
		state.img = `../../static/game/game-${Math.floor(index)}.png`
		state.show = true
		setTimeout(() => {
			state.show = false
		}, 1000)
	}
	watch(() => props.total, (total) => {
		if (total > 100 && total < 101) {
			debounce(showImg(100), 5000)
		} else if (total > 200 && total < 201) {
			debounce(showImg(200), 5000)
		} else if (total > 300 && total < 301) {
			debounce(showImg(300), 5000)
		} else if (total > 400 && total < 401) {
			debounce(showImg(400), 5000)
		} else if (total > 500 && total < 501) {
			debounce(showImg(500), 5000)
		} else if (total > 600 && total < 601) {
			debounce(showImg(600), 5000)
		} else if (total > 700 && total < 701) {
			debounce(showImg(700), 5000)
		} else if (total > 800 && total < 801) {
			debounce(showImg(800), 5000)
		} else if (total > 900 && total < 901) {
			debounce(showImg(900), 5000)
		}
	})
</script>

<style lang="scss">
	.game-tip {
		width: 100%;
		height: 80rpx;
		position: absolute;
		top: 35%;
		transform: translateY(-50%);

		&-img {
			width: 60%;
		}
	}
</style>