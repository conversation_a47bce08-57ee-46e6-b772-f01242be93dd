<template>
	<view class="form">
		<view className="form-title">请填写完整的个人信息，完成建档</view>
		<uni-forms :modelValue="state.formData" label-position="top" label-width="200" ref="scaForm"
			:rules="state.rules">
			<uni-forms-item label="身份证号" name="idnum" required>
				<uni-easyinput v-model="state.formData.idnum" placeholder="请输入真实身份证号" />
			</uni-forms-item>
			<uni-forms-item label="姓名" name="traineeName" required>
				<uni-easyinput type="text" v-model="state.formData.traineeName" placeholder="请输入真实姓名" />
			</uni-forms-item>
			<uni-forms-item label="性别" required name="sex">
				<uni-data-checkbox v-model="state.formData.sex" :localdata="state.sex" />
			</uni-forms-item>
			<uni-forms-item label="出生年月" required name="birth">
				<picker mode="date" fields="day" :value="state.formData.birth" @change="bindDateChange">
					<view v-if="state.formData.birth" class="form-title-value">{{state.formData.birth}}</view>
					<view v-else class="form-title-birth">请输入出生年月</view>
				</picker>
				<!-- <uni-datetime-picker type="data" return-type="string" v-model="state.formData.birth" /> -->
			</uni-forms-item>
			<uni-forms-item label="身高cm" name="height">
				<uni-easyinput type="number" v-model="state.formData.height" placeholder="请输入cm" />
			</uni-forms-item>
			<uni-forms-item label="体重kg" name="weight">
				<uni-easyinput type="number" v-model="state.formData.weight" placeholder="请输入kg"
					placeholder-style="line-height:50rpx" />
			</uni-forms-item>
			<uni-forms-item label="病历号" name="medicalRecordNum">
				<uni-easyinput v-model="state.formData.medicalRecordNum" placeholder="请输入病历号" />
			</uni-forms-item>
			<uni-forms-item label="门诊号" name="outPatientNum">
				<uni-easyinput v-model="state.formData.outPatientNum" placeholder="请输入门诊号" />
			</uni-forms-item>
			<uni-forms-item label="文化程度" name="stanOfCul" required>
				<uni-data-checkbox v-model="state.formData.stanOfCul" placeholder="请输入文化程度"
					:localdata="state.stanOfCul" />
			</uni-forms-item>
			<uni-forms-item label="具体文化程度" name="grade" required v-if="state.formData.stanOfCul!=-1">
				<uni-data-checkbox v-model="state.formData.grade" placeholder="请输入文化程度" :localdata="state.grade" />
			</uni-forms-item>
			<uni-forms-item label="其他" name="other">
				<uni-easyinput v-model="state.formData.other" placeholder="其他" />
			</uni-forms-item>
			<uni-forms-item label="与注册账户的关系" required name="relation">
				<uni-data-checkbox v-model="state.formData.relation" :localdata="state.relation" />
			</uni-forms-item>
			<button type="primary" form-type="submit" @click="formSubmit()">提交</button>
		</uni-forms>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		IdCard,
		timestampToTime
	} from "../../common/method";
	import {
		navigateBack,
		navigateTo,
		showToast
	} from "../../common/uniTool";
	import {
		getVisitorDetail,
		changeVisitorInfo
	} from "../../service";
	import {
		postInfo
	} from "../../service/scale";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		rsaEncrypt,
		rsaDecrypt
	} from "../../utils/encrypt";

	const props = defineProps(['traineeId', 'type'])
	const scaForm = ref(null)
	const loginStore = useLoginStore()
	const state = reactive({
		traineeId: '',
		formData: {
			traineeName: '',
			height: '',
			birth: null,
			weight: '',
			relation: -1,
			sex: -1,
			idnum: '',
			medicalRecordNum: '',
			outPatientNum: '',
			stanOfCul: -1,
			other: '',
			grade: -1
		},
		rules: {
			idnum: {
				rules: [{
					required: true,
					errorMessage: '身份证号不能为空'
				}, {
					validateFunction: function(rule, value, data, callback) {
						const myreg =
							/^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/;
						if (!myreg.test(value)) {
							callback('请输入正确身份证号')
						}
						return true
					}
				}]
			},
			grade: {
				rules: [{
					validateFunction: function(rule, value, data, callback) {
						if (value == -1) {
							callback('具体文化程度不能为空')
						}
						return true
					}
				}]
			},
			stanOfCul: {
				rules: [{
					validateFunction: function(rule, value, data, callback) {
						if (value == -1) {
							callback('文化程度不能为空')
						}
						return true
					}
				}]
			},
			relation: {
				rules: [{
					validateFunction: function(rule, value, data, callback) {
						if (value == -1) {
							callback('关系不能为空')
						}
						return true
					}
				}]
			},
			traineeName: {
				rules: [{
						required: true,
						errorMessage: '姓名不能为空'
					},
					{
						maxLength: 30,
						errorMessage: '姓名最长{maxLength}个字符',
					}
				]
			},
			sex: {
				rules: [{
					validateFunction: function(rule, value, data, callback) {
						if (value == -1) {
							callback('性别不能为空')
						}
						return true
					}
				}]
			},
			birth: {
				rules: [{
					required: true,
					errorMessage: '出生日期不能为空'
				}]
			}
		},
		relation: [{
			text: '父子',
			value: 1
		}, {
			text: '父女',
			value: 2
		}, {
			text: '母子',
			value: 3
		}, {
			text: '母女',
			value: 4
		}, {
			text: '祖孙',
			value: 5
		}, {
			text: '其他监护人',
			value: 6
		}, {
			text: '本人',
			value: 7
		}],
		sex: [{
			text: '男',
			value: 1,
		}, {
			text: '女',
			value: 2,
		}],
		stanOfCul: [{
			text: '幼儿园',
			value: '1'
		}, {
			text: '小学',
			value: '2'
		}, {
			text: '初中',
			value: '3'
		}, {
			text: '高中',
			value: '4'
		}],
		adhdHis: [{
			text: '半年以内',
			value: 1
		}, {
			text: '半年至一年',
			value: 2
		}, {
			text: '一年至三年',
			value: 3
		}, {
			text: '三年以上',
			value: 4
		}],
		grade: [{
			text: '幼儿园小班',
			value: '1'
		}, {
			text: '幼儿园中班',
			value: '2'
		}, {
			text: '幼儿园大班',
			value: '3'
		}],
	})
	onMounted(() => {
		state.traineeId = props.traineeId
	})

	watch(() => state.traineeId, (traineeId) => {
		if (traineeId) {
			getVisitorDetail({
				traineeId
			}).then(res => {
				console.log("res.data: " + JSON.stringify(res.data));
				state.formData = res.data
				state.formData.idnum = rsaDecrypt(state.formData.idnum)
				state.formData.birth = timestampToTime(res.data.birthDate, true)
			})
		}
	})
	watch(() => state.formData.stanOfCul, (stanOfCul) => {
		if (stanOfCul == 1) {
			state.grade = [{
				text: '幼儿园小班',
				value: '1'
			}, {
				text: '幼儿园中班',
				value: '2'
			}, {
				text: '幼儿园大班',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else if (stanOfCul == 2) {
			state.grade = [{
				text: '小学一年级',
				value: '1'
			}, {
				text: '小学二年级',
				value: '2'
			}, {
				text: '小学三年级',
				value: '3'
			}, {
				text: '小学四年级',
				value: '4'
			}, {
				text: '小学五年级',
				value: '5'
			}, {
				text: '小学六年级',
				value: '6'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else if (stanOfCul == 3) {
			state.grade = [{
				text: '初一',
				value: '1'
			}, {
				text: '初二',
				value: '2'
			}, {
				text: '初三',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else {
			state.grade = [{
				text: '高一',
				value: '1'
			}, {
				text: '高二',
				value: '2'
			}, {
				text: '高三',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		}
	})
	const bindDateChange = (e) => {
		state.formData.birth = e.detail.value
	}

	const getRelation = (e) => {
		formData.relation = e.detail.value
	}
	const formSubmit = (e) => {
		scaForm.value.validate().then(res => {
			res.idnum = rsaEncrypt(res.idnum)
			if (state.traineeId) {
				res.traineeId = state.traineeId
				changeVisitorInfo(res).then(() => {
					showToast('更改成功')
					loginStore.getVisitor()
					loginStore.getActiveTraineeInfo()
					navigateBack()
				})
				return
			}
			postInfo(res).then(() => {
				loginStore.getVisitor()
				loginStore.getActiveTraineeInfo()
				if (props.type == 1) {
					navigateBack()
				} else {
					navigateTo('/pages/scale/index')
				}
			}).catch(err => {
				showToast(err.desc)
			})
		}).catch(err => {
			console.log('err', err);
		})
	}
</script>

<style lang="scss">
	.form {
		padding: 42rpx 40rpx 120rpx 40rpx;

		&-title {
			font-size: 36rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			margin-bottom: 40rpx;

			&-birth {
				color: #999;
				font-size: 15rpx;
				border: 0.5px solid #bfbfbf;
				padding: 10rpx;
				border-radius: 4px;
			}

			&-value {
				font-size: 14rpx;
				border: 0.5px solid #c3c3c3;
				padding: 10rpx;
				border-radius: 6px;
				color: #000;
			}
		}

		&-radio {
			display: flex;
			justify-content: space-around;
		}
	}
</style>