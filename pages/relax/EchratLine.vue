<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="echartsLine.onClick" :prop="focusDate" :change:prop="echartsLine.updateEcharts" id="echartsLine" class="echartsLine"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: 'EchartLine',
		props: {
			focusDate: Object
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.focusDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="echartsLine" lang="renderjs">
	import {
		formatSeconds
	} from '../../common/method'
	let myChart
	let data = []
	let data1 = []
	export default {
		data() {
			return {
				option: {
					animation: false,
					grid: {
						left: '4%',
						right: '0%',
						bottom: '10%',
						top: '14%',

					},
					legend: {
						data: ['专注度', '压力值']
					},
					xAxis: {
						type: 'category',
						splitLine: {
							show: false
						}
					},
					yAxis: {
						type: 'value',
						boundaryGap: [0, '100%'],
						max: 100,
						min: 0,
					},
					series: [{
						name: '专注度',
						smooth: true,
						type: 'line',
						showSymbol: false,
						data: data,
					}, {
						name: '压力值',
						smooth: true,
						type: 'line',
						showSymbol: false,
						data: data1,
					}]
				},
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('echartsLine'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				data.push([formatSeconds(newValue.time, false), newValue.focus])
				data1.push([formatSeconds(newValue.time, false), newValue.stress])
				myChart && myChart.setOption({
					series: [{
						data: data
					}, {
						data: data1
					}]
				});
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 328rpx;
	}


	.echartsLine {
		width: 100%;
		height: 328rpx;
	}
</style>