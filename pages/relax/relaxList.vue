<template>
	<view class="relaxList" :class="props.type==2?'relaxList-bg':''">
		<view class="relaxList-box">
			<view class="relaxList-box-item" :key="item.id" v-for="(item,index) in props.type==2?state.list2:state.list" @click="()=>go(item.id,item.name)">
				<view class="relaxList-box-item-left">
					{{index+1}}
				</view>
				<view class="relaxList-box-item-center">
					<view class="relaxList-box-item-center-top">
						{{item.name}}
					</view>
					<view class="relaxList-box-item-center-bottom">
						{{item.tip}}
					</view>
				</view>
				<view class="relaxList-box-item-right">
					<image class="relaxList-box-item-right-icon" src="../../static/relax/relax-audio-play-icon.png" mode="widthFix"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		reactive,
		watch
	} from "vue";
	import {
		navigateTo
	} from "../../common/uniTool";
	const props = defineProps(['type'])
	const state = reactive({
		list: [{
				id: '01',
				name: '像青蛙一样呼吸',
				tip: '静静地坐一会儿什么也不想，专注地呼吸。'
			},
			{
				id: '02',
				name: '培养意识，提示专注',
				tip: '轻轻的闭上眼睛，让自己感觉舒适。'
			},
			{
				id: '03',
				name: '肚皮上的小船',
				tip: '折一只小纸船，找一个安静舒适的环境，身体放松，保持警觉。'
			},
			{
				id: '04',
				name: '专注石',
				tip: '仔细观察小石头，双收自然放在身前，轻轻闭上眼睛。'
			},
			{
				id: '05',
				name: '对身体微笑',
				tip: '感觉我们的头顶着蓝天，把你的注意力放在呼吸上。'
			},
			{
				id: '06',
				name: '用鼻子探索世界',
				tip: '看着眼前的小物品，闭上眼睛呼吸，带来美好的回忆和联想。'
			},
			{
				id: '07',
				name: '和云朵一起呼吸',
				tip: '透过窗户，仰望天空，选择一朵云。'
			}
		],
		list2: [{
			id: '08',
			name: '放松训练教程',
			tip: '缓解紧张和焦虑，快速有效的放松和减压。'
		}],
		backImg: ''
	})

	const go = (id, name) => {
		navigateTo(`/pages/relax/index?url=${id}&name=${name}`)
	}
	watch(() => props.type, (type) => {
		console.log(type);
		if (type == 2) {
			state.backImg = {
				backgroundImage: "url('~@/static/relax/relax-audio-relax2.png')"
			}
		}
	})
</script>

<style lang="scss">
	.relaxList {
		width: 100vw;
		height: 100vh;
		background: url('~@/static/relax/relax-audio-relax.png') #748D93;
		background-repeat: no-repeat;
		background-size: 100% auto;
		position: relative;
		padding-top: 46%;

		&-bg {
			background: url('~@/static/relax/relax-audio-relax2.png') #748D93;
			background-repeat: no-repeat;
			background-size: 100% auto;
		}

		&-box {
			display: flex;
			flex-direction: column;
			padding: 0 54rpx;
			background: #FFFFFF;
			border-radius: 16rpx 16rpx 0 0;
			height: 100%;

			&-item {
				height: 116rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 2rpx solid #EEEEEE;

				&-left {
					font-size: 24rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #26324A;
				}

				&-center {
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					height: 100%;
					padding: 26rpx 0;
					flex: 1;
					margin-left: 60rpx;

					&-top {
						font-size: 28rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: bold;
						color: #26324A;
					}

					&-bottom {
						font-size: 16rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #666666;
					}

				}

				&-right {
					&-icon {
						width: 56rpx;
						height: 56rpx;
					}
				}
			}

			&-item:last-child {
				border: none;
			}
		}
	}
</style>