<template>
	<view class="relaxVideo">
		<video class="relaxVideo-video" id="myVideo" object-fit="cover" :src="`../../static/video/${props.url}.mp4`" enable-play-gesture="false" autoplay="true" show-fullscreen-btn="false"
			@error="videoErrorCallback" controls="false" http-cache="true" @ended="onended">
			<cover-view class="relaxVideo-video-coverview" :style="{background:`rgba(17, 17, 17, ${state.videoBg})`}">

			</cover-view>
		</video>
		<view class="relaxVideo-num">
			<view class="relaxVideo-num-top">
				<view class="relaxVideo-num-top-left">
					<view class="relaxVideo-num-top-left-value" :style="{left:state.valueLeft+'rpx'}">
						<view class="relaxVideo-num-top-left-value-text">
							明亮度
						</view>
					</view>
					<view class="relaxVideo-num-top-left-bg">

					</view>
				</view>
				<!-- 	<view class="relaxVideo-num-top-right">
					{{state.focus}}
				</view> -->
			</view>
			<view class="relaxVideo-num-text">
				专注度越高，视频画面越亮
			</view>
			<view class="relaxVideo-value-num" style="margin-top: 18rpx;">
				<view class="relaxVideo-value-num-max">
					<image src="../../static/relax/relax-focus-icon.png" mode="widthFix" style="width: 30rpx;height: 30rpx;flex: 1;margin-right: 26rpx;"></image>
					最高专注度：{{state.focusData.maxFocus}}
				</view>
				<view class="relaxVideo-value-num-max">
					平均专注度：{{state.focusData.avgFocus}}
				</view>
			</view>
		</view>
		<view class="relaxVideo-value">
			<image class="relaxVideo-value-tip" src="../../static/relax/relax-mp4-tip.png" mode="widthFix"></image>
			<EchratLineVue :focusDate="state.focusData" />

			<!-- 			<view class="relaxVideo-value-num" style="background-color: #FFF2F2;margin-top: 18rpx;">
				<view class="relaxVideo-value-num-max">
					<image src="../../static/relax/realx-stress-icon.png" mode="widthFix" style="width: 30rpx;height: 30rpx;flex: 1;margin-right: 26rpx;"></image>
					最低压力值：{{state.focusData.maxFocus}}
				</view>
				<view class="relaxVideo-value-num-max">
					平均压力值：{{state.focusData.avgFocus}}
				</view>
			</view> -->
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from 'vue'
	import {
		useHelper
	} from '@/stores/helper.js'
	import {
		qryRelaxTrainData
	} from '../../service/relax';
	import EchratLineVue from './EchratLine.vue';
	import {
		setNavigationBarTitle,
		setScreenBrightness,
		setKeepScreenOn
	} from '../../common/uniTool';
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		wsUrl
	} from '../../common/global';
	import {
		useLoginStore
	} from "../../stores/login";
	import ws from '@/utils/websocket.js'
	import {
		BleController
	} from '@/utils/bluType';
	const loginStore = useLoginStore(); //用户基本信息
	const props = defineProps(['url', 'name'])
	setNavigationBarTitle(props.name)
	const socket = useSocketStore(); //websocket仓库
	const helper = useHelper(); //设备仓库
	const timeRef = ref(null)
	const state = reactive({
		focusData: {
			maxFocus: 0,
			avgFocus: 0,
			focus: 0,
			time: 0,
			stress: 0
		},
		focus: 0,
		focusArr: [],
		videoFocus: 0,
		videoBg: 1,
		valueLeft: 0,
		nowData: new Date(),

	})
	onMounted(() => {
		// plus.device.setVolume(0.1)
		setKeepScreenOn()
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}

		ownBluInit()
		socket.socketTask && socket.socketTask.getWebSocketMsg((data) => {
			state.focusData = data
			state.focus = data.focus
			state.focusArr.push(data.focus)
			state.videoBg = 0
			// if (data.focus < 30 && data.focus >= 0) {
			// 	plus.device.setVolume(0.1)
			// } else if (data.focus < 40 && data.focus >= 30) {
			// 	plus.device.setVolume(0.2)
			// } else if (data.focus < 50 && data.focus >= 40) {
			// 	plus.device.setVolume(0.3)
			// } else if (data.focus < 60 && data.focus >= 50) {
			// 	plus.device.setVolume(0.4)
			// } else if (data.focus < 70 && data.focus >= 60) {
			// 	plus.device.setVolume(0.5)
			// } else if (data.focus < 80 && data.focus >= 70) {
			// 	plus.device.setVolume(0.6)
			// } else if (data.focus < 90 && data.focus >= 80) {
			// 	plus.device.setVolume(0.7)
			// } else {
			// 	plus.device.setVolume(0.8)
			// }
			// if (data.focus < 25 && data.focus >= 0) {
			// 	state.videoBg = 1
			// 	state.valueLeft = 63
			// } else if (data.focus < 40 && data.focus >= 25) {
			// 	state.videoBg = 0.9
			// 	state.valueLeft = 186
			// } else if (data.focus < 50 && data.focus >= 40) {
			// 	state.videoBg = 0.75
			// 	state.valueLeft = 255
			// } else if (data.focus < 60 && data.focus >= 50) {
			// 	state.videoBg = 0.55
			// 	state.valueLeft = 315
			// } else if (data.focus < 70 && data.focus >= 60) {
			// 	state.videoBg = 0.25
			// 	state.valueLeft = 375
			// } else {
			// 	state.videoBg = 0
			// 	state.valueLeft = 490
			// }
		})
		postData()
	})
	const postData = () => {
		qryRelaxTrainData({
			traineeId: loginStore.qryActiveTrainee,
			beginDate: state.nowData,
			trainType: 'video',
			trainFileName: props.name
		}).then(res => {

		})
	}
	const onended = () => {
		console.log('视频结束关闭');
		if (socket.socketTask) {
			socket.socketTask.closeSocket()
		}
	}
	watch(() => state.focusArr, (focusArr) => {
		if (focusArr.length >= 5) {

			if (average(focusArr) > state.videoFocus) {
				let num = average(focusArr) - state.videoFocus
				let speed = num / 5
				if (!timeRef.value) {
					timeRef.value = setInterval(() => {
						state.videoFocus = state.videoFocus + speed
						setScreenBrightness(state.videoFocus * 0.01)
						state.valueLeft = state.videoFocus * 5.7
						if (state.videoFocus >= average(focusArr)) {
							state.focusArr = []
							clearInterval(timeRef.value)
							timeRef.value = null
						}
					}, 600)
				}

			} else if (average(focusArr) < state.videoFocus) {
				let num = state.videoFocus - average(focusArr)
				let speed = num / 5
				if (!timeRef.value) {
					timeRef.value = setInterval(() => {
						state.videoFocus = state.videoFocus - speed
						setScreenBrightness(state.videoFocus * 0.01)
						state.valueLeft = state.videoFocus * 5.7
						if (state.videoFocus <= average(focusArr)) {
							state.focusArr = []
							clearInterval(timeRef.value)
							timeRef.value = null
						}
					}, 600)
				}
			} else {
				state.videoFocus = average(focusArr)
				state.focusArr = []
			}

		}
	}, {
		deep: true
	})
	watch(() => state.videoFocus, (videoFocus) => {
		if (videoFocus === 0) {
			setScreenBrightness(0)
			state.valueLeft = 0
			state.videoBg = 1
		}
		// if (videoFocus < 30 && videoFocus >= 0) {
		// 	setScreenBrightness(0)
		// } else if (videoFocus < 40 && videoFocus >= 30) {
		// 	setScreenBrightness(0.1)
		// } else if (videoFocus < 50 && videoFocus >= 40) {
		// 	setScreenBrightness(0.25)
		// } else if (videoFocus < 60 && videoFocus >= 50) {
		// 	setScreenBrightness(0.4)
		// } else if (videoFocus < 70 && videoFocus >= 60) {
		// 	setScreenBrightness(0.55)
		// } else if (videoFocus < 80 && videoFocus >= 70) {
		// 	setScreenBrightness(0.7)
		// } else if (videoFocus < 90 && videoFocus >= 80) {
		// 	setScreenBrightness(0.85)
		// } else {
		// 	setScreenBrightness(1)
		// }
	})
	onUnmounted(() => {
		if (socket.socketTask) {
			socket.socketTask.closeSocket()
		}
	})
	watch(() => helper.bluData, (bluData) => {
		if (bluData.signal == 1) {
			state.videoBg = 1
			state.focus = 0
			state.videoFocus = 0
		}

	})

	function average(nums) {
		return nums.reduce((a, b) => a + b) / nums.length;
	}
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			const ret = JSON.parse(state)
			helper.bluData = ret
			if (socket.socketTask && !socket.socketTask.userClose && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(state)
			}
		})
	}

	const videoErrorCallback = (err) => {
		console.log(err);
	}
</script>

<style lang="scss">
	.relaxVideo {
		width: 100vw;
		height: 100vh;

		&-value {
			padding: 18rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			&-tip {
				width: 304rpx;
				margin-bottom: 22rpx;
			}

			&-num {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
				width: 96%;
				height: 62rpx;
				background: #EEF8FF;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 40rpx;

				&-max {
					display: flex;
					align-items: center;
				}
			}
		}

		&-video {
			width: 100vw;
			height: 450rpx;

			&-coverview {
				width: 100%;
				height: 100%;
			}
		}

		&-num {
			padding-top: 12rpx;
			width: 100vw;
			height: 245rpx;
			border-bottom: 10px solid #F6F6F6;
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;

			&-top {
				display: flex;
				align-items: center;
				width: 100%;
				justify-content: space-between;
				padding: 0 66rpx;

				&-right {
					margin-top: 16rpx;
					font-size: 40rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #1F2636;
					margin-left: 12rpx;
				}

				&-left {
					display: flex;
					position: relative;
					height: 40rpx;

					&-value {
						position: absolute;
						border: 14rpx solid transparent;
						border-top-color: #27A7FF;
						border-radius: 2rpx;
						top: -8rpx;
						left: 0;

						&-text {
							width: 60rpx;
							position: absolute;
							right: -38rpx;
							top: -34rpx;
							color: #111111;
							font-size: 14rpx;
						}
					}

					&-bg {
						margin-top: 10rpx;
						width: 600rpx;
						height: 60rpx;
						background: url('~@/static/relax/video-value-bg.png');
						background-repeat: no-repeat;
						background-size: 600rpx 60rpx;
					}
				}
			}

			&-text {
				font-size: 24rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #989DA9;
				margin-top: 20rpx;
			}
		}
	}
</style>