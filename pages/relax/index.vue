<template>
	<view class="relax">
		<image src="/static/relax/relax-sun.gif" v-if="state.showSun" mode="widthFix" style="width: 100%"></image>
		<image src="/static/relax/relax-rain.gif" v-else mode="widthFix" style="width: 100%"></image>
		<view class="relax-time">
			<view class="relax-time-text">
				剩余时间
			</view>
			<view class="relax-time-value">
				<view class="relax-time-value-box center DINAlternate-Bold">
					{{state.time.slice(0,1)}}
				</view>
				<view class="relax-time-value-box center DINAlternate-Bold">
					{{state.time.slice(1,2)}}
				</view>
				<view class="relax-time-value-icon">
					:
				</view>
				<view class="relax-time-value-box center DINAlternate-Bold">
					{{state.time.slice(3,4)}}
				</view>
				<view class="relax-time-value-box center DINAlternate-Bold">
					{{state.time.slice(4,5)}}
				</view>
			</view>
		</view>
		<view class="relax-tree">
			<view class="relax-tree-box">
				<image src="/static/relax/tree-1.png" class="relax-tree-box-img" mode="widthFix"></image>
				<image v-if="state.level==1" src="/static/relax/tree-1.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else-if="state.level==2" src="/static/relax/tree-2.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else-if="state.level==3" src="/static/relax/tree-3.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else-if="state.level==4" src="/static/relax/tree-4.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else-if="state.level==5" src="/static/relax/tree-5.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else-if="state.level==6" src="/static/relax/tree-6.png" class="relax-tree-box-img2" mode="widthFix"></image>
				<image v-else src="/static/relax/tree-7.png" class="relax-tree-box-img2" mode="widthFix"></image>
			</view>
			<image :src="`/static/relax/relax-${props.url}.gif`" class="relax-tree-panda" mode="widthFix"></image>
		</view>
		<view class="relax-box">
			<view class="relax-box-top">
				<view class="relax-box-top-left">
					<view class="relax-box-top-left-value" :style="{width:state.focusData.relaxation*5.6+'rpx'}">

					</view>
					<view class="relax-box-top-left-bg">

					</view>
				</view>
				<view class="relax-box-top-right">
					{{state.focusData.relaxation}}
				</view>
			</view>
			<view class="relax-box-text">
				当你越来越放松，达到不同阶段，树木会逐渐长叶、开花、结果。
			</view>
			<view class="relax-box-btn" @click="onEnd">
				<image class="relax-box-btn-img" src="/static/relax/relax-btn.png" mode="widthFix"></image>
			</view>
		</view>
		<uni-popup ref="popup" type="center" :animation="false" :is-mask-click="false">
			<view class="relax-end">
				<EchartVue :echData="state.echData" />
			</view>
		</uni-popup>
		<view class="relax-tree-box-imgLeft">
			<view class="relax-tree-box-imgLeft-item" v-for="(item,index) in 6">
				<view class="relax-tree-box-imgLeft-item-mask center" v-if="state.level===6-index">
					<view class="relax-tree-box-imgLeft-item-mask-value center">
						{{state.maintenanceTime}}
					</view>
				</view>
				<image class="relax-tree-box-imgLeft-item-value" :src="`/static/relax/relax-tree-${6-index}.png`"></image>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		formatSeconds
	} from '../../common/method';
	import {
		qryRelaxTrainData
	} from '../../service/relax';
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		setNavigationBarTitle,
		setKeepScreenOn
	} from '../../common/uniTool';
	import EchartVue from './Echarts.vue';
	import {
		useHelper
	} from '@/stores/helper.js'
	import {
		useSocketStore
	} from "../../stores/socket";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		wsUrl
	} from '../../common/global';
	import ws from '@/utils/websocket.js'
	import {
		BleController
	} from '@/utils/bluType';
	const loginStore = useLoginStore(); //用户基本信息
	const socket = useSocketStore(); //websocket仓库
	const helper = useHelper(); //设备仓库
	const innerAudioContext = uni.createInnerAudioContext()
	const timeRef = ref(null)
	const popup = ref(null)
	const props = defineProps(['url', 'name'])
	setNavigationBarTitle(props.name)

	innerAudioContext.src = `../../static/audio/relax/relax-${props.url}.MP3`
	innerAudioContext.play()
	innerAudioContext.onPlay(() => {
		timeRef.value = setInterval(() => {
			state.time = formatSeconds(Math.floor(innerAudioContext.duration - innerAudioContext.currentTime), false)
		}, 1000)
	})
	innerAudioContext.onEnded(() => {
		onEnd()
	})
	const onEnd = () => {
		clearInterval(timeRef.value)
		timeRef.value = null
		innerAudioContext.stop()
		postData()

	}
	const postData = () => {
		qryRelaxTrainData({
			traineeId: loginStore.qryActiveTrainee,
			beginDate: state.nowData,
			trainType: 'audio',
			trainFileName: props.name
		}).then(res => {
			state.echData = res.data
			popup.value.open('open')
			if (socket.socketTask) {
				socket.socketTask.closeSocket()
			}
			console.log(res);
		})
	}
	const state = reactive({
		time: '00:00',
		nowData: new Date(),
		focusData: {
			maxFocus: 0,
			avgFocus: 0,
			focus: 0,
			time: 0,
			relaxation: 0
		},
		echData: null, //最后结果
		maintenanceTime: 0, //维持时间
		level: 1, //档位
		showSun: false
	})
	onMounted(() => {
		console.log(props.url);
		setKeepScreenOn()
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address)
		}

		ownBluInit()
		socket.socketTask && socket.socketTask.getWebSocketMsg((data) => {
			console.log(data);
			state.focusData = data
		})
	})
	watch([() => state.maintenanceTime, () => state.level], ([maintenanceTime, level]) => {
		console.log(maintenanceTime);
		if (maintenanceTime == 30 && level == 1) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 2
			state.maintenanceTime = 0
		}
		if (maintenanceTime == 25 && level == 2) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 3
			state.maintenanceTime = 0
		}
		if (maintenanceTime == 20 && level == 3) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 4
			state.maintenanceTime = 0
		}
		if (maintenanceTime == 15 && level == 4) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 5
			state.maintenanceTime = 0
		}
		if (maintenanceTime == 10 && level == 5) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 6
			state.maintenanceTime = 0
		}
		if (maintenanceTime == 5 && level == 6) {
			state.showSun = true
			setTimeout(() => {
				state.showSun = false
			}, 3000)
			state.level = 7
			state.maintenanceTime = 0
		}
	})
	watch([() => state.focusData, () => state.level], ([focusData, level]) => {
		if (focusData.relaxation >= 35 && level === 1) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 35 && level === 1 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}

		if (focusData.relaxation >= 50 && level == 2) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 50 && level === 2 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}

		if (focusData.relaxation >= 60 && level == 3) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 60 && level === 3 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}

		if (focusData.relaxation >= 70 && level == 4) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 70 && level === 4 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}
		if (focusData.relaxation >= 80 && level == 5) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 80 && level === 5 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}
		if (focusData.relaxation >= 90 && level == 6) {
			state.maintenanceTime++
		} else if (focusData.relaxation < 90 && level === 6 && state.maintenanceTime > 0) {
			state.maintenanceTime--
		}
	})

	const getUrl = (value) => {
		let url = 1
		switch (true) {
			case value < 20:
				url = 1
				break;
			case value >= 20 && value <= 40:
				url = 2
				break;
			case value > 40 && value <= 60:
				url = 3
				break;
			case value >= 60 && value <= 80:
				url = 4
				break;
			case value >= 80 && value <= 100:
				url = 5
				break;
			default:
				break;
		}

		return url
	}
	onUnmounted(() => {
		if (socket.socketTask) {
			socket.socketTask.closeSocket()
		}
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			if (socket.socketTask && !socket.socketTask.userClose && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(state)
			}
		})
	}



	onUnmounted(() => {
		innerAudioContext.stop()
	})
</script>

<style lang="scss">
	.relax {
		width: 100vw;
		flex: 1;
		background: url('~@/static/relax/relax-bg.png');
		background-repeat: no-repeat;
		background-size: 100% auto;
		position: relative;

		&-end {
			width: 88vw;
			height: 616rpx;
			background: url('~@/static/relax/relax-end.png');
			background-repeat: no-repeat;
			background-size: 88vw auto;
		}

		&-box {
			position: absolute;
			width: 90%;
			height: 260rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(215, 228, 237, 1);
			border-radius: 24rpx;
			bottom: 10%;
			left: 50%;
			transform: translateX(-50%);
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			&-text {
				font-size: 22rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #989DA9;
				margin: 24rpx 0 34rpx 0;
			}

			&-btn {
				width: 204rpx;
			}

			&-top {
				display: flex;
				align-items: center;
				width: 95%;

				&-right {
					font-size: 40rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #1F2636;
					margin-left: 12rpx;
					flex: 1;
				}

				&-left {
					display: flex;
					position: relative;
					height: 40rpx;
					width: 606rpx;

					&-value {
						width: 266rpx;
						height: 40rpx;
						background: #36AFFF;
						border-radius: 20rpx;
					}

					&-bg {
						height: 40rpx;
						background: #D6EDFC;
						border-radius: 20rpx;
						width: 100%;
						background: #D6EDFC;
						position: absolute;
						left: 0;
						top: 0;
						z-index: -1;
					}
				}
			}
		}

		&-tree {
			position: absolute;
			top: 160rpx;
			left: 50%;
			transform: translateX(-50%);

			&-box {
				width: 480rpx;

				&-img {
					width: 480rpx;
				}

				&-img2 {
					width: 480rpx;
					position: absolute;
					top: 0;
					left: 0;
					animation: fadebackground 3s ease;
					animation-fill-mode: forwards;
					animation-play-state: running;
				}

				&-imgLeft {
					position: absolute;
					top: 160rpx;
					left: 36rpx;
					width: 90rpx;

					&-item {
						position: relative;

						&-mask {
							border-radius: 14rpx;
							width: 94rpx;
							height: 98rpx;
							position: absolute;
							top: 0;
							left: 0;
							background: rgba(17, 17, 17, 0.5);
							z-index: 1;


							&-value {
								width: 52rpx;
								height: 52rpx;
								border: 2rpx solid #FFFFFF;
								border-radius: 50%;
								font-size: 28rpx;
								font-family: PingFangSC-Medium, PingFang SC;
								font-weight: 500;
								color: #FFFFFF;
							}
						}

						&-value {
							width: 94rpx;
							height: 98rpx;
						}
					}
				}
			}


			&-panda {
				position: absolute;
				width: 240rpx;
				right: -100rpx;
				bottom: 0;
			}
		}

		&-time {
			width: 440rpx;
			height: 78rpx;
			background: #989DA9;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			position: absolute;
			top: 40rpx;
			left: 50%;
			transform: translateX(-50%);

			&-text {
				font-size: 32rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;
			}

			&-value {
				display: flex;
				align-items: center;
				font-size: 32rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;

				&-icon {
					margin-left: 8rpx;
				}

				&-box {
					width: 36rpx;
					height: 46rpx;
					background: #FFFFFF;
					border-radius: 8rpx;
					font-size: 40rpx;
					font-family: DINAlternate-Bold;
					font-weight: bold;
					color: #36AFFF;
					margin-left: 10rpx;
				}
			}
		}
	}

	@keyframes fadebackground {
		from {
			opacity: 0;
		}

		to {
			opacity: 1;
		}
	}
</style>