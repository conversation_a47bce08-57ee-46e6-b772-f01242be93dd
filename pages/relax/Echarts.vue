<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="relaxEcharts.onClick" :echData="echData" :change:echData="relaxEcharts.updateEcharts" id="relaxEcharts" class="relaxEcharts"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: 'Echarts',
		props: {
			echData: {
				type: Object
			}
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="relaxEcharts" lang="renderjs">
	let data = []
	let dataX = []
	let myChart
	export default {
		data() {
			return {
				option: {
					xAxis: {
						type: 'category',
						data: data
					},
					grid: {
						left: '8%',
						right: '6%',
						bottom: '6%',
						top: '2%'
					},
					visualMap: {
						show: false,
						pieces: [{
								gt: 0,
								lte: 40,
								color: '#FC7D02'
							},
							{
								gt: 40,
								lte: 50,
								color: '#93CE07'
							},
							{
								gt: 50,
								lte: 100,
								color: '#FBDB0F'
							}
						],
						outOfRange: {
							color: '#999'
						}
					},
					yAxis: {
						type: 'value',
						max: 100
					},
					series: [{
						data: dataX,
						type: 'line',
						smooth: true,
						markLine: {
							silent: true,
							lineStyle: {
								color: '#333'
							},
							data: [{
									yAxis: 40
								},
								{
									yAxis: 50
								},
								{
									yAxis: 100
								}
							]
						}
					}]
				}
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('relaxEcharts'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				console.log(":newValue " + JSON.stringify(newValue));
				this.option.xAxis.data = newValue['time']
				this.option.series[0].data = newValue['data']
				// 监听 service 层数据变更
				myChart && myChart.setOption(this.option)
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 363rpx;
		position: absolute;
		bottom: 40rpx;
		left: 0;
	}

	.relaxEcharts {
		width: 100%;
		height: 100%;
	}
</style>