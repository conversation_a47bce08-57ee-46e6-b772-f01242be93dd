<template>
	<view class="introduce" :class="props.type==2?'introduce-bg':''" @click="hideTip">
		<scroll-view class="introduce-value" :scroll-y="true" v-if="props.type==1" :style="{ 'height': screenPortrait?'45vh':'' }">
			<image src="/static/relax/relax-audio-img1.png" mode="widthFix" @click="()=>go(1,1)"></image>
			<image src="/static/relax/relax-audio-img2.png" mode="widthFix" style="margin-top: 16rpx;" @click="()=>go(2,1)"></image>
		</scroll-view>
		<scroll-view class="introduce-value" v-else :scroll-y="true" :style="{ 'height': screenPortrait?'45vh':'' }">
			<view class="introduce-value-item" v-for=" (item,index) in imgList" :key="item.name" @click="()=>subMoney(item,index)">
				<image :src="item.img" mode="widthFix" class="introduce-value-item-left"></image>
				<view class="introduce-value-item-center">
					<view class="introduce-value-item-center-title">
						{{item.name}}
					</view>
					<view class="introduce-value-item-center-text">
						{{item.text}}
					</view>
				</view>
				<view class="introduce-value-item-right">
					<view class="introduce-value-item-right-text">
						消耗脑力值
					</view>
					<image class="introduce-value-item-right-img" src="/static/relax/relax-mp4-money.png" mode="widthFix"></image>
					<view class="introduce-value-item-right-text">
						{{!index?'0':'15'}}个
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="introduce-tip">
			<view class="introduce-tip-title">
				训练技能
			</view>
			<view v-for="(item,index) in props.type==1?skill1:skill2" :key="item.name" class="introduce-tip-list" @click.stop="()=>showTip(item)">
				<view class="introduce-tip-list-other" :style="`top: ${state.tipHeight}px`" v-if="item.index===state.showIndex" :id="`relax-${item.index}`">
					<view class="introduce-tip-list-other-value">
						{{item.value}}
					</view>
				</view>
				<text class="introduce-tip-list-icon iconfont">{{item.icon}}</text>
				<text class="introduce-tip-list-text">{{item.name}}</text>
				<!-- <text class="introduce-tip-list-icon iconfont">&#xe618;</text> -->
			</view>
		</view>
		<view class="introduce-money center" v-if="props.type == 2">
			脑力值 <text style="font-size: 24rpx;margin-left:10rpx ;">{{loginStore.qryPoints}}</text>
		</view>
	</view>
</template>

<script setup>
	import {
		navigateTo,
		setNavigationBarTitle,
		showToast,
		setKeepScreenOn
	} from '../../common/uniTool';
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		onMounted,
		reactive,
		ref,
		onUnmounted
	} from "vue";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		changeSubPoints
	} from '../../service/relax'
	import {
		BleController
	} from '@/utils/bluType';
	import {
		throttle
	} from 'lodash';;
	const loginStore = useLoginStore(); //用户基本信息
	const props = defineProps(['type'])
	const screenPortrait = ref(true) //是否竖屏

	if (props.type == 1) {
		setNavigationBarTitle('放松空间')
	} else {
		setNavigationBarTitle('动画城解压')
	}
	onUnmounted(() => {
		uni.offWindowResize(windowResizeCallback)
	})
	onShow(() => {
		// #ifdef APP-PLUS
		BleController.addDeviceAcceptListListen(() => {})
		// #endif
	})
	onMounted(() => {
		setKeepScreenOn()
		uni.onWindowResize(windowResizeCallback, 1000);
	})
	const windowResizeCallback = throttle((res) => {
		changeScreenPortrait(res)
	}, 1000)
	const changeScreenPortrait = (res) => {
		if (res.size.windowHeight > 1000) {
			screenPortrait.value = false
		} else {
			screenPortrait.value = true
		}
	}
	const subMoney = (item, index) => {
		changeSubPoints({
			traineeId: loginStore.qryActiveTrainee,
			relaxType: 'VIDEO',
			free: !index ? true : false
		}).then(res => {
			loginStore.getPoints(loginStore.qryActiveTrainee)
			go(item, 2)
		}).catch(err => {
			showToast(err.desc)
		})
	}
	const go = (type, mold) => {
		if (mold == 2) {
			navigateTo(`/pages/relax/relaxVideo?url=${type.value}&name=${type.name}`)
		} else {
			navigateTo(`/pages/relax/relaxList?type=${type}`)
		}
	}
	const hideTip = () => {
		state.showIndex = -1
	}
	const showTip = (item) => {
		state.showIndex = item.index
		setTimeout(() => getTipHeight(item.index), 1)
	}
	const getTipHeight = (showIndex) => {
		//获取元素信息
		let info = uni.createSelectorQuery().select(`#relax-${showIndex}`);
		info.boundingClientRect(function (data) { //data - 各种参数
			// console.log(data.height) // 获取元素宽度
			state.tipHeight = -data.height - 15
		}).exec()

	}
	const skill1 = [{
		name: '听觉感知',
		value: '使大脑更有效的检测和解释到达耳朵的信息，可以促进学习，尤其是语言和阅读的获得。',
		icon: '\ue69e',
		index: '1'
	}, {
		name: '非语言记忆',
		value: '使大脑能够保留视觉或听觉信息，有助于管理一般不包含文字或语言内容的信息。',
		icon: '\ue65d',
		index: '2'
	}]
	const skill2 = [{
		name: '注意力集中',
		value: '集中在我们周围的刺激上，使我们保持专注。',
		icon: '\ue69e',
		index: '3'
	}, {
		name: '视觉感知',
		value: '对于正确阅读单词和识别图像或对象至关重要，有助于阅读和其他视觉活动。',
		icon: '\ue65d',
		index: '4'
	}]
	const imgList = [{
			name: '各种花朵的绽放',
			value: 'TomAndJerry',
			text: '时长：01:00',
			img: '../../static/relax/relax-mp4-img1.png'
		},
		{
			name: '历经千年的长城烽火台',
			value: 'Conan',
			text: '时长：02:33',
			img: '../../static/relax/relax-mp4-img2.png'
		}, {
			name: '太阳照耀下的地球',
			value: 'CrayonShinchan',
			text: '时长：01:44',
			img: '../../static/relax/relax-mp4-img3.png'
		}
	]
	const state = reactive({
		tipHeight: -27,
		showIndex: -1,
	})
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		background: url('~@/static/relax/relax-audio-bg.png') #F4F7F9;
		background-repeat: no-repeat;
		background-size: 100% auto;
		position: relative;

		&-money {
			position: absolute;
			top: 207rpx;
			right: 32rpx;
			width: 170rpx;
			height: 48rpx;
			background: #111111;
			border-radius: 8rpx;
			font-size: 16rpx;
			font-family: SourceHanSansCN-Normal, SourceHanSansCN;
			font-weight: 400;
			color: #FFFFFF;
		}

		&-tip {
			width: 92%;
			position: absolute;
			bottom: 40rpx;
			left: 50%;
			transform: translateX(-50%);
			background: #FFFFFF;
			border-radius: 8rpx;

			&-list {
				margin: 0 auto;
				width: 96%;
				padding: 13rpx 10rpx 13rpx 24rpx;
				font-size: 24rpx;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #111111;
				display: flex;
				align-items: center;
				border-bottom: 2rpx solid #E3E3E3;
				position: relative;

				&-other {
					display: flex;
					flex-direction: column;
					flex-wrap: wrap;
					padding: 32rpx;
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					background: #FFFFFF;
					box-shadow: 8rpx 8rpx 16rpx 0rpx rgba(0, 0, 0, 0.15), 2rpx -2rpx 16rpx 0rpx rgba(0, 0, 0, 0.3);
					border-radius: 5rpx;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #111111;
					width: 100%;
					z-index: 99;

					&-title {
						font-size: 13rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #FFFFFF;
						margin-bottom: 10rpx;
					}
				}

				&-other::after {
					content: ' ';
					position: absolute;
					left: 50%;
					bottom: -16rpx;
					transform: translateX(-50%);
					border: 8rpx solid transparent;
					border-top-color: #FFFFFF;
				}

				&-text {
					flex: 1;
					margin-left: 12rpx;
				}

				&-icon {
					font-size: 30rpx;
				}
			}

			&-list:last-child {
				border: none;
			}

			&-title {
				font-size: 28rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #111111;
				padding: 16rpx 24rpx;
				border-bottom: 2rpx solid #E3E3E3;
			}
		}

		&-bg {
			background: url('~@/static/relax/relax-mp4-bg.png') #F4F7F9;
			background-repeat: no-repeat;
			background-size: 100% auto;
		}

		&-value {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			position: absolute;
			top: 25%;
			left: 50%;
			transform: translateX(-50%);
			padding: 0 32rpx;

			&-item {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx 36rpx 24rpx 24rpx;
				background: #FFFFFF;
				border-radius: 16rpx;
				margin-bottom: 16rpx;

				&-left {
					width: 134rpx;
					height: 134rpx;
				}

				&-center {
					display: flex;
					flex-direction: column;
					flex: 1;
					margin-left: 24rpx;
					justify-content: space-around;

					&-title {
						font-size: 32rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #111111;
						margin-bottom: 44rpx;
					}

					&-text {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #111111;
					}
				}

				&-right {
					width: 126rpx;
					height: 114rpx;
					background: #FFF0DE;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					flex-direction: column;
					justify-content: space-around;

					&-text {
						font-size: 20rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #C67D42;
					}

					&-img {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
		}
	}
</style>