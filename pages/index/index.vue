<template>
	<view class="front">
		<view class="front-content">
			<view class="front-top">
				<view class="front-top-hello">{{getHello()}}
					<view class="front-top-hello-text">
						欢迎使用脑机智能测评训练系统
					</view>
				</view>
				<view class="front-top-add center" @click="goDevice">
					<image :src="helper.address?'/static/index/blu-icon.png':'/static/index/blu-no-icon.png'" class="front-top-add-icon"></image> {{helper.address?'查看':'添加'}}BCI设备
				</view>
			</view>
			<view class="front-add center" v-if="loginStore.visitorList.length===0" @click="goInfo">
				请先添加亲属 <text class="front-add-text1">开始数字测评训练</text> <text class="front-add-text2">前往添加</text>
			</view>
			<view class="front-info" v-else>
				<view class="front-info-list">
					<view class="front-info-list-item" :key="item.idnum" v-for="(item,index) in loginStore.visitorList" @click="()=>changeTab(item.traineeId)">
						<view class="front-info-list-item-click" v-if="item.traineeId==loginStore.qryActiveTrainee">
							<image class="front-info-list-item-click-icon" :src="item.sex=='1'?'/static/index/quick-boy.png':'/static/index/quick-girl.png'"></image>
							<view class="front-info-list-item-click-content">
								<view class="front-info-list-item-click-content-top">
									{{item.traineeName.length>=3?item.traineeName.slice(0,3):item.traineeName}}
								</view>
								<view class="front-info-list-item-click-content-bottom">
									{{item.age}}
									<!-- <text class="front-info-list-item-click-content-bottom-text">{{ getRela(item.relation)}}</text> -->
								</view>
							</view>
						</view>
						<view class="front-info-list-item-noclick center" v-else>
							{{item.traineeName.length>=3?item.traineeName.charAt(0):item.traineeName}}
						</view>
					</view>
				</view>
				<view class="front-info-money">
					<view class="front-info-money-left">
						<image class="front-info-money-left-img" src="/static/shopping/shopping-money-icon.png" mode="widthFix"></image>
						<text>我的脑力值</text>
					</view>
					<text>{{loginStore.qryPoints}}</text>
					<view class="" @click="goList">
						查看明细 <text class="iconfont">&#xe618;</text>
					</view>
				</view>
				<view class="front-info-more center" v-if="loginStore.visitorList.length>=1" @click="navigateTo('/pages/home/<USER>/index')">
					<text class="iconfont" style="margin-bottom: 8rpx;">&#xe634;</text>管理
				</view>
			</view>
		</view>
		<view class="front-noWife center" v-if="!loginStore.network">
			<image class="front-noWife-img" src="/static/index/network-error.png" mode="widthFix"></image> 当前无法连接网络，可检查网络设置是否正常。
		</view>
		<uni-transition ref="ani" :mode-class="state.modeClass" :styles="state.styles" :show="state.modeShow" @click="go">
			<view v-if="state.lastData">
				<image class="front-train-img" src="/static/index/train-bg.png"></image>
				<view class="front-train-right">
					<view class="front-train-right-time">
						<text class="front-train-right-time-icon iconfont">&#xe74f;</text> 距离上次测评报告时间 <text class="front-train-right-time-value">{{state.lastData.timeBetween}}</text>天
					</view>
					<view class="front-train-right-title">
						认知测评结果
					</view>
					<view class="front-train-right-box">
						<view class="front-train-right-box-content" v-if="state.lastData.signalNormalRsp">
							反应信号反应时： <text class="front-train-right-box-content-value">{{state.lastData.signalNormalRsp}}ms</text>
						</view>
						<view class="front-train-right-box-content" v-if="state.lastData.signalStopRate">
							成功抑制率：<text class="front-train-right-box-content-value">{{state.lastData.signalStopRate}}%</text>
						</view>
						<view class="front-train-right-box-content" v-if="state.lastData.memoryBre">
							记忆广度值：<text class="front-train-right-box-content-value">{{state.lastData.memoryBre}}</text>
						</view>
						<view class="front-train-right-box-content" v-if="state.lastData.canAleExp">
							注意指数：<text class="front-train-right-box-content-value">{{state.lastData.canAleExp}}</text>
						</view>
					</view>
					<!-- 				<view class="front-train-right-box" v-else>
						<view class="front-train-right-box-content" v-if="state.lastData.signalPercentDesc">
							抑制控制能力： <text class="front-train-right-box-content-value">{{state.lastData.signalPercentDesc}}</text>
						</view>
						<view class="front-train-right-box-content" v-if="state.lastData.memoryPercentDesc">
							短时记忆能力：<text class="front-train-right-box-content-value">{{state.lastData.memoryPercentDesc}}</text>
						</view>

					</view> -->
					<view class="front-train-right-btn center" @click.stop="goReport">
						查看测评报告
					</view>
				</view>
			</view>
			<image v-else class="front-train-bg" src="/static/index/train-right.png" mode="widthFix"></image>
			<view class="front-train-left">
				<view class="front-train-left-text">
					全面、客观、严谨
				</view>
				<view class="front-train-left-text1">
					国际权威量表 经典心理学范式
				</view>
				<view class="front-train-left-text1">
					报告详细解读 多通道BCI设备
				</view>
				<view class="front-train-left-btn center">
					快速认知测评
				</view>
			</view>
		</uni-transition>
		<view class="front-box">
			<uni-transition ref="ani" :mode-class="state.modeClass" :styles="state.taskStyles" :show="state.modeShow">
				<view class="front-box-title center" v-if="!helper.address">
					未连接BCI设备，仅支持体验部分训练功能
				</view>
				<view class="front-box-bci" :style="{marginTop:helper.address?0:'20rpx'}" v-if="helper.address">
					<view v-for="(item,index) in state.stepList" :key="item.title" class="front-box-bci-item" @click="()=>goStep(index)">
						<!-- <view class="front-box-bci-item-num center">
								第{{numToCapital(index+1)}}步
							</view> -->
						<view class="front-box-bci-item-left">
							<view class="front-box-bci-item-left-name">
								{{item.name}}
							</view>
							<view class="front-box-bci-item-left-title">
								{{item.title}}
							</view>
							<image :src="item.imgList" mode="heightFix" class="front-box-bci-item-left-img"></image>
						</view>
						<image class="front-box-bci-item-img" :src="item.img" mode="heightFix"></image>
					</view>
				</view>
				<view class="" v-else>
					<view class="front-box-relax">
						<image class="front-box-relax-img" src="/static/index/relax-left.png" mode="widthFix" @click="()=>goRelaxAu(1)"></image>
						<image class="front-box-relax-img" src="/static/index/relax-right.png" mode="widthFix" @click="()=>goRelaxAu(2)"></image>
					</view>
					<view class="front-box-train center">
						<image class="front-box-train-img" src="/static/index/index-train.png" mode="widthFix" @click="()=>goGame(1)"></image>
					</view>
					<view class="front-box-game">
						<view class="front-box-game-title">
							<view class="front-box-game-title-icon iconfont">
								&#xe739;
							</view>
							<view class="front-box-game-title-text">
								数字训练{{loginStore.prescriptionList.length===0?'-暂无开具的数字处方':''}}
							</view>
						</view>
						<view class="front-box-game-content">
							<view class="front-box-game-content-left">
								<view class="front-box-game-content-left-title">
									认知训练+BCI神经反馈训练
								</view>
								<view class="front-box-game-content-left-bottom">
									<image class="front-box-game-content-left-bottom-img" src="/static/index/game-icon.png" mode="widthFix"></image>
									<view class="front-box-game-content-left-bottom-box">
										<view class="">
											今日训练任务 <text class="front-box-game-content-left-bottom-box-value">无</text>
										</view>
										<view class="">
											当前状态 <text class="front-box-game-content-left-bottom-box-value">无</text>
										</view>
									</view>
								</view>
							</view>
							<view class="front-box-game-content-right" @click="goGame(3)">
								<view class="front-box-game-content-right-value">
									有效性：0%
								</view>
								<view class="front-box-game-content-right-value">
									有效时间：0分0秒
								</view>
								<view class="front-box-game-content-right-btn">
									{{loginStore.prescriptionList.length===0?'尝试体验':'前往训练'}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</uni-transition>
		</view>
	</view>
</template>

<script setup>
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		onTabItemTap
	} from '@dcloudio/uni-app'
	import {
		navigateTo,
		showToast,
		getStorageSync,
		showLoading,
		hideLoading
	} from "../../common/uniTool";
	import {
		getRela,
		numToCapital
	} from "../../common/method";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		updateQryActiveTrainee,
		getLastEvaluationResult,
		getQryIsHavePres,
		getQryNewPrescripTrain,
		getQryTraineePrescripPdf,
		checkExistCarrier
	} from "../../service";
	import {
		reportUrl,
		pdfUrl
	} from "../../common/global";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		useSocketStore
	} from '../../stores/socket';
	import {
		BleController
	} from '../../utils/bluType';
	const socket = useSocketStore(); //websocket仓库
	const loginStore = useLoginStore()
	const helper = useHelper(); //设备仓库

	const go = () => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		if (!loginStore.qryActiveTrainee) {
			showToast('请添加亲属')
			return
		}
		uni.navigateTo({
			url: '/pages/evaluation/index',
		});
	}
	const getData = async () => {
		await loginStore.getVisitor()
		await loginStore.getActiveTraineeInfo()
		if (loginStore.qryActiveTrainee && helper.address) {
			loginStore.getTraineePrescripList(loginStore.qryActiveTrainee)
		}
		loginStore.qryActiveTrainee && loginStore.getPoints(loginStore.qryActiveTrainee)
		getLastResult()
	}
	watch(() => loginStore.network, (network) => {
		if (network) {
			getData()
		}
	})
	onShow(() => {
		getData()
		uni.getNetworkType({ //首次判断网络状态
			success: (res) => {
				const networkType = res.networkType;
				if (networkType === 'none') { //none是没有网络
					loginStore.network = false
				} else {
					loginStore.network = true
				}
			},
		})
		uni.onNetworkStatusChange((res) => {
			//监听网络状态，状态发生变化时触发
			loginStore.network = res.isConnected
		})
		// #ifdef APP-PLUS
		if (socket.socketTask && !socket.socketTask.userClose) {
			socket.socketTask.closeSocket()
		}
		plus.navigator.setFullscreen(false);
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏

		ownBluInit()
		// #endif
		state.modeShow = false
		setTimeout(() => {
			state.modeShow = true
			state.modeClass = ['fade', 'slide-right']
		}, 1)
	})
	watch(() => helper.address, (address) => {
		if (address) {
			state.taskStyles = {
				'background': 'url("../../static/index/train-bg-address.png")',
				'backgroundSize': '100% auto',
				'backgroundRepeat': 'no-repeat',
				'marginTop': '12rpx',
				'padding': '46rpx 0 70rpx 0',
				'position': 'relative'
			}
		} else {
			state.taskStyles = {
				'background': 'url("../../static/index/task-bg.png")',
				'backgroundSize': '100% auto',
				'backgroundRepeat': 'no-repeat',
				'marginTop': '12rpx',
				'padding': '100rpx 0 70rpx 0',
				'position': 'relative'
			}
		}
	})
	watch(() => loginStore.qryActiveTrainee, (qryActiveTrainee) => {
		if (qryActiveTrainee) {
			loginStore.getPoints(qryActiveTrainee)
		}
		if (helper.address) {
			loginStore.getTraineePrescripList(loginStore.qryActiveTrainee)
		}
	})
	onMounted(() => {
		loginStore.getAppVersionData('index')
	})
	onUnmounted(() => {
		// console.log("1: ", 1);
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(() => {})
	}
	const goList = () => {
		navigateTo('/pages/home/<USER>/money')
	}
	const goRelaxAu = (type) => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		if (!loginStore.qryActiveTrainee) {
			showToast('请先添加亲属')
			return
		}
		navigateTo(`/pages/relax/introduce?type=${type}`)
	}
	const state = reactive({
		styles: {
			'height': '300rpx',
			'background': 'linear-gradient(155deg, #FFFFFF 0%, #D6E8FF 100%)',
			'margin': '0 auto',
			'marginTop': '32rpx',
			'borderRadius': '18rpx',
			'border': '1rpx solid #FFFFFF',
			'position': 'relative',
			'padding': '40rpx 20rpx',
			'width': '96%',
		},
		taskStyles: {
			'background': 'url("../../static/index/task-bg.png")',
			'backgroundSize': '100% auto',
			'backgroundRepeat': 'no-repeat',
			'marginTop': '12rpx',
			'padding': '100rpx 0 70rpx 0',
			'position': 'relative'
		},
		modeClass: 'fade',
		modeShow: true,
		lastData: null,
		popType: 0,
		stepList: [{
			title: '情绪改善、放松感知',
			name: '正念训练',
			imgList: '../../static/index/front-bci-relax-list.png',
			img: '../../static/index/front-bci-relax.png'
		}, {
			title: '注意力、自控力、记忆力',
			imgList: '../../static/index/front-bci-game-list.png',
			img: '../../static/index/front-bci-game.png',
			name: '脑机接口训练',
		}, {
			title: '保持专注、集中注意力',
			imgList: '../../static/index/front-bci-video-list.png',
			img: '../../static/index/front-bci-video.png',
			name: '脑电生物反馈训练',
		}]
	})
	const goStep = (index) => {
		if (!index) {
			goRelaxAu(1)
		} else if (index == 1) {
			goGame(2)
		} else {
			goRelaxAu(2)
		}
	}



	const getLastResult = () => {
		getLastEvaluationResult({}).then(res => {
			if (res) {
				state.lastData = res.data
			}
		}).catch(err => {
			console.log(err);
		})
	}

	const goGame = (type) => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		if (!loginStore.qryActiveTrainee) {
			showToast('请先添加亲属')
			return
		}
		if (type === 3) {
			navigateTo('/pages/train/introduce')
			return
		}
		checkExistCarrier({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			if (res.data.checkResult) {
				navigateTo('/pages/game/growthPath')
			} else {
				navigateTo('/pages/shopping/index')
			}
		})
		// if (type === 1) {
		// 	navigateTo('/pages/game/introduce')
		// } else if (type == 2) {
		// 	navigateTo('/pages/train/directory')
		// } else {
		// 	navigateTo('/pages/train/introduce')
		// }
	}
	const close = () => {
		popup.value.close()
	}
	const goReport = () => {
		let tToken = getStorageSync('ttoken');
		navigateTo(`/pages/webview/index?url=${reportUrl}#/eegReport/${tToken}/${state.lastData.round}/${state.lastData.activeTraineeId}/SMSCODE?evaluatId=${state.lastData.evaluatId}`)
	}
	const getHello = () => {
		// 获取当前时间
		let timeNow = new Date();
		// 获取当前小时
		let hours = timeNow.getHours();
		// 设置默认文字
		let text = ``;
		// 判断当前时间段
		if (hours >= 0 && hours <= 10) {
			text = `早上好`;
		} else if (hours > 10 && hours <= 14) {
			text = `中午好`;
		} else if (hours > 14 && hours <= 18) {
			text = `下午好`;
		} else if (hours > 18 && hours <= 24) {
			text = `晚上好`;
		}
		// 返回当前时间段对应的状态
		return text;

	}

	onTabItemTap((e) => {
		// console.log(e);
	})
	const changeTab = (traineeId) => {
		state.modeShow = false
		updateQryActiveTrainee({
			traineeId
		}).then(res => {
			loginStore.getActiveTraineeInfo()
			getLastResult()
			state.modeShow = true
			state.modeClass = ['fade', 'slide-right']
		})
	}
	const clickItem = (e) => {
		state.swiperDotIndex = e
	}
	const change = (e) => {
		state.current = e.detail.current
	}
	const goDevice = () => {
		// navigateTo('/pages/index/blu')
		navigateTo('/pages/device/index')
	}
	const goInfo = () => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		navigateTo('/pages/informationForm/index?type=1')
	}
</script>

<style lang="scss">
	.front {
		width: 100vw;
		position: relative;
		background: url('~@/static/index/bg.jpg') #F6F6F6;
		background-repeat: no-repeat;
		background-size: 100vw;

		&-noWife {
			width: 100%;
			height: 64rpx;
			background: #FFE5E5;
			font-weight: 400;
			font-size: 24rpx;
			color: #FF0000;

			&-img {
				width: 24rpx;
				margin-right: 16rpx;
			}
		}

		&-content {
			padding: 0 32rpx;
			padding-top: 50rpx;
		}

		&-doctorQr {
			width: 600rpx;
		}

		&-end {
			width: 650rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			padding: 64rpx 0 60rpx 0;

			&-two2 {
				display: flex;
				align-items: center;
				justify-content: space-around;
				width: 100%;

				&-top {
					width: 246rpx;
					height: 80rpx;
					border-radius: 16rpx;
					border: 2rpx solid #0485F4;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #0485F4;
				}

				&-bottom {
					width: 246rpx;
					height: 80rpx;
					background: #0485F4;
					border-radius: 16rpx;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #FFFFFF;
				}
			}

			&-two1 {
				display: flex;
				flex-direction: column;
				align-items: center;


				&-top {
					width: 516rpx;
					height: 80rpx;
					background: #0485F4;
					border-radius: 16rpx;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #FFFFFF;
					margin-bottom: 16rpx;
				}

				&-bottom {
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #0485F4;
					width: 516rpx;
					height: 80rpx;
					border-radius: 16rpx;
					border: 2rpx solid #0485F4;
				}
			}

			&-title {
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #111111;
				margin-bottom: 48rpx;


			}

			&-img {
				width: 100%;
			}

			&-text {
				padding: 0 40rpx;
				width: 100%;
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				line-height: 48rpx;
				margin-bottom: 60rpx;

				&-min {
					color: #FF4747;
				}
			}

			&-btn {
				width: 416rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #FFFFFF;

			}
		}

		&-trainBg {
			width: 100%;
			margin-top: 18rpx;
		}

		&-swiper {
			margin: 22rpx 0;
			border-radius: 18rpx;

			&-item {
				width: 100%;
				background: #FFFFFF;
				border-radius: 18rpx;
				height: 100%;
			}
		}

		&-box {
			padding: 0 10rpx;
			padding-bottom: 40rpx;
			background: #F6F6F6;

			&-bci {
				width: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;

				&-item {
					height: 186rpx;
					background: #FFFFFF;
					border-radius: 16rpx;
					border: 3rpx solid #E4F0FF;
					width: 88%;
					justify-content: space-between;
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;
					padding: 32rpx 50rpx 24rpx 48rpx;
					position: relative;

					&-num {
						top: -2rpx;
						left: -2rpx;
						position: absolute;
						width: 136rpx;
						height: 40rpx;
						background: #4E8AFF;
						border-radius: 16rpx 0rpx 16rpx 0rpx;
						font-size: 24rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #FFFFFF;
					}

					&-left {
						display: flex;
						flex-direction: column;

						&-name {
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: bold;
							font-size: 32rpx;
							color: #207BFF;
						}

						&-title {
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: 400;
							font-size: 24rpx;
							color: #111111;
							line-height: 40rpx;
							margin-bottom: 13rpx;
						}

						&-img {
							height: 56rpx;
						}
					}

					&-img {
						height: 112rpx;
					}
				}
			}

			&-title {
				width: 100%;
				font-size: 34rpx;
				font-family: YouSheBiaoTiHei;
				color: #207BFF;
				position: absolute;
				top: 34rpx;
				left: 50%;
				transform: translateX(-50%);
			}

			&-train {
				width: 100%;

				&-img {
					width: 90%;
				}
			}

			&-relax {
				width: 94%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 10rpx;
				padding-left: 44px;

				&-img {
					width: 48%;

				}
			}

			&-game {
				padding: 0 52rpx;
				width: 100%;
				margin: 10rpx 0;

				&-title {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #207BFF;
					border-top: 4rpx solid #EEEEEE;
					padding-top: 12rpx;

					&-text {
						font-size: 28rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;
						margin-left: 8rpx;
					}
				}

				&-content {
					width: 100%;
					display: flex;
					justify-content: space-between;
					margin-top: 10rpx;

					&-right {
						font-size: 20rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						background: #F6F6F6;
						border-radius: 24rpx;
						display: flex;
						align-items: center;
						justify-content: space-around;
						flex-direction: column;
						padding: 0 24rpx;

						&-btn {
							border-radius: 8rpx;
							border: 1rpx solid #207BFF;
							font-size: 24rpx;
							font-family: PingFangSC-Medium, PingFang SC;
							font-weight: bold;
							color: #207BFF;
							padding: 8rpx 24rpx;
						}
					}

					&-left {
						display: flex;
						flex-direction: column;
						flex: 1;

						&-title {
							font-size: 24rpx;
							font-family: PingFangSC-Medium, PingFang SC;
							font-weight: bold;
							color: #397BFF;
							margin-bottom: 16rpx;
						}

						&-bottom {
							display: flex;
							align-items: center;

							&-img {
								width: 128rpx;
								height: 128rpx;
							}

							&-box {
								flex: 1;
								font-size: 24rpx;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: #111111;
								margin-left: 16rpx;
								height: 128rpx;
								display: flex;
								flex-direction: column;
								justify-content: space-around;

								&-value {
									color: #207BFF;
								}
							}
						}
					}
				}
			}
		}

		&-add {
			background: #FFFFFF;
			border-radius: 14px;
			font-size: 28rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #111111;
			margin-top: 32px;
			padding: 36rpx 24rpx;

			&-text1 {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #207BFF;
				margin: 0 20rpx;
			}

			&-text2 {
				background: linear-gradient(270deg, #207BFF 0%, #6CA8FF 100%);
				border-radius: 64rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #FFFFFF;
				padding: 10rpx 32rpx;
			}


		}

		&-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			z-index: 0;
		}

		&-top {
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-hello {
				font-size: 48rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #FFFFFF;

				&-text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					margin-top: 18rpx;
				}
			}

			&-add {
				background: #E9F2FF;
				border-radius: 48rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #000000;
				padding: 8rpx 24rpx 8rpx 8rpx;
				height: 72rpx;

				&-icon {
					width: 56rpx;
					height: 56rpx;
					margin-right: 8rpx;
				}
			}
		}



		&-info {
			display: flex;
			margin-top: 16px;
			position: relative;
			width: 100%;
			flex-direction: column;

			&-money {
				width: 100%;
				display: flex;
				align-items: center;
				height: 48rpx;
				background: rgba(17, 92, 203, 0.5);
				border-radius: 26rpx;
				padding: 0 40rpx;
				justify-content: space-between;
				margin-top: 16rpx;
				font-size: 20rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FFFFFF;

				&-left {
					display: flex;
					align-items: center;

					&-img {
						width: 32rpx;
						height: 32rpx;
						margin-right: 16rpx;
					}
				}
			}

			&-list {
				width: 88%;
				display: flex;
				overflow: hidden;
				overflow-x: auto;
				// margin-left: 102rpx;

				&-item {
					display: flex;
					align-items: flex-end;
					margin-left: 12px;
					flex-shrink: 0;
					position: relative;

					&-click {
						display: flex;
						align-items: center;
						padding: 12px;
						background: #FFFFFF;
						border-radius: 14px;
						border: 1px solid #559AFF;
						flex-direction: row;



						&-icon {
							width: 50px;
							height: 50px;
							flex-shrink: 0;
						}

						&-content {
							flex-shrink: 0;
							margin-left: 12px;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							height: 100%;

							&-top {
								font-size: 23px;
								font-family: PingFangSC-Semibold, PingFang SC;
								font-weight: bold;
								color: #207BFF;
								margin-bottom: 8px;
							}

							&-bottom {
								font-size: 17px;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: #207BFF;
								width: 100%;
							}
						}
					}



					&-noclick {
						width: 72rpx;
						height: 72rpx;
						background: #B4D6FF;
						border-radius: 14px;
						font-size: 14px;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: bold;
						color: #333333;

					}

				}

				&-item:first-child {
					margin-left: 0;
				}


			}

			&-add {
				height: 100%;
				width: 92rpx;
				background: #1567D2;
				border-radius: 16rpx;
				font-size: 16rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 6rpx;
				position: absolute;
				top: 0;
				left: 0;

			}

			&-more {
				color: #FFFFFF;
				width: 60rpx;
				background: #287FFF;
				border-radius: 8rpx;
				height: 80rpx;
				position: absolute;
				top: 0;
				right: 0;
				font-size: 18rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				display: flex;
				flex-direction: column;
				padding: 10rpx;
				height: 100rpx;
			}


		}

		&-train {
			margin-top: 42px;
			background: linear-gradient(155deg, #FFFFFF 0%, #D6E8FF 100%);
			border-radius: 18rpx;
			border: 1rpx solid #FFFFFF;
			position: relative;
			height: 260rpx;
			padding: 36rpx 44rpx;
			position: relative;

			&-bg {
				width: 92%;
				position: absolute;
				bottom: 0;
				right: 0;
			}

			&-left {
				&-text {
					font-size: 22rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #002252;
				}

				&-text1 {
					font-size: 26rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #002252;
					margin-top: 16rpx;
				}

				&-btn {
					font-size: 26rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #FFFFFF;
					width: 192rpx;
					height: 64rpx;
					background: #207BFF;
					border-radius: 11rpx;
					border: 0rpx solid #DEF0FF;
					margin-top: 30rpx;
				}
			}

			&-img {
				position: absolute;
				right: 14rpx;
				bottom: 12rpx;
				height: 276rpx;
				width: 260rpx;
			}

			&-right {
				width: 260rpx;
				position: absolute;
				right: 14rpx;
				bottom: 12rpx;
				height: 276rpx;
				display: flex;
				align-items: center;
				flex-direction: column;
				padding: 33rpx 0 0 0;

				&-time {
					font-size: 16rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #000000;

					&-icon {
						color: #FF8520;
					}

					&-value {
						color: #FF8520;
						font-size: 24rpx;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: bolder;
					}
				}

				&-title {
					font-size: 24rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #000000;
					margin: 10rpx 0;
				}

				&-box {
					height: 80rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-around;

					&-content {
						font-size: 16rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #000000;

						&-value {
							font-weight: bold;
							color: #4B93FF;
						}
					}
				}


				&-btn {
					font-size: 20rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #FFFFFF;
					width: 168rpx;
					height: 40rpx;
					background: #FF8520;
					border-radius: 43rpx;
					margin-top: 16rpx;
				}
			}
		}

		&-game {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			background: #FFFFFF;
			box-shadow: 0px 1px 6px 0px rgba(17, 17, 17, 0.1);
			border-radius: 14px;
			margin-top: 24px;
			padding: 49px 40px;

			&-btn {
				background: linear-gradient(360deg, #217CFF 0%, #5EBAFF 100%);
				border-radius: 28rpx;
				border: 0rpx solid #DEF0FF;
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				padding: 10rpx 46rpx;
				margin-top: 36rpx;
			}

			&-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;

				&-item {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-text {
						font-size: 17px;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;
						margin-bottom: 12px;
					}

					&-time {
						font-size: 26px;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: bolder;
						color: #111111;

						&-text {
							font-size: 12px;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
						}
					}
				}
			}
		}

		&-title {
			background: #FFFFFF;
			border-radius: 18rpx;
			display: flex;
			flex-direction: column;

			&-box {
				display: flex;
				align-items: center;
				padding-left: 8rpx;
				padding-bottom: 12rpx;

				&-text {
					margin-left: 18rpx;
				}

				&-img {
					width: 100rpx;
				}
			}
		}

	}
</style>