<template>
	<view class="trend">
		<view class="trend-selet">
			<view class="trend-selet-btn center" :class="timeType==='WEEK'?'trend-selet-btn-click':''" @click="changeTimeType('WEEK')">
				最近7天
			</view>
			<view class="trend-selet-btn center" :class="timeType==='MONTH'?'trend-selet-btn-click':''" @click="changeTimeType('MONTH')">
				最近30天
			</view>
			<view class="trend-selet-date" @click="open('CUSTOM')" :class="timeType==='CUSTOM'?'trend-selet-btn-click':''">
				<text class="iconfont">&#xe8cf;</text> <text>{{defDate[0]}}</text> — <text>{{defDate[1]}}</text>
			</view>
		</view>
		<view class="trend-echart">
			<view class="trend-echart-title">
				<text class="iconfont" style="margin-right: 10rpx;">&#xe636;</text>平均专注力
			</view>
			<TrendLineVue :amplitudeDate="list"></TrendLineVue>
			<view class="trend-echart-title">
				<text class="iconfont" style="margin-right: 10rpx;">&#xe636;</text>正确抑制率
			</view>
			<TrendLineInhibitionVue :amplitudeDate="list"></TrendLineInhibitionVue>
		</view>
		<uv-calendars ref="calendars" mode="range" @confirm="confirm" :date="defDate" />
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref
	} from 'vue';
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		getQryTrainTendencyChart
	} from '../../service';
	import TrendLineVue from '../../components/TrendLine.vue';
	import TrendLineInhibitionVue from '../../components/TrendLineInhibition.vue';
	const loginStore = useLoginStore()
	const calendars = ref(null)
	const timeType = ref('WEEK')
	const list = ref({})
	const defDate = ref(['2023-08-08', '2023-08-27'])
	const timeList = ref([])
	const confirm = (e) => {
		timeList.value = e.range.data
		getList()
	}
	onMounted(() => {
		getList()
	})
	const changeTimeType = (value) => {
		timeList.value = []
		timeType.value = value
		getList()
	}
	const getList = () => {
		const params = {
			timeType: timeType.value,
			standardList: ["avgFocus", "signalStopRspRate"],
			traineeId: loginStore.qryActiveTrainee
		}
		if (timeList.value.length > 0) {
			params.beginTime = new Date(timeList.value[0])
			params.endTime = new Date(timeList.value[timeList.value.length - 1])
		}
		getQryTrainTendencyChart(params).then(res => {
			list.value = res.data
			defDate.value = [res.data.time[0].replace(/\//g, '-'), res.data.time[res.data.time.length - 1].replace(/\//g, '-')]
		})
	}
	const open = (value) => {
		calendars.value.open()
		timeType.value = value
	}
</script>

<style lang="scss">
	.trend {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;

		&-echart {
			width: 100vw;
			height: 100%;
			background: #FFFFFF;
			padding: 24rpx;

			&-title {
				display: flex;
				align-items: center;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 500;
				font-size: 28rpx;
				color: #111111;
				margin-bottom: 16rpx;
			}
		}

		&-selet {
			display: flex;
			align-items: center;
			padding: 16rpx;

			&-btn {
				width: 150rpx;
				height: 64rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 400;
				font-size: 24rpx;
				color: #111111;
				margin-right: 16rpx;

				&-click {
					background: #287FFF !important;
					color: #FFFFFF !important;
				}
			}

			&-date {
				height: 64rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				padding: 20rpx;
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: 500;
				font-size: 24rpx;
				color: #111111;
			}
		}

	}
</style>