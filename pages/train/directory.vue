<template>
	<view class="directory">
		<image class="directory-img" src="../../static/train/directory-img1.png" mode="widthFix" @click="()=>goGame(1)"></image>
		<image class="directory-img" src="../../static/train/directory-img2.png" mode="widthFix" @click="()=>goGame(2)"></image>
		<doctorQrPopVue :open="state.open" @close="close"></doctorQrPopVue>
	</view>
</template>

<script setup>
	import {
		reactive
	} from "vue";
	import {
		navigateTo
	} from "../../common/uniTool";
	import doctorQrPopVue from '../../components/doctorQrPop.vue';
	const state = reactive({
		open: false
	})
	const close = (value) => {
		console.log(value);
		state.open = false
	}
	const goGame = (type) => {
		if (type === 1) {
			navigateTo('/pages/game/introduce')
		} else {
			state.open = true
		}
	}
</script>

<style lang="scss">
	.directory {
		height: 100vh;
		background: #F6F6F6;
		display: flex;
		flex-direction: column;
		align-items: center;

		&-doctorQr {
			width: 600rpx;
		}

		&-end {
			width: 650rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			padding: 64rpx 0 60rpx 0;

			&-two2 {
				display: flex;
				align-items: center;
				justify-content: space-around;
				width: 100%;

				&-top {
					width: 246rpx;
					height: 80rpx;
					border-radius: 16rpx;
					border: 2rpx solid #0485F4;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #0485F4;
				}

				&-bottom {
					width: 246rpx;
					height: 80rpx;
					background: #0485F4;
					border-radius: 16rpx;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #FFFFFF;
				}
			}

			&-two1 {
				display: flex;
				flex-direction: column;
				align-items: center;


				&-top {
					width: 516rpx;
					height: 80rpx;
					background: #0485F4;
					border-radius: 16rpx;
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #FFFFFF;
					margin-bottom: 16rpx;
				}

				&-bottom {
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;
					color: #0485F4;
					width: 516rpx;
					height: 80rpx;
					border-radius: 16rpx;
					border: 2rpx solid #0485F4;
				}
			}

			&-title {
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #111111;
				margin-bottom: 48rpx;


			}

			&-img {
				width: 100%;
			}

			&-text {
				padding: 0 40rpx;
				width: 100%;
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				line-height: 48rpx;
				margin-bottom: 60rpx;

				&-min {
					color: #FF4747;
				}
			}

			&-btn {
				width: 416rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #FFFFFF;

			}
		}

		&-img {
			width: 90%;
			margin-top: 32rpx;
		}
	}
</style>