<template>
	<view class="rank">
		<view class="rank-content">
			<view class="rank-content-title">
				<view v-for="(item,index) in state.titleList" :key="index" class="rank-content-title-item center">
					<view :class="item.name===state.titleClick?'rank-content-title-item-click center':''" @click="()=>changeTitle(item.name,'title')">
						{{item.name}}
					</view>
				</view>
			</view>
			<view class="rank-content-tip" v-if="state.titleClick!=='抑制数据榜'">
				<view :class="item.value===state.tipClick?'rank-content-tip-item-click center':''" v-for="(item,index) in state.classList[state.titleClick]" :key="index"
					class="rank-content-tip-item center" @click="()=>changeTitle(item.value,'tip')">
					{{item.name}}
				</view>
			</view>
			<view class="rank-content-box">
				<view class="rank-content-box-btn">
					<view class="rank-content-box-btn-today" :class="state.isToday==='TODAY'?'rank-content-box-btn-today-click':''" @click="()=>changeToday('TODAY')">
						今日排行榜
						<text class="rank-content-box-btn-today-tip">每日00:00清零</text>
					</view>
					<view class="rank-content-box-btn-today center" :class="state.isToday==='ALL'?'rank-content-box-btn-today-click':''" @click="()=>changeToday('ALL')">
						累计平均值榜
					</view>
				</view>
				<view class="rank-content-box-own" v-if="state.rankingData.rankingSelf">
					<view class="rank-content-box-own-text">
						当前本人所在名次
					</view>
					<view class="rank-content-box-own-value">
						<view class="rank-content-box-list-value-left center">
							<image class="rank-content-box-list-value-left-img" v-if="state.rankingData.rankingSelf.ranking==1" src="../../static/train/rank-first-icon.png" mode="widthFix"></image>
							<image class="rank-content-box-list-value-left-img" v-else-if="state.rankingData.rankingSelf.ranking==2" src="../../static/train/rank-second-icon.png" mode="widthFix">
							</image>
							<image class="rank-content-box-list-value-left-img" v-else-if="state.rankingData.rankingSelf.ranking==3" src="../../static/train/rank-three-icon.png" mode="widthFix">
							</image>
							<text v-else style="color: #999999;">{{state.rankingData.rankingSelf.ranking}}</text>
						</view>
						<view class="rank-content-box-own-value-left center">{{state.rankingData.rankingSelf.traineeName}}</view>
						<view class="rank-content-box-own-value-left center">{{state.rankingData.rankingSelf.age}}</view>
						<view class="rank-content-box-list-value-left center" style="width: 25%;">
							{{state.titleClick !== '跑动数据榜'?state.rankingData.rankingSelf.rankValue:state.rankingData.rankingSelf.rankValue>=1000?state.rankingData.rankingSelf.rankValue/1000:state.rankingData.rankingSelf.rankValue}}{{unit(state.rankingData.rankingSelf.rankValue)}}
							<view class="rank-content-box-list-value-left-other" v-if="state.rankingData.rankingSelf.countSum">
								{{state.rankingData.rankingSelf.countSum}} {{getTitle()==='专注力数值'?'次':'秒'}}
							</view>
						</view>
						<view class="rank-content-box-own-value-left center rank-content-box-list-value-icon iconfont" style="width: 15%;"
							:class="state.likeIndex.includes('self')?'rank-content-box-list-value-icon-like':''" @click="()=>like('self',state.rankingData.rankingSelf.traineeId)">&#xe8c3; <text
								class="rank-content-box-list-value-icon-num">{{state.rankingData.rankingSelf.thumbsupCount}}</text> </view>
					</view>
				</view>
				<view class="rank-content-box-list">
					<view class="rank-content-box-list-tip">
						<view class="rank-content-box-list-tip-item center">前10排名</view>
						<view class="rank-content-box-list-tip-item center">名字</view>
						<view class="rank-content-box-list-tip-item center">年龄</view>
						<view class="rank-content-box-list-tip-item center">{{getTitle()}}</view>
						<view class="rank-content-box-list-tip-item center">点赞</view>
					</view>
					<view v-for="(item,index) in state.rankingData.rankingList" :key="item.traineeId" class="rank-content-box-list-value">
						<view class="rank-content-box-list-value-left center">
							<image class="rank-content-box-list-value-left-img" v-if="item.ranking==1" src="../../static/train/rank-first-icon.png" mode="widthFix"></image>
							<image class="rank-content-box-list-value-left-img" v-else-if="item.ranking==2" src="../../static/train/rank-second-icon.png" mode="widthFix"></image>
							<image class="rank-content-box-list-value-left-img" v-else-if="item.ranking==3" src="../../static/train/rank-three-icon.png" mode="widthFix"></image>
							<text v-else style="color: #999999;">{{item.ranking}}</text>
						</view>
						<view class="rank-content-box-list-value-left center">{{item.traineeName}}</view>
						<view class="rank-content-box-list-value-left center">{{item.age}}</view>
						<view class="rank-content-box-list-value-left center">
							{{state.titleClick !== '跑动数据榜'?item.rankValue:item.rankValue>=1000?item.rankValue/1000:item.rankValue}}{{unit(item.rankValue)}}
							<view class="rank-content-box-list-value-left-other" v-if="item.countSum">
								{{item.countSum}}{{getTitle()==='专注力数值'?'次':'秒'}}
							</view>
						</view>
						<view class="rank-content-box-list-value-left center rank-content-box-list-value-icon iconfont"
							:class="state.likeIndex.includes(index)?'rank-content-box-list-value-icon-like':''" @click="()=>like(index,item.traineeId)">&#xe8c3; <text
								class="rank-content-box-list-value-icon-num">{{item.thumbsupCount}}</text></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive
	} from "vue";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		showToast
	} from "../../common/uniTool";
	import {
		getFocusRanking,
		getStopRanking,
		getNormalRanking,
		getRunDisRanking,
		addThumbsUp
	} from "../../service/rank";
	import {
		useLoginStore
	} from "../../stores/login";
	const loginStore = useLoginStore(); //用户基本信息
	onShow(() => {
		// #ifdef APP-PLUS
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
		// #endif
	})
	onMounted(() => {
		getData()
	})
	const getTitle = () => {
		let text = '平均专注力'

		if (state.tipClick === 'AVG') {
			text = '平均专注力'
		} else if (state.tipClick === 'MAX') {
			text = '专注力数值'
		} else if (state.tipClick === 'SUSTAIN') {
			text = '最高持续时长'
		} else if (state.tipClick === 'RATE') {
			text = '正确反应率'
		} else if (state.tipClick === 'TIME') {
			text = '正确反应时'
		} else if (state.tipClick === 'SING') {
			text = '单次跑动距离'
		} else {
			text = '累计跑动距离'
		}
		if (state.titleClick === '抑制数据榜') {
			text = '正确抑制率'
		}
		return text
	}
	const unit = (item) => {
		let text
		if (state.titleClick === '专注数据榜') {
			text = ''
		} else if (state.titleClick === '抑制数据榜') {
			text = '%'
		} else if (state.titleClick === '反应数据榜') {
			text = '%'
		} else {
			if (item >= 1000) {
				text = '千米'
			} else {
				text = '米'
			}
		}
		return text
	}
	const getData = () => {
		if (state.titleClick === '专注数据榜') {
			getFocusRanking({
				traineeId: loginStore.qryActiveTrainee,
				rankingType: state.tipClick,
				timeRange: state.isToday
			}).then(res => {
				state.rankingData = res.data
			})
		} else if (state.titleClick === '抑制数据榜') {
			getStopRanking({
				traineeId: loginStore.qryActiveTrainee,
				timeRange: state.isToday
			}).then(res => {
				state.rankingData = res.data
			})
		} else if (state.titleClick === '反应数据榜') {
			getNormalRanking({
				traineeId: loginStore.qryActiveTrainee,
				rankingType: state.tipClick,
				timeRange: state.isToday
			}).then(res => {
				state.rankingData = res.data
			})
		} else {
			getRunDisRanking({
				traineeId: loginStore.qryActiveTrainee,
				rankingType: state.tipClick,
				timeRange: state.isToday
			}).then(res => {
				state.rankingData = res.data
			})
		}

	}
	const state = reactive({
		isLike: true,
		rankingData: [],
		titleClick: '专注数据榜',
		tipClick: 'AVG',
		isToday: 'TODAY',
		likeIndex: [],
		titleList: [{
				name: '专注数据榜',
			},
			{
				name: '抑制数据榜',
			},
			{
				name: '反应数据榜',
			},
			{
				name: '跑动数据榜',
			},
		],
		classList: {
			"专注数据榜": [{
				name: '平均专注力',
				value: 'AVG',
			}, {
				name: '最高专注力',
				value: 'MAX'
			}, {
				name: '最高专注力持续时长',
				value: 'SUSTAIN'
			}],
			"反应数据榜": [{
				name: '正确反应率',
				value: 'RATE',
			}, {
				name: '正确反应时',
				value: 'TIME'
			}],
			"跑动数据榜": [{
				name: '单次回合最长跑动距离',
				value: 'SING',
			}, {
				name: '训练课程累计跑动距离',
				value: 'MULT'
			}]
		},
	})
	const changeTitle = (click, type) => {
		if (type === 'title') {
			state.titleClick = click
			state.isToday = 'TODAY'
			if (state.titleClick === '反应数据榜') {
				state.tipClick = 'RATE'
			}
			if (state.titleClick === '跑动数据榜') {
				state.tipClick = 'SING'
			}
			if (state.titleClick === '专注数据榜') {
				state.tipClick = 'AVG'
			}
		} else {
			state.tipClick = click
		}
		getData()
	}
	const changeToday = (value) => {
		state.isToday = value
		getData()
	}
	const like = (value, traineeId) => {
		if (!state.isLike) {
			return
		}
		state.isLike = false
		addThumbsUp({
			thumbsupId: loginStore.qryActiveTrainee,
			traineeId
		}).then(res => {
			showToast('点赞成功')
			state.isLike = true
			if (state.likeIndex.indexOf(value) == -1) {
				state.likeIndex.push(value)
			}
			if (state.likeIndex === 'self') {
				state.rankingData.rankingSelf.thumbsupCount++
			} else {
				state.rankingData.rankingList[value].thumbsupCount++
			}
		}).catch(err => {
			state.isLike = true
			showToast(err.desc)
		})
	}
</script>

<style lang="scss">
	.rank {
		width: 100vw;
		flex: 1;
		background: url('~@/static/train/rank-banner.png') #F6F6F6;
		background-repeat: no-repeat;
		background-size: 100vw auto;
		position: relative;
		padding: 252rpx 32rpx 32rpx 32rpx;

		&-content {
			background: linear-gradient(180deg, #FFF4CD 0%, #FFFFFF 6%);
			border-radius: 24rpx;
			border-top: 2rpx solid #FFFFFF;

			&-box {
				padding: 24rpx;
				border-radius: 24rpx;
				margin-top: 28rpx;
				background: linear-gradient(180deg, #FFF2DB 0%, #FFFFFF 10%);

				&-list {
					display: flex;
					width: 100%;
					margin-top: 24rpx;
					flex-direction: column;

					&-value {
						width: 100%;
						height: 90rpx;
						display: flex;
						align-items: center;
						justify-content: space-around;
						font-size: 24rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;
						border-bottom: 2rpx solid #E0E0E0;

						&-icon {
							color: #E0E0E0;
							position: relative;
							display: flex;
							flex-direction: column;

							&-num {
								font-size: 16rpx;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: #111111;
								margin-top: 2rpx;
								// animation: showhide 3s ease forwards;
							}

							&-like {
								color: #FF8520;
								position: relative;


							}
						}

						&-left {
							width: 20%;
							display: flex;
							flex-direction: column;

							&-other {
								font-size: 16rpx;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: #111111;
							}

							&-img {
								width: 44rpx;
								height: 48rpx;
							}

						}

						&-left:nth-child(4) {
							width: 25%;
						}

						&-left:nth-child(5) {
							width: 15%;
						}
					}

					&-value:last-child {
						border: none;
					}


					&-tip {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 16rpx;

						&-item {
							width: 20%;
							font-size: 24rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #666666;
						}

						&-item:nth-child(4) {
							width: 25%;
						}

						&-item:nth-child(5) {
							width: 15%;
						}
					}
				}

				&-own {
					display: flex;
					flex-direction: column;
					margin-top: 28rpx;

					&-value {
						margin-top: 16rpx;
						width: 100%;
						height: 90rpx;
						background: #FEF8E3;
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: space-around;
						font-size: 24rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: bold;
						color: #111111;

						&-left {
							width: 20%;

							&-img {
								width: 44rpx;
								height: 48rpx;
							}

						}
					}

					&-text {
						font-size: 24rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #111111;
						position: relative;
						display: flex;
						align-items: center;
						padding-left: 16rpx;
					}

					&-text::before {
						position: absolute;
						top: 50%;
						transform: translateY(-50%);
						left: 0;
						content: ' ';
						width: 8rpx;
						height: 24rpx;
						background: #FF8520;
						border-radius: 16rpx;
					}
				}

				&-btn {
					display: flex;
					align-items: center;
					width: 368rpx;
					height: 64rpx;
					background: #EEEADD;
					border-radius: 40rpx;
					align-items: center;
					padding: 4rpx;

					&-today {
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: center;
						font-size: 20rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #666666;
						flex: 1;

						&-click {
							background: #FFFFFF;
							border-radius: 40rpx;
							font-size: 20rpx;
							font-family: SourceHanSansCN-Bold, SourceHanSansCN;
							font-weight: bold;
							color: #FF8520;
							height: 100%;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
						}

						&-tip {
							font-size: 14rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #111111;
						}
					}

				}
			}

			&-tip {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 32rpx;
				padding: 0 24rpx;

				&-item {
					height: 60rpx;
					background: #F2F2F2;
					flex: 1;
					font-size: 20rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #666666;
					position: relative;

					&-click {
						font-size: 20rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #FFFFFF;
						background: #FF8520;
					}

					&-click::after {
						content: ' ';
						border: 16rpx solid transparent;
						position: absolute;
						bottom: -28rpx;
						left: 50%;
						transform: translateX(-50%);
						border-bottom-color: #FFF4CD;
					}
				}

				&-item:first-child {
					border-right: 4px solid #fff;
					border-radius: 16rpx 0rpx 0rpx 16rpx;
				}

				&-item:last-child {
					border-left: 4px solid #fff;
					border-radius: 0rpx 16rpx 16rpx 0rpx;
				}
			}

			&-title {
				display: flex;
				justify-content: space-between;

				&-item {
					width: 25%;
					font-size: 24rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #666666;
					height: 64rpx;
					position: relative;

					&-click {
						font-size: 28rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #111111;
						top: 50%;
						transform: translateY(-50%);
						left: -2rpx;
						position: absolute;
						width: 102%;
						height: 104rpx;
						background: #FFFFFF;
						border-radius: 24rpx;
						border: 2rpx solid #FFFFFF;
					}

					&-click::after {
						content: ' ';
						width: 48rpx;
						height: 6rpx;
						background: #FF8520;
						border-radius: 40rpx;
						position: absolute;
						bottom: 10rpx;
						left: 50%;
						transform: translateX(-50%);
					}
				}
			}
		}

	}

	@keyframes showhide {
		20% {
			opacity: 0;
		}

		50% {
			opacity: 1;
		}

		100% {
			opacity: 0;
		}
	}
</style>