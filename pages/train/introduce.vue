<template>
	<!-- 	<web-view
		src="http://192.168.1.116:8005/#/calendar/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXNzV29yZCI6IjYyZjYxMDRjODJjMGQyM2RjODU4NzQwYiIsImV4cCI6MTY5NTAzMzE4MiwiaWF0IjoxNjc5NDgxMTgyLCJ1c2VySWQiOiI2MmY2MTA0YzgyYzBkMjNkYzg1ODc0MGIifQ.0At8PKGb-GRfYvND6eoid4BhGPNT7-8EpG3ldAFwqAA"
		class="web" @message="handleMessage"></web-view> -->
	<view class="game">
		<!-- 		<view class="game-box">
			<image class="game-box-img" src="../../static/train/banner.jpg" mode="widthFix" />
			<view class="game-box-title">{{state.project[0].name}}</view>
			<view class="game-box-text">原理和效果介绍</view>
			<view class="game-box-text1">{{state.project[0].text}}</view>
			<view class="game-box-text">故事梗概玩法介绍</view>
			<view class="game-box-text1">{{state.project[0].introduce}}</view>
			<view class="game-box-button center" @click="junpApp">
				打开训练APP
			</view>
		</view> -->
		<image class="game-bg" src="../../static/train/train-bg.png" mode="widthFix"></image>
		<view class="game-btn center" @click="goGame">
			打开数字训练
		</view>
		<doctorQrPopVue :open="state.open" @close="close"></doctorQrPopVue>
	</view>
</template>

<script setup>
	import doctorQrPopVue from '../../components/doctorQrPop.vue';
	import {
		reactive
	} from "vue";
	const state = reactive({
		open: false
	})
	const close = (value) => {
		console.log(value);
		state.open = false
	}
	const goGame = () => {
		console.log(1);
		state.open = true
	}
</script>

<style lang="scss">
	.web {
		flex: 1
	}



	.game {
		width: 100vw;
		height: 100%;
		background: #FFFFFF;
		padding: 12rrpx;
		position: relative;

		&-bg {
			width: 100%;
		}


		&-btn {
			width: 682rpx;
			height: 81rpx;
			background: linear-gradient(360deg, #217CFF 0%, #5EBAFF 100%);
			border-radius: 14rpx;
			font-size: 25rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			position: absolute;
			bottom: 20rpx;
			left: 50%;
			transform: translateX(-50%);
		}

		&-box {
			width: 100%;
			height: 100%;
			background: #ffffff;
			border-radius: 32rpx 32rpx 16rpx 16rpx;
			padding: 32rpx 32rpx 0 32rpx;
			position: relative;

			&-img {
				width: 100%;
				border-radius: 16rpx;
				margin-bottom: 35rpx;
			}

			&-video {
				width: 686rpx;
				height: 430rpx;
				margin-bottom: 37rpx;
			}

			&-title {
				font-size: 30rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #000000;
				margin-bottom: 21rpx;
			}

			&-text {
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #111111;
				margin-bottom: 21rpx;
			}

			&-text1 {
				font-size: 18rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #666666;
				margin-bottom: 36rpx;
			}

			&-button {
				width: 686rpx;
				height: 96rpx;
				background: linear-gradient(360deg, #217CFF 0%, #5EBAFF 100%);
				border-radius: 16rpx;
				font-size: 22rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				position: absolute;
				bottom: 90rpx;
				left: 50%;
				transform: translate(-50%);
			}
		}
	}
</style>