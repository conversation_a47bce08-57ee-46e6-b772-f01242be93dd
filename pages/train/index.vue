<template>
	<view class="game">
		<view class="game-top-control">
			<view class="game-top-control-box" @click="previous">
				<view class="iconfont">&#xe604;</view>
			</view>
			<view class="game-top-control-content">
				<view>{{state.textWeek}}已完成{{state.finishArr&&state.finishArr.trainDay||0}}/{{state.finishArr&&state.finishArr.planDay||0}}天</view>
				<view class="game-top-control-content-date">{{state.weekNum[0].data.split('-')[1]}}月{{state.weekNum[0].data.split('-')[2]}}日 -
					{{state.weekNum[state.weekNum.length - 1].data.split('-')[1]}}月{{state.weekNum[state.weekNum.length - 1].data.split('-')[2]}}日
				</view>
			</view>
			<view class="game-top-control-box" @click="next">
				<view class="iconfont">&#xe60d;</view>
			</view>
		</view>
		<view class="">
			<view class="game-top-process">
				<view :class="state.clickIndex==index&&'game-top-process-box'" v-for="(item,index) in state.weekNum" :key="item" @click="()=>click(index)">
					<view class="game-top-process-can">
						<canvas :style="`width:${state.small.width}rpx; height: ${state.small.height}rpx;`" :canvas-id="item.index" :id="item.index"></canvas>
					</view>
					<view class="game-top-process-date">
						<view class="game-top-process-date-item center" :class="state.clickIndex==index&&'game-top-process-date-item-click'">
							{{['周一', '周二', '周三', '周四', '周五', '周六', '周日'][index]}}
						</view>
					</view>
				</view>

			</view>
			<view class="game-top-text center" @click="goRankList">
				<view class="game-top-text-title">
					专注、抑制、反应、跑动
				</view>
				<view class="game-top-text-btn center">
					前往查看排行榜详情<text class="game-top-text-btn-icon iconfont">&#xe623;</text>
				</view>
			</view>
			<view class="game-center">
				<view class="game-center-date">
					<view class="game-center-date-update">
						更新时间：
						<view :class="state.trainEffDetail.time===null?'game-center-content-top-left-box-value-no':''">
							{{state.trainEffDetail.time===null?'暂无':state.trainEffDetail.time}}
						</view>
					</view>
					<view class="game-center-date-trend center" @click="goTrend">
						<text class="iconfont">&#xe68a;</text> 训练效果走势
					</view>
				</view>
				<view class="game-center-content">
					<view class="game-center-content-top">
						<view class="game-center-content-top-left">
							<view class="game-center-content-top-left-box">
								<view class="game-center-content-top-left-box-title">
									训练 <text class="game-center-content-top-left-box-title-other">(分钟)</text>
								</view>
								<view class="game-center-content-top-left-box-value" :class="state.trainEffDetail.effTrainTime===null?'game-center-content-top-left-box-value-no':''">
									{{state.trainEffDetail.effTrainTime===null?'暂无':state.trainEffDetail.effTrainTime}} <text class="game-center-content-top-left-box-value-other">/
										{{state.trainEffDetail.planTrainTime===null?'暂无':state.trainEffDetail.planTrainTime}} </text>
								</view>
							</view>
							<view class="game-center-content-top-left-box">
								<view class="game-center-content-top-left-box-title game-center-content-top-left-box-two">
									训练效率<text class="game-center-content-top-left-box-title-other"> (%) </text>
								</view>
								<view class="game-center-content-top-left-box-value" style="display: flex;align-items: center;"
									:class="state.trainEffDetail.totalTrainEffi===null?'game-center-content-top-left-box-value-no':''">
									{{state.trainEffDetail.totalTrainEffi===null?'暂未完成':state.trainEffDetail.totalTrainEffi}}
									<text class="game-center-content-top-left-box-value-tip center" v-show="state.trainEffDetail.totalTrainEffi">
										{{getTotalText(state.trainEffDetail.totalTrainEffi)}}</text>
								</view>
							</view>
						</view>
						<view class="game-center-content-top-right">
							<canvas id="process" canvas-id="process" :style="`width:${state.big.width}rpx; height: ${state.big.height}rpx;`"> </canvas>
						</view>
					</view>
				</view>
				<view class="game-center-bottom">
					<view class="game-center-bottom-sum">
						<view class="game-center-bottom-sum-left">
							总计
						</view>
						<view class="game-center-bottom-sum-center">
							<view class="game-center-bottom-sum-center-item">
								<view class="game-center-bottom-sum-center-item-text">
									反应成功数
								</view>
								<view class="game-center-bottom-sum-center-item-value" :class="state.trainEffDetail.signalNormalSuccCount===null?'game-center-bottom-sum-center-item-value-novalue':''">
									{{state.trainEffDetail.signalNormalSuccCount===null?'暂无':state.trainEffDetail.signalNormalSuccCount}}
								</view>
							</view>
							<view class="game-center-bottom-sum-center-item">
								<view class="game-center-bottom-sum-center-item-text">
									反应失败数
								</view>
								<view class="game-center-bottom-sum-center-item-value" :class="state.trainEffDetail.signalNormalFailCount===null?'game-center-bottom-sum-center-item-value-novalue':''">
									{{state.trainEffDetail.signalNormalFailCount===null?'暂无':state.trainEffDetail.signalNormalFailCount}}
								</view>
							</view>
						</view>
						<view class="game-center-bottom-sum-right">
							<view class="game-center-bottom-sum-right-item">
								<view class="game-center-bottom-sum-right-item-text">
									抑制正确数
								</view>
								<view class="game-center-bottom-sum-right-item-value" :class="state.trainEffDetail.signalStopSuccCount===null?'game-center-bottom-sum-right-item-value-novalue':''">
									{{state.trainEffDetail.signalStopSuccCount===null?'暂无':state.trainEffDetail.signalStopSuccCount}}
								</view>
							</view>
							<view class="game-center-bottom-sum-right-item">
								<view class="game-center-bottom-sum-right-item-text">
									抑制错误数
								</view>
								<view class="game-center-bottom-sum-right-item-value" :class="state.trainEffDetail.signalStopFailCount===null?'game-center-bottom-sum-right-item-value-novalue':''">
									{{state.trainEffDetail.signalStopFailCount===null?'暂无':state.trainEffDetail.signalStopFailCount}}
								</view>
							</view>
						</view>
					</view>
					<view class="game-center-bottom-avg">
						<view class="game-center-bottom-avg-left">
							平均
						</view>
						<view class="game-center-bottom-avg-center">
							<view class="game-center-bottom-avg-center-text">
								反应时
							</view>
							<view class="game-center-bottom-avg-center-value" :class="state.trainEffDetail.rightRspTime===null?'game-center-bottom-avg-center-value-novalue':''">
								{{state.trainEffDetail.rightRspTime===null?'暂无':state.trainEffDetail.rightRspTime+'ms'}}
							</view>
						</view>
						<view class="game-center-bottom-avg-center">
							<view class="game-center-bottom-avg-center-text">
								正确抑制率
							</view>
							<view class="game-center-bottom-avg-center-value" :class="state.trainEffDetail.signalStopRspRate===null?'game-center-bottom-avg-center-value-novalue':''">
								{{state.trainEffDetail.signalStopRspRate===null?'暂无':state.trainEffDetail.signalStopRspRate+'%'}}
							</view>
						</view>
					</view>
					<view class="game-center-bottom-top">
						<view class="game-center-bottom-top-left">
							<view class="game-center-bottom-top-left-item">
								<view class="game-center-bottom-top-left-item-text">
									专注力
								</view>
								<view class="game-center-bottom-top-left-item-value game-center-bottom-top-left-item-value-other"
									:class="state.trainEffDetail.maxFocus===null?'game-center-bottom-top-left-item-value-other-novalue':''">
									{{state.trainEffDetail.maxFocus===null?'暂无':'最高 '+state.trainEffDetail.maxFocus}}
								</view>
								<view class="game-center-bottom-top-left-item-value game-center-bottom-top-left-item-value-other"
									:class="state.trainEffDetail.avgFocus===null?'game-center-bottom-top-left-item-value-other-novalue':''">
									{{state.trainEffDetail.avgFocus===null?'暂无':'平均 '+state.trainEffDetail.avgFocus}}
								</view>
								<view class="game-center-bottom-top-left-item-value game-center-bottom-top-left-item-value-other"
									:class="state.trainEffDetail.effiFocusTime===null?'game-center-bottom-top-left-item-value-other-novalue':''">
									{{state.trainEffDetail.effiFocusTime===null?'暂无':'有效时长 '+state.trainEffDetail.effiFocusTime}}
								</view>
							</view>
						</view>
					</view>
					<view class="game-center-bottom-emotion">
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left game-center-bottom-emotion-top-bg game-center-bottom-emotion-top-br1 center">
								情绪指数
							</view>
							<view class="game-center-bottom-emotion-top-item game-center-bottom-emotion-top-bg center">
								总时长
							</view>
							<view class="game-center-bottom-emotion-top-item game-center-bottom-emotion-top-bg center">
								单次最久时间段
							</view>
						</view>
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left  game-center-bottom-emotion-top-bg center">
								左脑-积极
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								<text v-if="state.trainEffDetail.leftEmotionDetail">{{state.trainEffDetail.leftEmotionDetail.totalTime}}
								</text>
								<text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								<text v-if="state.trainEffDetail.leftEmotionDetail">
									{{state.trainEffDetail.leftEmotionDetail.beginTime+'至'+state.trainEffDetail.leftEmotionDetail.endTime}}
								</text>
								<text v-else>暂无</text>
							</view>
						</view>
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left game-center-bottom-emotion-top-bg game-center-bottom-emotion-top-br2 center">
								右脑-消极
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								<text v-if="state.trainEffDetail.rightEmotionDetail">{{state.trainEffDetail.rightEmotionDetail.totalTime}}
								</text>
								<text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								<text v-if="state.trainEffDetail.rightEmotionDetail">
									{{state.trainEffDetail.rightEmotionDetail.beginTime+'至'+state.trainEffDetail.rightEmotionDetail.endTime}}
								</text>
								<text v-else>暂无</text>
							</view>
						</view>
					</view>
					<view class="game-center-bottom-emotion" style="margin-top: 20rpx;">
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left game-center-bottom-emotion-top-bg game-center-bottom-emotion-top-br1 center"
								style="width:260rpx ;justify-content: flex-start;padding-left: 12rpx;">
								脑力值(总计奖励：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.questTotalpoints+'个'}}</text> <text v-else>暂无</text>)
							</view>
							<view class="game-center-bottom-emotion-top-item game-center-bottom-emotion-top-bg center" style="background-color: #fff;justify-content: flex-start;padding-left: 12rpx;">
								时间：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.consumTime}}</text> <text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item game-center-bottom-emotion-top-bg center" style="background-color: #fff;">
								平均专注力：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.avgFocus}}</text> <text v-else>暂无</text>
							</view>
						</view>
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left  game-center-bottom-emotion-top-bg center" style="width:260rpx ;justify-content: flex-start;padding-left: 12rpx;">
								今日奖励：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.points}}个</text> <text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center" style="justify-content: flex-start;padding-left: 12rpx;">
								正确：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.rightCount}}题
								</text>
								<text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								最高专注力：<text v-if="state.trainEffDetail.queTrainDetail">
									{{state.trainEffDetail.queTrainDetail.maxFocus}}
								</text>
								<text v-else>暂无</text>
							</view>
						</view>
						<view class="game-center-bottom-emotion-top">
							<view class="game-center-bottom-emotion-top-left game-center-bottom-emotion-top-bg game-center-bottom-emotion-top-br2 center"
								style="width:260rpx ;justify-content: flex-start;padding-left: 12rpx;">
								今日错失：<text v-if="state.trainEffDetail.queTrainDetail">
									{{state.trainEffDetail.queTrainDetail.undoneCount+state.trainEffDetail.queTrainDetail.failCount}}个
								</text>
								<text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center" style="justify-content: flex-start;padding-left: 12rpx;">
								错误或遗漏：<text v-if="state.trainEffDetail.queTrainDetail">{{state.trainEffDetail.queTrainDetail.undoneCount+state.trainEffDetail.queTrainDetail.failCount}}
								</text>
								<text v-else>暂无</text>
							</view>
							<view class="game-center-bottom-emotion-top-item center">
								最低专注力：<text v-if="state.trainEffDetail.queTrainDetail">
									{{state.trainEffDetail.queTrainDetail.minFocus}}
								</text>
								<text v-else>暂无</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="game-train" v-for="(item,index) in state.trainList" :key="item.title" @click="()=>go(index)">
				<view class="game-train-left">
					<view class="game-train-left-title">
						<text class="game-train-left-title-icon iconfont">{{item.icon}}</text>{{item.title}}
					</view>
					<text class="game-train-left-text">
						{{item.text}}
					</text>
				</view>
				<image class="game-train-img" :src="item.img" mode="widthFix"></image>
			</view>
			<!-- <image :src="loginStore.prescriptionList.length===0?'../../static/train/no-doctor.png':'../../static/train/tarin-go.png'" mode="widthFix" @click="go"></image> -->
			<uni-popup ref="popup" type="center" :animation="false">
				<image src="../../static/doctor-qr.png" mode="widthFix" class="game-doctorQr"></image>
			</uni-popup>
		</view>
		<!-- <image src="../../static/no-pre.png" mode="widthFix" class="game-noGame" v-show="!state.isPre" @click="go"></image> -->
	</view>
</template>

<script setup>
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		onMounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		timestampToTime
	} from '../../common/method';
	import {
		navigateTo,
		showToast
	} from "../../common/uniTool";
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		getQryPresTrainEffList,
		getQryPresTrainEffDetail,
		checkExistCarrier
	} from "../../service";
	import {
		debounce
	} from 'lodash';
	const loginStore = useLoginStore()
	const currentFirstDateRef = ref(null)
	const popup = ref(null) ///显示弹窗
	const state = reactive({
		isPre: false,
		small: {
			width: 80,
			height: 60,
			radius1: 30,
			radius2: 16,
			lineWidth: 10
		},
		big: {
			width: 340,
			height: 180,
			radius1: 132,
			radius2: 80,
			lineWidth: 32
		},
		textWeek: '该周',
		finishArr: [],
		clickIndex: new Date().getDay() == 0 ? 6 : new Date().getDay() - 1,
		weekNum: [{
				data: '',
				index: 'can0',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can1',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can2',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can3',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can4',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can5',
				trainRate: '',
				finishRate: ''
			},
			{
				data: '',
				index: 'can6',
				trainRate: '',
				finishRate: ''
			}
		],
		trainEffDetail: {
			avgFocus: null,
			day: null,
			effTrainTime: null,
			effiFocusTime: null,
			finishRate: null,
			isHavePres: 0,
			maxFocus: null,
			planTrainTime: null,
			prescriptionId: null,
			signalNormalFailCount: null,
			rightRspTime: null,
			signalNormalSuccCount: null,
			signalStopFailCount: null,
			signalStopRspRate: null,
			signalStopSuccCount: null,
			time: null,
			totalDay: null,
			totalTrainTime: null,
			trainDayEvrTime: null,
			trainRate: null,
			traineeId: null,
		},
		trainList: [{
				img: '../../static/train/train-1.png',
				title: '脑机接口训练',
				text: '注意力、自控力、记忆力\n专注力实时驱动',
				icon: '\ue608'
			}, {
				img: '../../static/train/train-3.png',
				title: '正念训练',
				text: '情绪改善、放松感知',
				icon: '\ue60f'
			}, {
				img: '../../static/train/train-4.png',
				title: '脑电生物反馈训练',
				text: '保持专注、集中注意力',
				icon: '\ue636'
			}, {
				img: '../../static/train/train-2.png',
				title: '注意力探险',
				text: '个性化训练方案\n多个场景模式',
				icon: '\ue739'
			}
			// , {
			// 	img: '../../static/train/train-5.png',
			// 	title: '数字划消',
			// 	text: '视觉注意力，视野广度，计算能力',
			// 	icon: '\ue641'
			// },
		],
	})
	onMounted(() => {
		getDate(new Date());
		getReportData();
		console.log(uni.getSystemInfoSync());
		if (uni.getSystemInfoSync().platform === 'ios') {
			state.trainList.pop()
		}
	})

	watch(() => state.weekNum, (weekNum) => {
		weekNum.forEach(item => {
			toSmallCanvas(item.index, item.finishRate, item.trainRate, state.small);
		})
	}, {
		deep: true
	})
	watch([() => state.clickIndex, () => state.finishArr], ([clickIndex, finishArr]) => {
		getReportDetail(new Date(Date.parse(finishArr.list[clickIndex]['time'])), new Date(Date.parse(finishArr.list[clickIndex]['time'])))
	}, {
		deep: true
	})
	onShow(() => {
		// #ifdef APP-PLUS
		plus.navigator.setFullscreen(false);
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
		// #endif
		getDate(new Date());
		getReportData();
	})
	const getReportDetail = (beginDate, endDate) => {
		getQryPresTrainEffDetail({
			beginDate,
			endDate,
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.trainEffDetail = res.data
			toSmallCanvas('process', res.data.finishRate, res.data.trainRate, state.big);
		})
	}
	const goRankList = () => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		navigateTo('/pages/train/rankingList')
	}

	function isCurrentWeek(past) {
		const pastTime = new Date(past).getTime()
		const today = new Date(new Date().toLocaleDateString())
		let day = today.getDay()
		day = day == 0 ? 7 : day
		const oneDayTime = 60 * 60 * 24 * 1000
		const monday = new Date(today.getTime() - (oneDayTime * (day - 1)))
		const nextMonday = new Date(today.getTime() + (oneDayTime * (8 - day)))
		if (monday.getTime() <= pastTime && nextMonday.getTime() > pastTime) {
			return true
		} else {
			return false
		}
	}


	const getReportData = () => {
		getQryPresTrainEffList({
			beginDate: new Date(Date.parse(state.weekNum[0].data.slice(0, -2))),
			endDate: new Date(Date.parse(state.weekNum[state.weekNum.length - 1].data.slice(0, -2))),
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.isPre = true
			if (new Date().getTime() >= new Date(res.data.list[0].time).getTime() && new Date().getTime() <= new Date(res.data.list[res.data.list.length - 1].time).getTime()) {
				state.textWeek = '本周'
				state.clickIndex = new Date().getDay() == 0 ? 6 : new Date().getDay() - 1
			} else {
				state.textWeek = '该周'
			}
			state.finishArr = res.data
			res.data.list.forEach((item, index) => {
				state.weekNum[index]['finishRate'] = item.finishRate
				state.weekNum[index]['trainRate'] = item.trainRate
			})
		}).catch(res => {
			state.textWeek = '该周'
			state.isPre = false
		})

	}
	const goTrend = () => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		navigateTo('/pages/train/trend')
	}
	const go = debounce((index) => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		console.log(index);
		// if (loginStore.prescriptionList.length === 0) {
		// 	popup.value.open('center')
		// 	return
		// }
		if (!loginStore.qryActiveTrainee) {
			showToast('请添加亲属')
			return
		}
		if (!index) {
			checkExistCarrier({
				traineeId: loginStore.qryActiveTrainee
			}).then(res => {
				if (res.data.checkResult) {
					navigateTo('/pages/game/growthPath')
				} else {
					navigateTo('/pages/shopping/index')
				}
			})
		} else if (index == 1) {
			navigateTo('/pages/relax/introduce?type=1')
		} else if (index == 2) {
			navigateTo('/pages/relax/introduce?type=2')
		} else if (index == 3) {
			navigateTo('/pages/train/introduce')
		} else {
			showToast('敬请期待~')
		}
	}, 300)
	const click = (index) => {
		state.clickIndex = index
		getReportDetail(new Date(Date.parse(state.weekNum[index].data.slice(0, -2))), new Date(Date.parse(state.weekNum[index].data.slice(0, -2))))
	}
	const previous = () => {
		state.clickIndex = 0
		getDate(addDate(currentFirstDateRef.value, -7));
		getReportData()
	}
	const next = () => {
		state.clickIndex = 0
		getDate(addDate(currentFirstDateRef.value, 7));
		getReportData()
	}
	const getDate = (date) => {
		const weeks = [...state.weekNum]
		let week = date.getDay() - 1;
		if (week == -1) {
			week = 6
		}
		date = addDate(date, week * -1);
		currentFirstDateRef.value = new Date(date);
		for (let i = 0; i < state.weekNum.length; i++) {
			weeks[i].data = formatDate(i == 0 ? date : addDate(date, 1));
		}
		state.weekNum = weeks
	}
	const addDate = function (date, n) {
		date.setDate(date.getDate() + n);
		return date;
	};
	const formatDate = function (date) {
		const year = date.getFullYear();
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		const day = date.getDate().toString().padStart(2, '0');
		// const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()];
		const week = date.getDay();

		return `${year}-${month}-${day}-${week}`;
	}
	const getTotalText = (value) => {
		let text
		switch (true) {
			case value > 50 && value < 65:
				text = '再接再厉'
				break;
			case value >= 65 && value < 80:
				text = '干得漂亮'
				break;
			case value >= 80:
				text = '效率之王'
				break;
			default:
				text = '请提高效率'
				break;
		}
		return text
	}

	function toSmallCanvas(id, progress, progress2, configuration) {
		//canvas进度条
		const ctx = uni.createCanvasContext(id),
			percent = progress || 1, //进度
			percent2 = progress2 || 1, //进度
			//最终百分比
			circleX = configuration.width / 2, // 中心x坐标
			circleY = configuration.width / 2, //中心y坐标
			radius = configuration.radius1, //圆环半径
			radius1 = configuration.radius2,
			lineWidth = configuration.lineWidth // 圆形线条的宽度
		//画圆重合
		function circle(cx, cy, r) {
			ctx.beginPath();
			ctx.lineWidth = lineWidth;
			ctx.strokeStyle = '#E0EDFF';
			ctx.lineCap = 'butt';
			ctx.arc(cx, cy, r, Math.PI, 2 * Math.PI);
			ctx.stroke();

		}

		function circle1(cx, cy, r) {
			ctx.beginPath();
			ctx.lineWidth = lineWidth;
			ctx.strokeStyle = '#FFE6DF';
			ctx.lineCap = 'butt';
			ctx.arc(cx, cy, r, Math.PI, 2 * Math.PI);
			ctx.stroke();

		}
		//画弧线1
		function sector(cx, cy, r, endAngle) {
			ctx.beginPath();
			ctx.lineWidth = lineWidth;

			// // 渐变色 - 可自定义
			// const linGrad = ctx.createLinearGradient(
			//     circleX - radius - lineWidth, circleY, circleX + radius + lineWidth, circleY
			// );
			// linGrad.addColorStop(0.0, '#FAAD14');
			// linGrad.addColorStop(1.0, '#FAAD14');
			// ctx.strokeStyle = linGrad;
			ctx.strokeStyle = "#287FFF";
			//圆弧两端的样式
			ctx.lineCap = 'butt';
			ctx.arc(cx, cy, r, Math.PI, endAngle * Math.PI, false);
			ctx.stroke();

		}
		//画弧线02
		function sector2(cx, cy, r, endAngle) {
			ctx.beginPath();
			ctx.lineWidth = lineWidth;

			// 渐变色 - 可自定义
			// const linGrad = ctx.createLinearGradient(
			//     circleX - radius - lineWidth, circleY, circleX + radius + lineWidth, circleY
			// );
			// if (userInfo && userInfo.level == 200) {
			//     linGrad.addColorStop(0.0, '#FAAD14');
			//     linGrad.addColorStop(1.0, '#FAAD14');
			// } else {
			//     linGrad.addColorStop(0.0, '#1990FF');
			//     linGrad.addColorStop(1.0, '#1990FF');
			// }
			// ctx.strokeStyle = linGrad
			ctx.strokeStyle = "#FF4747"

			//圆弧两端的样式
			ctx.lineCap = 'butt';
			ctx.arc(cx, cy, r, Math.PI, endAngle * Math.PI, false);
			ctx.stroke();

		}
		//刷新
		function loading() {

			//清除canvas内容
			ctx.clearRect(0, 0, circleX * 3, circleY * 3);

			//圆形
			circle(circleX, circleY, radius);
			circle1(circleX, circleY, radius1);
			//圆弧
			sector(circleX, circleY, radius, percent);
			sector2(circleX, circleY, radius1, percent2);
			ctx.draw()
		}
		loading();
	}
</script>

<style lang="scss">
	.game {
		width: 100vw;
		flex: 1;
		background: #F6F6F6;
		padding: 10rpx 32rpx;
		padding-top: 32rpx;
		position: relative;

		&-train {
			background: #FFFFFF;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			margin-top: 16rpx;
			padding: 16rpx 16rpx 16rpx 32rpx;

			&-img {
				width: 240rpx;
				height: 160rpx;
			}

			&-left {
				display: flex;
				flex-direction: column;
				flex: 1;

				&-title {
					font-size: 32rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #111111;

					&-icon {
						margin-right: 16rpx;
					}
				}

				&-text {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					margin-top: 26rpx;
					line-height: 40rpx;
				}
			}
		}

		&-noGame {
			height: 100%;
			width: 100vw;
			position: absolute;
			left: 0;
			top: 120rpx;
			z-index: 1;
		}

		&-doctorQr {
			width: 600rpx;
		}

		&-center {
			display: flex;
			flex-direction: column;
			background: #FFFFFF;
			box-shadow: inset 0rpx 0rpx 3rpx 0rpx rgba(255, 255, 255, 0.5);
			border-radius: 8rpx;
			margin-top: 16rpx;
			padding: 32rpx;
			padding-top: 16rpx;
			padding-bottom: 16rpx;
			margin-bottom: 10rpx;

			&-bottom {
				display: flex;
				flex-direction: column;
				font-size: 20rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;

				&-emotion {
					display: flex;
					flex-direction: column;
					width: 100%;

					&-top {
						width: 100%;
						display: flex;
						align-items: center;
						border-bottom: 1rpx solid #E2E6EC;
						border-left: 1rpx solid #E2E6EC;
						border-radius: 8rpx;
						overflow: hidden;

						&-bg {
							background: #F9FAFB;
						}

						&-left {
							width: 110rpx;
							height: 36rpx;
							border-right: 1rpx solid #E2E6EC;
						}

						&-br1 {
							border-radius: 8rpx 0rpx 0rpx 0rpx;
						}

						&-br2 {
							border-radius: 0rpx 0rpx 0rpx 8rpx;
						}


						&-item {
							border-right: 1rpx solid #E2E6EC;
							height: 36rpx;
							flex: 1;
						}
					}

					&-top:first-child {
						border-top: 1rpx solid #E2E6EC;
					}
				}

				&-avg {
					display: flex;
					align-items: center;
					border-radius: 8rpx;
					border: 1rpx solid #E2E6EC;
					margin-top: 9rpx;
					margin-bottom: 25rpx;
					overflow: hidden;
					height: 36rpx;

					&-left {
						padding-left: 8rpx;
						line-height: 36rpx;
						background: #F9FAFB;
						width: 110rpx;
						border-right: 1rpx solid #E2E6EC;
					}

					&-center {
						background: #fff;
						width: 282rpx;
						height: 100%;
						display: flex;
						align-items: center;
						padding-left: 16rpx;

						&-value {
							font-weight: bolder;
							margin-left: 24rpx;

							&-novalue {
								color: #999999;
								font-weight: 400;
							}
						}
					}

					&-center:last-child {
						border-left: 1rpx solid #E2E6EC;
					}
				}

				&-sum {
					display: flex;
					align-items: center;
					border-radius: 8rpx;
					border: 1rpx solid #E2E6EC;
					border-right: none;
					width: 100%;
					height: 71rpx;
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					overflow: hidden;

					&-left {
						padding-left: 8rpx;
						height: 100%;
						line-height: 71rpx;
						background: #F9FAFB;
						width: 110rpx;
						border-right: 1rpx solid #E2E6EC;
					}

					&-right {
						background: #fff;
						width: 282rpx;
						height: 100%;

						&-item {
							display: flex;
							height: 36rpx;
							line-height: 36rpx;
							padding-left: 16rpx;
							flex: 1;
							border-radius: 0rpx 8rpx 8rpx 0rpx;
							overflow: hidden;

							&-value {
								font-weight: bolder;
								margin-left: 24rpx;

								&-novalue {
									color: #999999;
									font-weight: 400;
								}
							}

						}

						&-item {
							border-right: 1rpx solid #E2E6EC;
							border-bottom: 1rpx solid #E2E6EC;
						}
					}

					&-center {
						background: #fff;
						width: 282rpx;
						height: 100%;

						&-item {
							display: flex;
							height: 36rpx;
							line-height: 36rpx;
							padding-left: 16rpx;
							flex: 1;

							&-value {
								font-weight: bolder;
								margin-left: 24rpx;

								&-novalue {
									color: #999999;
									font-weight: 400;
								}
							}

						}

						&-item {
							border-right: 1rpx solid #E2E6EC;
							border-bottom: 1rpx solid #E2E6EC;
						}
					}
				}

				&-top {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10rpx;

					&-left {
						display: flex;
						flex-direction: column;

						&-item {
							background: #F9FAFB;
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							border: 1rpx solid #E2E6EC;
							display: flex;
							align-items: center;
							margin-bottom: 4rpx;
							overflow: hidden;

							&-text {
								width: 104rpx;
								padding: 8rpx;
								padding-right: 0;
							}

							&-value {
								background: #FFF;
								border-left: 1rpx solid #E2E6EC;
								width: 134rpx;
								padding: 8rpx 0 8rpx 16rpx;
								font-weight: bolder;

								&-other {

									&-novalue {
										color: #999999;
										font-weight: 400;
									}
								}
							}

							&-value:last-child {
								width: 248rpx;
							}
						}
					}
				}

				&-top:last-child {
					margin-bottom: 0;
				}
			}

			&-content {
				display: flex;
				flex-direction: column;
				padding-top: 14rpx;
				margin-bottom: 14rpx;

				&-top {
					display: flex;
					justify-content: space-between;

					&-right {
						display: flex;
						flex-direction: column;
						align-items: center;
						font-size: 20rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
					}

					&-left {
						display: flex;
						flex-direction: column;

						&-box {
							display: flex;
							flex-direction: column;

							&-value {
								font-size: 28rpx;
								font-family: DINAlternate-Bold, DINAlternate;
								font-weight: bold;
								color: #111111;
								margin-top: 14rpx;

								&-other {
									font-size: 22rpx;
									font-family: DINAlternate-Bold, DINAlternate;
									font-weight: bold;
									color: #C3C3C3;
								}

								&-tip {
									font-size: 16rpx;
									font-family: PingFangSC-Medium, PingFang SC;
									font-weight: 500;
									color: #FF8520;
									width: 84rpx;
									height: 24rpx;
									background: #FFEEDF;
									border-radius: 4rpx;
									border: 1rpx solid #FF8520;
									margin-left: 16rpx;
								}

								&-no {
									color: #999999;
								}
							}

							&-title {
								display: flex;
								font-size: 24rpx;
								font-family: PingFangSC-Regular, PingFang SC;
								font-weight: 400;
								color: #277FFF;
								align-items: center;

								&-other {
									font-size: 20rpx;
									font-family: PingFangSC-Regular, PingFang SC;
									font-weight: 400;
									color: #999999;
									margin-left: 6rpx;
								}
							}

							&-title::before {
								content: ' ';
								width: 10rpx;
								height: 24rpx;
								background: #287FFF;
								border-radius: 5rpx;
								margin-right: 8rpx;
							}

							&-two {
								color: #FF4747;
								margin-top: 20rpx;
							}

							&-two::before {
								content: ' ';
								width: 10rpx;
								height: 24rpx;
								background: #FF4747;
								border-radius: 5rpx;
								margin-right: 8rpx;
							}
						}
					}
				}
			}

			&-date {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 2rpx solid #EEEEEE;
				padding-bottom: 8rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;

				&-trend {
					width: 236rpx;
					height: 44rpx;
					background: #EBF3FF;
					border-radius: 8rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #287FFF;
					line-height: 28rpx;
					border: 1rpx solid #287FFF;
				}

				&-update {
					display: flex;
				}
			}
		}

		&-top {
			width: 100%;
			display: flex;
			align-content: center;
			flex-direction: column;

			&-text {
				width: 100%;
				height: 140rpx;
				background: url('~@/static/train/train-rank-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 140rpx;
				border-radius: 8rpx;
				padding: 24rpx;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: space-between;

				&-title {
					font-size: 32rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #287FFF;
				}

				&-btn {
					width: 240rpx;
					height: 36rpx;
					background: #000000;
					font-size: 20rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					border-radius: 8rpx;

					&-icon {
						margin-left: 4rpx;
					}
				}
			}

			&-process {
				display: flex;
				align-content: center;
				width: 100%;
				margin-bottom: 10rpx;
				justify-content: space-between;



				&-can {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}

				&-date {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #666666;

					&-item {
						width: 80rpx;

						&-click {
							font-weight: bold;
							color: #FFFFFF;
						}
					}
				}

				&-box {
					padding-right: 4rpx;
					background: #000000;
					border-radius: 42rpx 42rpx 8rpx 8rpx;
				}
			}

			&-control {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;

				&-box {
					height: 100%;
				}

				&-content {
					display: flex;
					align-items: center;
					flex-direction: column;
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;

					&-date {
						font-size: 20rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						margin-top: 8rpx;
					}
				}
			}

		}
	}
</style>