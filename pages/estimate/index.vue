<template>
	<view class="estimate">
		<view class="estimate-text">
			评估
		</view>
		<view class="estimate-finish" @click="()=>goList()">
			<text class="estimate-finish-text">你已经完成（{{state.qckCogTimes}}）次测评，详情请查看</text>
			<text class="iconfont">&#xe618;</text>
		</view>
		<view class="estimate-list" v-for="(item,index) in state.estimateList" @touchstart="()=>touchstart(item.path)" @touchend="touchend" :key="item.title" @click="()=>go(item.path,item.title)">
			<view class="estimate-list-left">
				<view class="estimate-list-left-title">
					{{item.title}}
				</view>
				<view class="estimate-list-left-text">
					{{item.text}}
				</view>
				<view class="estimate-list-left-other">
					<view class="estimate-list-left-other-time">
						<text class="iconfont">&#xe74f;</text> {{item.time}}
					</view>
					<view class="estimate-list-left-other-skill" v-if="item.skill">
						<text class="iconfont">&#xe7a4;</text> {{item.skill}}
					</view>
				</view>
			</view>
			<!-- 	<view class="estimate-list-hope center" v-if="index>=3">
				敬请期待
			</view> -->
			<image :src="item.img" class="estimate-list-img" mode="widthFix" :class="index==1?'estimate-list-img-last':''"></image>
		</view>
	</view>
	<uni-popup ref="popup" type="center" :is-mask-click="true">
		<PopBoxVue :text="{value:''}" title="当前设备未连接" :btn="{center:'前往设备管理'}" @click="goDevice"></PopBoxVue>
	</uni-popup>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		navigateTo,
		showToast
	} from "../../common/uniTool";
	import PopBoxVue from "../../components/PopBox.vue";
	import {
		getEnvalueNum
	} from "../../service/evaluation";
	import {
		checkBirth
	} from "../../service/scale";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		useLoginStore
	} from "../../stores/login";
	import {
		useHelper
	} from "../../stores/helper";
	import {
		throttle,
		debounce
	} from 'lodash';
	const loginStore = useLoginStore()
	const helper = useHelper(); //设备仓库
	const timeRef = ref(null)
	const popup = ref(null)
	onShow(() => {
		getNum()
	})
	onHide(() => {
		popup.value.close()
	})
	const goDevice = () => {
		navigateTo('/pages/device/index')
	}
	const touchstart = (path) => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		if (!loginStore.qryActiveTrainee) {
			showToast('请添加亲属')
			return
		}
		if (path === '/pages/ivacpt/introduce?type=1') {
			timeRef.value = setTimeout(() => {
				navigateTo(path)
			}, 2500)
		}
	}
	const touchend = () => {
		clearTimeout(timeRef.value)
	}
	const getNum = () => {
		getEnvalueNum({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			state.qckCogTimes = res.data.qckCogTimes
		})
	}
	const state = reactive({
		qckCogTimes: 0,
		estimateList: [{
			img: '/static/train/estimate-train.png',
			title: '快速认知测评',
			text: '注意力、记忆力',
			time: '3-6分钟',
			path: '/pages/evaluation/index'
		}, {
			img: '/static/train/estimate-3.png',
			title: '脑电数据',
			text: '(脑电指标评估EEG)',
			time: '15-20分钟',
			path: '/pages/collect/form'
		}, {
			img: '/static/train/estimate-2.png',
			title: '视听整合持续测试',
			text: 'IVA-CPT(MINI版)',
			time: '9分钟',
			path: '/pages/ivacpt/introduce?type=1'
		}, {
			img: '/static/train/estimate-4.png',
			title: '视听整合持续测试',
			text: 'IVA-CPT(标准版)',
			time: '25分钟',
			path: '/pages/ivacpt/introduce?type=2'
		}, {
			img: '/static/train/estimate-1.png',
			title: 'PPVT',
			text: '智力测试  (面向3岁3个月到8岁5个月)',
			time: '15-20分钟',
			path: '/pages/ppvt/index'
		}],
	})
	const go = debounce((path, title) => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		if (!loginStore.qryActiveTrainee) {
			showToast('请添加亲属')
			return
		}
		if (path) {
			if (title === 'PPVT') {
				checkBirth({
					traineeId: loginStore.qryActiveTrainee
				}).then(res => {
					if (res.data.isConform) {
						navigateTo(path)
					} else {
						showToast('不在本测评所对应的年龄范围内', 3000)
					}
				})
			} else if (path === '/pages/ivacpt/introduce?type=1') {
				if (!helper.address) {
					popup.value.open()
				} else {
					navigateTo(path)
				}
			} else {
				navigateTo(path)
			}
		} else {
			showToast('敬请期待~')
		}
	}, 300); // 2秒节流时间

	const goList = () => {
		if (!loginStore.network) {
			showToast('当前没有网络无法进入', 2000)
			return
		}
		navigateTo(`/pages/home/<USER>/index?traineeId=${loginStore.qryActiveTrainee}`)
	}
</script>

<style lang="scss">
	.estimate {
		background: #F6F6F6;
		padding: 34rpx 32rpx;
		width: 100vw;
		flex: 1;

		&-list {
			display: flex;
			align-items: center;
			height: 210rpx;
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 16rpx;
			padding: 24rpx 52rpx 24rpx 40rpx;
			position: relative;

			&-hope {
				top: 0;
				right: 24rpx;
				position: absolute;
				width: 128rpx;
				height: 36rpx;
				background: #D5E6FF;
				border-radius: 0rpx 0rpx 8rpx 8rpx;
				font-size: 20rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #207BFF;
			}

			&-img {
				width: 200rpx;
				height: 184rpx;

				&-last {
					width: 126rpx;
					margin-right: 50rpx;
				}
			}


			&-left {
				display: flex;
				flex: 1;
				flex-direction: column;

				&-title {
					font-size: 32rpx;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
					margin-bottom: 24rpx;
				}

				&-text {
					font-size: 24rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #111111;
					margin-bottom: 40rpx;
				}

				&-other {
					display: flex;
					align-items: center;

					&-time {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #111111;
					}

					&-skill {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 500;
						color: #111111;
						margin-left: 44rpx;
					}
				}
			}
		}

		&-text {
			font-size: 48rpx;
			font-family: SourceHanSansCN-Bold, SourceHanSansCN;
			font-weight: bold;
			color: #111111;
			margin-bottom: 20rpx;
		}

		&-finish {
			margin: 0 auto;
			padding: 0 24rpx;
			display: flex;
			align-items: center;
			width: 98%;
			height: 56rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(0, 0, 0, 0.15);
			border-radius: 16rpx;
			font-size: 24rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: 500;
			color: #111111;
			margin-bottom: 20rpx;

			&-text {
				flex: 1;
			}
		}
	}
</style>