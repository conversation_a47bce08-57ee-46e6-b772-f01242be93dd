<template>
	<view class="login">
		<view class="login-title">
			欢迎登入｜注册
		</view>
		<view class="login-text">
			<text class="login-text-icon iconfont">&#xe622;</text> 手机号码
		</view>
		<view class="login-phone">
			<uni-easyinput :inputBorder="false" type="number" v-model="state.phoneNumber" :styles="state.styles" :placeholderStyle="state.placeholderStyle" placeholder="请输入您的手机号码"
				@input="changePhoneNumber"></uni-easyinput>
		</view>
		<view class="login-text">
			<text class="login-text-icon iconfont">&#xe609;</text> 验证码
		</view>
		<view class="login-phone">
			<uni-easyinput :inputBorder="false" type="number" v-model="state.smsAuthCode" :clearable="false" :styles="state.styles" :placeholderStyle="state.placeholderStyle" placeholder="请输入您的短信验证码"
				@input="changeSmsAuthCode"></uni-easyinput>
			<view class="login-phone-text" @click="getCode" v-if="!timer">
				获取
			</view>
			<view class="login-phone-sended" v-else>
				已发送（{{state.time}}）
			</view>
		</view>
		<view class="login-btn center" :class="state.smsAuthCode?'login-btn-click':''" @click="getLogin">
			登 录
		</view>
		<view class="login-tips center">
			<radio value="r1" @click="changeAgree" style="transform:scale(0.8)" :checked="state.isAgree" />我已阅读并同意 <text class="login-tips-btn" @click="openUrl(0)">《用户协议》</text>与 <text
				class="login-tips-btn" @click="openUrl(1)">《隐私政策》</text>
		</view>
	</view>
</template>

<script setup>
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		reactive,
		ref,
		onMounted
	} from "vue";
	import {
		isMobile
	} from "../../common/method";
	import {
		showToast,
		navigateTo
	} from "../../common/uniTool";
	import {
		getSmsAuthCode
	} from "../../service";
	import {
		useLoginStore
	} from "../../stores/login";
	const loginStore = useLoginStore()
	const state = reactive({
		styles: {
			placeholderStyle: "color:#DFDFDF;font-size:22rpx",
		},
		smsAuthCode: '',
		phoneNumber: '',
		time: 120,
		isAgree: false
	})
	onShow(() => {
		uni.removeStorage({
			key: 'jump'
		})
		// #ifdef APP-PLUS
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
		// #endif
	})
	const openUrl = (index) => {
		let url = 'http://**************:9900/resources/quickAieve/html/aura.htm'
		if (index) {
			url = 'http://**************:9900/resources/quickAieve/html/privacy.html'
		}
		navigateTo(`/pages/webview/url?url=${url}`)
	}
	const changeAgree = () => {
		state.isAgree = !state.isAgree
	}
	const timer = ref(null)
	const changePhoneNumber = (e) => {
		state.phoneNumber = e
	}
	const changeSmsAuthCode = (e) => {
		state.smsAuthCode = e
	}
	const getLogin = () => {
		if (!state.phoneNumber) {
			showToast('请输入您的手机号码')
			return
		}
		if (!state.smsAuthCode) {
			showToast('请获取验证码')
			return
		}
		if (!state.isAgree) {
			showToast('请阅读后同意')
			return
		}
		loginStore.getLoginIn(state.phoneNumber, state.smsAuthCode)
	}
	const getCode = () => {
		console.log(state.phoneNumber);
		if (!state.phoneNumber) {
			showToast('请输入手机号')
			return
		} else if (!isMobile(state.phoneNumber)) {
			showToast('请输入正确手机号')
			return
		} else {
			getSmsAuthCode({
				phoneNumber: state.phoneNumber
			}).then(res => {
				showToast(res.data)
			}).catch(err => {
				showToast('请检查你的网络设置')
				console.log("err: " + JSON.stringify(err));
			})
			timer.value = setInterval(() => {
				state.time--
				if (state.time == 0) {
					clearInterval(timer.value)
					timer.value = null
					state.time = 120
				}
			}, 1000)
		}
	}
</script>

<style lang="scss">
	.login {
		background: #FFFFFF;
		padding: 170rpx 100rpx;
		position: relative;
		width: 100vw;
		height: 100vh;

		&-title {
			font-size: 32rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			color: #111111;
			margin-bottom: 110rpx;
		}

		&-text {
			font-size: 25rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #111111;
			display: flex;
			align-items: center;

			&-icon {
				font-size: 45rpx;
				margin-right: 6rpx;
			}
		}

		&-phone {
			width: 100%;
			border-bottom: 2rpx solid #F6F6F6;
			margin-top: 30rpx;
			margin-bottom: 72rpx;
			position: relative;

			&-sended {
				font-size: 18rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #DFDFDF;
				position: absolute;
				right: 0;
				bottom: 6rpx;
			}

			&-text {
				position: absolute;
				right: 0;
				bottom: 6rpx;
				font-size: 18rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #2B8FFD;
				border-radius: 7rpx;
				border: 2rpx solid #2B8FFD;
				padding: 6rpx 24rpx;
			}
		}

		&-btn {
			background: linear-gradient(90deg, #D4E7FF 0%, #C4DCFF 100%);
			box-shadow: 0rpx 18rpx 18rpx 0rpx rgba(203, 227, 255, 0.24);
			border-radius: 14rpx;
			font-size: 22rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			padding: 30rpx 240rpx;
			margin-top: 160rpx;

			&-click {
				background: linear-gradient(360deg, #217CFF 0%, #5EBAFF 100%);
				color: #FFFFFF;
			}
		}

		&-tips {
			width: 100%;
			font-size: 18rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			position: absolute;
			bottom: 10rpx;
			left: 50%;
			transform: translateX(-50%);

			&-btn {
				color: #2B8FFD;
			}
		}
	}
</style>