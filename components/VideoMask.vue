<template>
	<view class="mask">
		<view class="mask-box">
			<view class="mask-box-jump center" @click="emit('event')">跳过</view>
			<video id='myVideo' show-center-play-btn="true" :src="props.video" initialTime="0" autoplay="true" :onEnded="onEnded" class='mask-box-video' />
			<image v-if="props.videoBtn" :src="state.finish?props.videoBtn.img1:props.videoBtn.img2" @click="clicked" mode="widthFix" class='mask-box-videoBtn' />
			<ConfirmBottom v-else :text="props.text || '开始练习' " :color="state.finish ? '#55CC66' : '#cccccc' " @event="clicked" />
		</view>

	</view>
</template>

<script setup>
	import {
		showToast
	} from '../common/uniTool';
	import {
		onMounted,
		reactive,
		ref,
		onBeforeMount,
		onUnmounted
	} from "vue";
	import ConfirmBottom from './ConfirmBottom.vue';
	const videoContext = ref(null)
	onMounted(() => {
		videoContext.value = uni.createVideoContext('myVideo')
	})
	onUnmounted(() => {
		videoContext.value.stop()
	})
	const state = reactive({
		finish: false,
		end: true
	})
	const props = defineProps(['video', 'text', 'videoBtn'])
	const emit = defineEmits(['event', 'getState'])

	const clicked = () => {
		console.log(1);
		if (state.finish) {
			return emit('event')
		} else {
			return showToast('尽量看完哦~~~')
		}
	}
	const onEnded = () => {
		state.finish = true
		emit.getState ? emit['getState', true] : ''
	}
</script>

<style lang="scss">
	.mask {
		width: 100vw;
		height: 100vh;
		position: absolute;
		background: rgba(0, 0, 0, 0.7);
		backdrop-filter: blur(16rpx);
		top: 0;
		left: 0;
		z-index: 10;

		&-box {
			display: flex;
			flex-direction: column;
			height: 100%;
			width: 100%;
			justify-content: space-around;
			align-items: center;
			padding: 40rpx 0;

			&-videoBtn {
				width: 590rpx;
			}

			&-jump {
				width: 148rpx;
				height: 64rpx;
				border-radius: 50rpx;
				border: 1rpx solid rgba(255, 255, 255, 0.2);
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
			}

			&-video {
				width: 680rpx;
				height: 680rpx;
			}
		}
	}
</style>