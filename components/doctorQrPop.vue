<template>
	<uni-popup ref="popup" type="center" :animation="false" :is-mask-click="state.isStartDownload?false:true" @change="change">
		<image src="/static/doctor-qr.png" mode="widthFix" class="doctorQr" v-if="state.popType===4"></image>
		<view class="end" v-else>
			<view class="end-title" v-if="state.popType!==5">{{state.popType===3?'训练方案推送':'前往训练'}}</view>
			<view class="end-text" v-if="state.popType===1">
				由于您暂无训练方案，训练APP中您可以进行试练评估，体验数字训练。
			</view>
			<view class="end-text" v-else-if="state.popType===2">
				您即将开始本日的训练任务，请确保<text class="end-text-min">20-30分钟</text>的持续训练不中断。
			</view>
			<view class="end-text center" style="text-align: center;" v-else-if="state.popType===5">
				当前未安装探险打怪程序应用 <br />
				是否立即下载
			</view>
			<view class="end-text" v-else>
				亲爱的{{state.PrescripData.traineeName}}您好！<br />
				已为您制定了训练方案，请您查看。
			</view>
			<view class="end-two1" v-if="state.popType===1">
				<view class="end-two1-top center" @click="junpApp">
					打开训练APP
				</view>
				<view class="end-two1-bottom center" @click="openQr">
					联系医助获取训练方案
				</view>
			</view>
			<view class="end-two2" v-else-if="state.popType===2">
				<view class="end-two2-top center" @click="close">
					取消
				</view>
				<view class="end-two2-bottom center" @click="junpApp">
					打开训练APP
				</view>
			</view>
			<view class="end-two2" v-else-if="state.popType===5">
				<view class="end-two2" v-if="!state.isStartDownload">
					<view class="end-two2-top center" @click="close">
						取消
					</view>
					<view class="end-two2-bottom center" @click="handleUpgrade">
						立即下载
					</view>
				</view>
				<view class="footer" v-else>
					<view class="progress-view" :class="{'active':!state.hasProgress}" @click="handleInstallApp">
						<!-- 进度条 -->
						<view v-if="state.hasProgress" style="height: 100%;">
							<view class="txt">{{percentText()}}</view>
							<view class="progress" :style="{width:(510 * state.currentPercent / 100) + 'rpx'}"></view>
						</view>
						<view v-else>
							<view class="btn upgrade force">{{ state.isDownloadFinish  ? '立即安装' :'下载中...'}}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="end-btn center" @click="openPdf">查看训练方案</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		navigateTo,
		showToast,
		getStorageSync,
		showLoading,
		hideLoading
	} from "@/common/uniTool";
	import {
		useLoginStore
	} from "@/stores/login";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		getQryIsHavePres,
		getQryNewPrescripTrain,
		getQryTraineePrescripPdf
	} from "@/service";
	import {
		pdfUrl
	} from "@/common/global";
	import {
		downloadApp,
		installApp
	} from '@/pages/index/upgrade.js'
	const props = defineProps(['open'])
	const event = defineEmits(['close'])
	const loginStore = useLoginStore()
	const popup = ref(null) ///显示弹窗
	const state = reactive({
		popType: 0,
		prescriptionId: '',
		PrescripData: null,
		isDownloadFinish: false, //是否下载完成
		hasProgress: false, //是否能显示进度条
		currentPercent: 0, //当前下载百分比
		isStartDownload: false, //是否开始下载
		fileName: '', //下载后app本地路径名称
	})

	watch(() => props.open, (open) => {
		open && goGame()
		if (!open) {
			popup.value.close()
		}
	})
	const change = (e) => {
		const {
			show
		} = e
		if (!show) {
			close()
		}
	}
	const getNewPrescrip = (traineeId) => {
		getQryNewPrescripTrain({
			traineeId
		}).then(res => {
			if (res.data && res.data.prescriptionId) {
				state.PrescripData = res.data
				state.prescriptionId = res.data.prescriptionId
				state.popType = 3
				popup.value.open('center')
			}
		})
	}
	onShow(() => {
		if (loginStore.qryActiveTrainee) {
			getNewPrescrip(loginStore.qryActiveTrainee)
			loginStore.getTraineePrescripList(loginStore.qryActiveTrainee)
		}
	})
	const close = () => {
		event('close', false)
	}

	const goGame = () => {
		getQryIsHavePres({
			traineeId: loginStore.qryActiveTrainee
		}).then(res => {
			if (!res.data.isHavePres) {
				state.popType = 1
				popup.value.open('center')
			} else {
				state.popType = 2
				popup.value.open('center')
			}
		})
	}
	const openQr = () => {
		state.popType = 4
	}
	const openPdf = () => {
		console.log(state.prescriptionId, '---------state.prescriptionId');
		getQryTraineePrescripPdf({
			prescriptionId: state.prescriptionId
		}).then(res => {
			showLoading('打开处方中...')
			uni.downloadFile({
				url: `${pdfUrl}resources/quickAieve/pdf/${res.data.fileName}`,
				success: function (res) {
					var filePath = res.tempFilePath;
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function (res) {
							hideLoading()
							console.log('打开文档成功');
						}
					});
				}
			});
		}).catch(err => {
			console.log(err, '-----------err');
		})

	}
	//判断app是否已经安装
	const checkApp = () => {
		if (plus.runtime.isApplicationExist({
				pname: 'com.naoyu.paoku'
			})) {
			return true
		} else {
			return false
		}
	}
	const junpApp = () => {
		console.log('go');
		//#ifdef APP-PLUS
		// 点击事件
		let senddata = {
			"token": getStorageSync("ttoken"),
			"traineeId": loginStore.qryActiveTrainee
		}

		if (plus.os.name == 'Android') {
			if (checkApp()) {
				plus.runtime.launchApplication({
					pname: 'com.naoyu.paoku',
					extra: senddata //传递的参数
				}, function (e) {
					alert('Open system default browser failed: ' + e.message);
				});
			} else {
				state.popType = 5
			}

		}
		//#endif
	}
	//安装app
	const handleInstallApp = () => {
		//下载完成才能安装，防止下载过程中点击
		if (state.isDownloadFinish && state.fileName) {
			installApp(state.fileName, () => {
				//安装成功,关闭升级弹窗
				uni.navigateBack()
			})
		}
	}
	const handleUpgrade = () => {
		let downloadUrl = 'http://101.35.248.239:9900/resources/apk/ADHD-GAME/COG_TRAIN_APP.apk'
		if (downloadUrl) {
			state.isStartDownload = true
			//开始下载App
			downloadApp(downloadUrl, current => {
				//下载进度监听

				state.hasProgress = true
				state.currentPercent = current

			}).then(fileName => {
				//下载完成
				state.isDownloadFinish = true
				state.fileName = fileName
				if (fileName) {
					//自动安装App
					handleInstallApp()
				}
			}).catch(e => {
				console.log(e, 'e')
			})
		} else {
			uni.showToast({
				title: '下载链接不存在',
				icon: 'none'
			})
		}
	}
	const percentText = () => {
		let percent = state.currentPercent;
		if (typeof percent !== 'number' || isNaN(percent)) return '下载中...'
		if (percent < 100) return `下载中${percent}%`
		return '立即安装'

	}
</script>

<style lang="scss">
	.doctorQr {
		width: 600rpx;
	}

	.footer {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		flex-shrink: 0;

		.btn {
			width: 246rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			z-index: 999;
			height: 76rpx;
			box-sizing: border-box;
			font-size: 32rpx;
			border-radius: 10rpx;
			letter-spacing: 2rpx;

			&.force {
				width: 500rpx;
			}

			&.close {
				border: 1px solid #E0E0E0;
				margin-right: 25rpx;
				color: #000;
			}

			&.upgrade {
				background-color: #026DF7;
				color: white;
			}
		}

		.progress-view {
			width: 510rpx;
			height: 76rpx;
			display: flex;
			position: relative;
			align-items: center;
			border-radius: 6rpx;
			background-color: #dcdcdc;
			display: flex;
			justify-content: flex-start;
			padding: 0px;
			box-sizing: border-box;
			border: none;
			overflow: hidden;

			&.active {
				background-color: #026DF7;
			}

			.progress {
				height: 100%;
				background-color: #026DF7;
				padding: 0px;
				box-sizing: border-box;
				border: none;
				border-top-left-radius: 10rpx;
				border-bottom-left-radius: 10rpx;

			}

			.txt {
				font-size: 28rpx;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #fff;
			}
		}
	}

	.end {
		width: 650rpx;
		background: #ffffff;
		border-radius: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
		padding: 64rpx 0 60rpx 0;

		&-two2 {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: 100%;

			&-top {
				width: 246rpx;
				height: 80rpx;
				border-radius: 16rpx;
				border: 2rpx solid #0485F4;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #0485F4;
			}

			&-bottom {
				width: 246rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 16rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #FFFFFF;
			}
		}

		&-two1 {
			display: flex;
			flex-direction: column;
			align-items: center;


			&-top {
				width: 516rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 16rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #FFFFFF;
				margin-bottom: 16rpx;
			}

			&-bottom {
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #0485F4;
				width: 516rpx;
				height: 80rpx;
				border-radius: 16rpx;
				border: 2rpx solid #0485F4;
			}
		}

		&-title {
			font-size: 40rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: bolder;
			color: #111111;
			margin-bottom: 48rpx;


		}

		&-img {
			width: 100%;
		}

		&-text {
			padding: 0 40rpx;
			width: 100%;
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			line-height: 48rpx;
			margin-bottom: 60rpx;

			&-min {
				color: #FF4747;
			}
		}

		&-btn {
			width: 416rpx;
			height: 80rpx;
			background: #0485F4;
			border-radius: 40rpx;
			font-size: 32rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			color: #FFFFFF;

		}
	}
</style>