<template>
	<view class="mask center">
		<image src="../static/reminder-img.png" class="reminder" @click="goDevice"></image>
	</view>
</template>

<script setup>
	const emit = defineEmits(['change'])
	import {
		navigateTo
	} from '../common/uniTool';
	const goDevice = () => {
		navigateTo('/pages/device/index')
	}
</script>

<style>
	.mask {
		width: 100%;
		height: 100vh;
		position: absolute;
		z-index: 2;
		background: rgba(17, 17, 17, 0.7);
		top: 0;
		left: 0;
	}

	.reminder {
		width: 596rpx;
		height: 424rpx;
	}
</style>