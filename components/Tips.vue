<template>
	<view class='tips'>
		<view class='tips-item'>
			<image class="tips-item-img" src="../static/evaluation/quick-true.png" mode="widthFix" />
			<view class='tips-item-num'>
				<view class="tips-item-num-text">
					{{props.text||'累计正确'}}
				</view>
				<view class="tips-item-num-value">
					{{props.trueNum}}
				</view>
			</view>
		</view>
		<view class='tips-item' v-if="props.frequency>=0">
			<image class="tips-item-img" src="../static/evaluation/frequency.png" mode="widthFix" />
			<view class='tips-item-num'>
				<view class="tips-item-num-text">
					剩余次数
				</view>
				<view class="tips-item-num-value">
					{{props.frequency}}
				</view>
			</view>
		</view>
		<view class='tips-item'>
			<image class="tips-item-img" src="../static/evaluation/continuous-true.png" mode="widthFix" />
			<view class='tips-item-num'>
				<view class="tips-item-num-text">
					{{props.text3||'连续正确'}}
				</view>
				<view class="tips-item-num-value">
					{{props.continuousTrueNum}}
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	const props = defineProps(['trueNum', 'frequency', 'continuousTrueNum', 'text', 'text3'])
</script>

<style lang="scss">
	.tips {
		width: 100vw;
		padding: 0 45rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		&-item {
			width: 180rpx;
			height: 62rpx;
			position: relative;

			&-img {
				width: 100%;
				position: absolute;
				z-index: -1;
			}

			&-num {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding-left: 50rpx;

				&-text {
					font-size: 18rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #20507D;
				}

				&-value {
					font-size: 32rpx;
					font-family: DINCondensed-Bold, DINCondensed;
					font-weight: bold;
					color: #258ACF;
				}
			}
		}
	}
</style>