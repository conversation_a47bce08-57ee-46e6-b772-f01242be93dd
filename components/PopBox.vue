<template>
	<view class="pop-end">
		<view class="pop-end-title" v-if="props.title">{{props.title}}</view>
		<view class="" v-if="!props.text['value']">

		</view>
		<view class="pop-end-text" v-else-if="props.textType===1">
			{{props.text['value']}}
		</view>
		<view class="pop-end-text" v-else-if="props.textType===2">
			{{props.text['value']}}
		</view>
		<view class="pop-end-text" v-else>
			{{props.text['value']}}<br />
			{{props.text['other']}}
		</view>
		<view v-if="props.btnType===0">
		</view>
		<view class="pop-end-two1" v-else-if="props.btnType===1">
			<view class="pop-end-two1-top center" @click="()=>event('clickLeft')">
				{{props.btn['left']}}
			</view>
			<view class="pop-end-two1-bottom center" @click="()=>event('clickRight')">
				{{props.btn['right']}}
			</view>
		</view>
		<view class="pop-end-two2" v-else-if="props.btnType===2">
			<view class="pop-end-two2-top center" @click="()=>event('close')">
				{{props.btn['left']}}
			</view>
			<view class="pop-end-two2-bottom center" @click="()=>event('clickLeft')">
				{{props.btn['right']}}
			</view>
		</view>
		<view v-else class="pop-end-btn center" @click="()=>event('click')">{{props.btn['center']}}</view>
	</view>
</template>

<script setup>
	const props = defineProps(['title', 'textType', 'btnType', 'text', 'btn'])
	const event = defineEmits(['clickLeft', 'close', 'clickRight', 'click'])
</script>

<style lang="scss">
	.pop-end {
		width: 650rpx;
		background: #ffffff;
		border-radius: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
		padding: 64rpx 0 60rpx 0;

		&-two2 {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: 100%;

			&-top {
				width: 246rpx;
				height: 80rpx;
				border-radius: 16rpx;
				border: 2rpx solid #0485F4;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #0485F4;
			}

			&-bottom {
				width: 246rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 16rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #FFFFFF;
			}
		}

		&-two1 {
			display: flex;
			flex-direction: column;
			align-items: center;


			&-top {
				width: 516rpx;
				height: 80rpx;
				background: #0485F4;
				border-radius: 16rpx;
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #FFFFFF;
				margin-bottom: 16rpx;
			}

			&-bottom {
				font-size: 32rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: bolder;
				color: #0485F4;
				width: 516rpx;
				height: 80rpx;
				border-radius: 16rpx;
				border: 2rpx solid #0485F4;
			}
		}

		&-title {
			font-size: 40rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: bolder;
			color: #111111;
			margin-bottom: 48rpx;
		}

		&-img {
			width: 100%;
		}

		&-text {
			padding: 0 40rpx;
			width: 100%;
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			line-height: 48rpx;
			margin-bottom: 60rpx;

			&-min {
				color: #FF4747;
			}
		}

		&-btn {
			width: 416rpx;
			height: 80rpx;
			background: #0485F4;
			border-radius: 40rpx;
			font-size: 32rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			color: #FFFFFF;

		}
	}
</style>