<template>
	<view class="recordbox">
		<view class="recordbox-top">
			<image class="recordbox-top-img" :src="getLeftImg()" mode="widthFix"></image>
			<view class="recordbox-top-content">
				<view class="recordbox-top-content-title">
					{{props.data.questionTypeName}}
				</view>
				<view class="recordbox-top-content-info">
					<span style="margin-right: 16rpx;">{{props.data.name}}</span>
					<span style="margin-right: 16rpx;">{{props.data.sex===1?'男':'女'}}</span>
					<span>{{props.data.birth}}</span>
				</view>
				<view class="recordbox-top-content-hospital">
					{{props.data.traineeHospitalName}}
				</view>
			</view>
		</view>
		<view class="recordbox-btn">
			<view class="recordbox-btn-time">
				评估时间：{{props.data.cdate}}
			</view>
			<view v-if="props.data.questionType===11">
			</view>
			<view v-else-if="props.data.questionType===12||props.data.questionType===13" class="recordbox-btn-value center"
				@click="goIva(props.data.evalId,props.data.traineeId,props.data.questionType)">
				查看报告
			</view>
			<view v-else-if="props.data.evalId" class="recordbox-btn-value center" @click="downPdf(props.data.evalId)">
				查看报告
			</view>
			<view v-else-if="props.data.completed" class="recordbox-btn-value center" @click="go(props.data.isWearable,props.data.round,props.data.traineeId,props.data.evaluatId)">
				查看报告
			</view>
			<!-- <view v-else class="recordbox-btn-value-go center" @click="goText(props.data)">
				继续测评
			</view> -->
		</view>
	</view>
</template>

<script setup>
	import {
		updateQryActiveTrainee
	} from '@/service';
	import {
		reportUrl,
		pdfUrl
	} from '@/common/global';
	import {
		navigateTo,
		showLoading,
		hideLoading,
		showToast,
		getStorageSync
	} from '@/common/uniTool';
	import {
		qryFreeEvaResultPDF
	} from '@/service/scale';
	const props = defineProps(['data'])
	const getLeftImg = () => {
		let path = ''
		if (props.data.questionType === 11) {
			path = '/static/record-blc-icon.png'
		} else if (props.data.questionType === 12) {
			path = '/static/ivacpt/record-ivacpt.png'
		} else if (props.data.questionType === 13) {
			path = '/static/ivacpt/record-ivacpt-main.png'
		} else if (props.data.evalId) {
			path = '/static/record-eva-icon.png'
		} else if (props.data.isWearable) {
			path = '/static/record-blc.png'
		} else {
			path = '/static/record-blc-no.png'
		}
		return path
	}
	const goText = (item) => {
		updateQryActiveTrainee({
			traineeId: item.traineeId
		}).then(res => {
			if (!item.signalStatus) {
				navigateTo('/pages/evaluation/introduce?type=0')
			} else if (!item.memoryStatus) {
				navigateTo('/pages/evaluation/introduce?type=1')
			} else if (!item.cancellatStatus) {
				navigateTo('/pages/evaluation/introduce?type=2')
			}
		})
	}
	const go = (type, round, traineeId, evaluatId) => {
		let tToken = getStorageSync('ttoken');
		navigateTo(`/pages/webview/index?url=${reportUrl}#/eegReport/${tToken}/${round}/${traineeId}/SMSCODE?evaluatId=${evaluatId}`)
	}
	const goIva = (evaluatId, traineeId, questionType) => {
		navigateTo(`/pages/webview/index?url=${reportUrl}#/ivacpt/${getStorageSync('ttoken')}/${traineeId}/${evaluatId}/SMSCODE/${questionType}`)
	}
	const downPdf = (evalId) => {
		qryFreeEvaResultPDF({
			evalId
		}).then(res => {
			showLoading('生成报告中....')
			console.log("res: " + JSON.stringify(res));
			console.log(`${pdfUrl}resources/quickAieve/pdf/${res.data.fileName}`, '-----------res');
			uni.downloadFile({
				url: `${pdfUrl}resources/quickAieve/pdf/${res.data.fileName}`,
				success: function (res) {
					var filePath = res.tempFilePath;
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function (res) {
							hideLoading()
							console.log('打开文档成功');
						}
					});
				}
			});
		}).catch(err => {
			showToast(err.msg)
		})
	}
</script>

<style lang="scss">
	.recordbox {
		display: flex;
		flex-direction: column;
		margin-top: 28rpx;

		&-btn {
			display: flex;
			justify-content: space-between;
			margin-top: 16rpx;
			align-items: center;

			&-value {
				width: 137rpx;
				height: 47rpx;
				background: #287FFF;
				border-radius: 7rpx;
				font-size: 18rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;

				&-go {
					border-radius: 7rpx;
					width: 137rpx;
					height: 47rpx;
					font-size: 20rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #287FFF;
					border: 2rpx solid #287FFF;
				}
			}

			&-time {
				font-size: 22rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
			}
		}

		&-top {
			display: flex;
			align-items: center;

			&-img {
				width: 180rpx;
			}

			&-content {
				height: 135rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				margin-left: 21rpx;

				&-info {
					font-size: 25rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
				}

				&-hospital {
					font-size: 22rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
				}

				&-title {
					font-size: 25rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #111111;


				}
			}
		}
	}
</style>