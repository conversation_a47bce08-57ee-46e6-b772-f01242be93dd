<template>
	<view class="count-mask" :class="[small ? 'smask' : '']">
		<view class="count-mask-text" :class="[small ? 'stext' : '']">{{state.time}}</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		watch
	} from "vue";
	const props = defineProps(['count', 'speed', 'isStart', 'small'])
	const emit = defineEmits(['show'])
	const state = reactive({
		timeRef: null,
		time: props.count
	})
	onMounted(() => {
		if (props.isStart) {
			setTimer() // 调用函数
		}
	})
	onUnmounted(() => {
		clearInterval(state.timeRef)
	})
	watch(() => state.time, (state) => {
		if (!state) {
			emit('show', state)
		}
	})
	const setTimer = () => {
		// 3 创建倒计时
		state.timeRef = setInterval(() => {
			if (state.time) {
				state.time -= 1
			} else {
				clearInterval(state.timeRef)
				state.time = 0
			}
		}, props.speed)
	}
</script>

<style lang="scss">
	.count-mask {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: 100;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.8);

		&-text {
			position: absolute;
			top: 30vh;
			left: 50%;
			transform: translateX(-50%);
			width: 342rpx;
			height: 342rpx;
			background: rgba(255, 255, 255);
			font-size: 160rpx;
			font-weight: 600;
			color: #55cc66;
			border-radius: 50%;
			line-height: 342rpx;
			text-align: center;
		}
	}

	.smask {
		background: none;
	}

	.stext {
		position: absolute;
		top: 46vh;
		left: 50%;
		transform: translateX(-50%);
		width: 100rpx;
		height: 100rpx;
		background: #55cc66;
		font-size: 80rpx;
		color: #ffffff;
		line-height: 100rpx;
		text-align: center;
		border-radius: 0%;
	}
</style>
