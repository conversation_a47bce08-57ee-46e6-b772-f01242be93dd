<template>
	<view class="steps">
		<view v-for="(item,index) in props.options" class="steps-item" :key="index">
			<view class="steps-item-num center" :class="props.active>=index+1?'steps-item-num-active':''">
				{{index+1}}
				<view class="steps-item-num-wave" :class="props.active>index+1?'steps-item-num-active':''" v-if="index<props.options.length-1">

				</view>
			</view>
			<view class="steps-item-title" :class="props.active>=index+1?'steps-item-title-active':''">
				{{item.title}}
			</view>

		</view>
	</view>
</template>

<script setup>
	import {
		reactive
	} from "vue";
	const props = defineProps(['options', 'active'])
</script>

<style lang="scss">
	.steps {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100vw;
		padding: 0 32rpx;

		&-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			&-title {
				font-size: 24rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: bold;
				color: #999999;

				&-active {
					color: #FFFFFF;
				}
			}

			&-num {
				border-radius: 50%;
				width: 32rpx;
				height: 32rpx;
				background: #D8D8D8;
				font-size: 24rpx;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #FFFFFF;
				margin-bottom: 8rpx;
				position: relative;

				&-wave {
					width: 158rpx;
					height: 6rpx;
					background: #D8D8D8;
					position: absolute;
					right: -162rpx;
					top: 50%;
					transform: translateY(-50%);
				}

				&-active {
					background: #FFFFFF;
					color: #287FFF;
				}

			}
		}
	}
</style>