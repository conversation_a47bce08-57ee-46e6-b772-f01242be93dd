<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="echarts.onClick" :prop="amplitudeDate" :change:prop="echarts.updateEcharts" id="inhibition" class="echarts"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: 'Echarts',
		props: {
			amplitudeDate: {
				required: true
			},
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="echarts" lang="renderjs">
	let myChart
	let data = new Array(100).fill(25)
	export default {
		data() {
			return {
				address: '',
				option: {
					animation: true,
					animationDuration: 3,
					grid: {
						left: '5%',
						right: '2%',
						bottom: '10%',
						top: '8%'
					},
					xAxis: {
						type: 'category',
						data: [],

					},
					yAxis: {
						type: 'value',
						min: 0,
						max: 100
					},
					series: [{
						type: 'line',
						data: data,
						label: {
							show: true,
							position: 'top',
							formatter: '{c}%',
						},
						lineStyle: {
							width: 1
						},
					}]
				}
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('inhibition'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				myChart && myChart.clear()
				// 监听 service 层数据变更
				if (Object.keys(newValue).length > 0) {
					let data = newValue['tendencyList'][1]['data']
					this.option.series[0].data = data
					this.option.xAxis.data = newValue['time']
					myChart && myChart.setOption(this.option)
				}
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 258rpx;
		position: relative;
		background: #FAFAFA;
		margin-bottom: 16rpx;
	}



	.echarts {
		height: 100%;
		width: 100%;
	}
</style>