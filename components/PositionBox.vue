<template>
	<view class='place' :style="{ width:props.wid + 'rpx', height: props.hei + 'rpx' }">
		<view v-for="item in state.positionArr">
			<image v-if="item.img" class="place-img" :src="'http://**************:9900/' + item.img" :style='{ top: item.top, left: item.left }' />
			<view class='place-box' v-else :style="{ top: item.top, left: item.left }">
				{{num}}
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		watch
	} from "vue";
	const props = defineProps(['wid', 'hei', 'length', 'num', 'imgList'])
	watch(() => props.imgList, (imgList) => {
		console.log(imgList, '---------imgList');
		imgList.length > 0 && getPosition()
	})

	const state = reactive({
		positionArr: []
	})
	const getPosition = () => {
		const length = props.length
		const imgList = props.imgList
		const arr = [{
				top: '',
				left: '',
				img: ''
			},
			{
				top: '',
				left: '',
				img: ''
			},
			{
				top: '',
				left: '',
				img: ''
			},
			{
				top: '',
				left: '',
				img: ''
			},
			{
				top: '',
				left: '',
				img: ''
			},
		]
		for (let index = 0; index < length; index++) {
			const position = getNum(index + 1)
			const element = arr[index];
			element.left = position.left
			element.top = position.top
		}
		if (imgList && imgList.length > 0) {
			for (let index = 0; index < imgList.length; index++) {
				const element1 = arr[index];
				const element2 = imgList[index];
				element1.img = element2
			}
		}
		console.log(arr);
		state.positionArr = arr
	}
	//生成从minNum到maxNum的随机数
	function randomNum(minNum, maxNum) {
		switch (arguments.length) {
			case 1:
				return parseInt(Math.random() * minNum + 1, 10);
				break;
			case 2:
				return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
				break;
			default:
				return 0;
				break;
		}
	}

	const getNum = (index) => {
		const position = {
			top: '',
			left: ''
		}
		if (length == 1) {
			switch (index) {
				case 1:
					position.left = randomNum(0, hei - 222) + 'rpx'
					position.top = randomNum(0, hei - 222) + 'rpx'
					return position
					break;
				default:
					break;
			}
		} else if (length == 2) {
			switch (index) {
				case 1:
					position.top = randomNum(0, 222) + 'rpx'
					position.left = randomNum(0, 222) + 'rpx'
					return position
					break;
				case 2:
					position.top = 444 + 'rpx'
					position.left = randomNum(0, 444) + 'rpx'
					return position
					break;
				default:
					break;
			}
		} else if (length == 3) {
			switch (index) {
				case 1:
					position.top = 0 + 'rpx'
					position.left = randomNum(0, 444) + 'rpx'
					return position
					break;
				case 2:
					position.top = 222 + 'rpx'
					position.left = randomNum(0, 444) + 'rpx'
					return position
					break;
				case 3:
					position.top = 444 + 'rpx'
					position.left = randomNum(0, 444) + 'rpx'
					return position
					break;
				default:
					break;
			}
		} else if (length == 4) {
			switch (index) {
				case 1:
					position.top = 0 + 'rpx'
					position.left = randomNum(0, 222) + 'rpx'
					return position
					break;
				case 2:
					position.top = randomNum(0, 222) + 'rpx'
					position.left = 444 + 'rpx'
					return position
					break;
				case 3:
					position.top = 444 + 'rpx'
					position.left = randomNum(222, 444) + 'rpx'
					return position
					break;
				case 4:
					position.top = randomNum(222, 444) + 'rpx'
					position.left = 0 + 'rpx'
					return position
					break;
				default:
					break;
			}
		} else if (length == 5) {
			switch (index) {
				case 1:
					position.top = 0 + 'rpx'
					position.left = randomNum(0, 222) + 'rpx'
					return position
					break;
				case 2:
					position.top = randomNum(0, 222) + 'rpx'
					position.left = 444 + 'rpx'
					return position
					break;
				case 3:
					position.top = 444 + 'rpx'
					position.left = randomNum(222, 444) + 'rpx'
					return position
					break;
				case 4:
					position.top = randomNum(222, 444) + 'rpx'
					position.left = 0 + 'rpx'
					return position
					break;
				case 5:
					position.top = 222 + 'rpx'
					position.left = 222 + 'rpx'
					return position
					break;
				default:
					break;
			}
		}

	}
</script>

<style lang="scss">
	.place {
		display: flex;
		flex-direction: column;
		position: relative;
		width: 666rpx;
		height: 666rpx;
		margin: 0 auto;

		&-box {
			position: absolute;
			background: rgb(144, 98, 98);
			width: 222rpx;
			height: 222rpx;
		}

		&-img {
			width: 222rpx;
			height: 222rpx;
			position: absolute;
		}
	}
</style>
