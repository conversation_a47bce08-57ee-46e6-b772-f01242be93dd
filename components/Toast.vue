<template>
	<view class="toast-mask">
		<view class="toast-position" :style="{ 'top': props.top, 'background': props.background }">
			<view class="toast-box">
				<text v-if="props.icon" class='toast-box-icon iconfont' :style="{ 'color': props.color }">{{props.icon}}</Text>
				<!-- <image v-if="props.img" class="toast-box-img"
					src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.redocn.com%2Fsheying%2F20150414%2Fxiyangxiadedahaimeijing_4161285.jpg&refer=http%3A%2F%2Fimg.redocn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1662189276&t=aa01dd616b25cf6b0ace27f15cbea9ac" /> -->
				<view class="toast-box-text" :style="{ 'color': props.textColor }">{{props.text}}</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	const props = defineProps(['text', 'icon', 'img', 'top', 'color', 'background', 'textColor'])
</script>

<style lang="scss">
	.toast-mask {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		width: 100%;
		height: 100vh;
	}

	.toast-position {
		width: 160rpx;
		height: 70rpx;
		background: #636164;
		border-radius: 40rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 42%;
	}

	.toast-box {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		&-icon {
			font-size: 50rpx;
			color: #fa5151;
			margin-top: 2rpx;
		}

		&-img {
			width: 48rpx;
			height: 48rpx;
			margin-right: 10rpx;
		}

		&-text {
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #ffffff;
			margin-left: 10rpx;
		}
	}
</style>
