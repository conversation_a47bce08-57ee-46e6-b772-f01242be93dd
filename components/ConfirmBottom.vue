<template>
	<view class="confirm" :style="{ 'background': props.color }" @click="emit('event')">
		<text class="confirm-text">{{props.text}}</text>
	</view>
</template>

<script setup>
	const props = defineProps(['text', 'color'])
	const emit = defineEmits(['event'])
</script>

<style lang="scss">
	.confirm {
		width: 670rpx;
		height: 120rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border-radius: 24rpx;
		margin: 0 auto;

		&-text {
			font-size: 40rpx;
			font-weight: 600;
			color: #ffffff;
		}
	}
</style>
