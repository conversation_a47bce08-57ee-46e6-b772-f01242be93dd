{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "产品首页",
				// "navigationBarBackgroundColor": "#A9D3FE"
				// "navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/index/upgrade", //升级窗口页面
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom", //导航栏自定义
				"app-plus": {
					"bounce": "none",
					"animationType": "none", //取消窗口动画
					"background": "transparent" // 设置背景透明
				}
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "关于版本"
			}
		},
		{
			"path": "pages/ivacpt/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ivacpt/introduce",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ivacpt/standard",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ivacpt/practice",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/estimate/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ppvt/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ppvt/question/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/ppvt/question/test",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/shopping/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/game/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/game/growthPath",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/game/course",
			"style": {
				"app-plus": {
					"titleNView": false,
					"scrollIndicator": "vertical"
				}
			}
		},
		{
			"path": "pages/game/introduce",
			"style": {
				"navigationBarTitleText": "",
				"transparentTitle": "auto"
			}
		},
		{
			"path": "pages/collect/fullScreen",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/evaluation/summarizeAbility",
			"style": {
				"navigationBarTitleText": "归纳总结能力",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/evaluation/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/evaluation/digitalErasure",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/home/<USER>/index",
			"style": {
				"navigationBarTitleText": "测评记录"
			}
		},
		{
			"path": "pages/home/<USER>/money",
			"style": {
				"navigationBarTitleText": "脑力值成长记录"
			}
		},
		{
			"path": "pages/home/<USER>/prescription",
			"style": {
				"navigationBarTitleText": "训练方案"
			}
		},
		{
			"path": "pages/home/<USER>/index",
			"style": {
				"navigationBarTitleText": "家庭亲属"
			}
		},
		{
			"path": "pages/train/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/train/rankingList",
			"style": {
				"navigationBarTitleText": "排行榜"
			}
		},
		{
			"path": "pages/train/introduce",
			"style": {
				"navigationBarTitleText": "脑认知数字训练"
			}

		},
		{
			"path": "pages/train/directory",
			"style": {
				"navigationBarTitleText": "认知能力训练"
			}

		},
		{
			"path": "pages/scale/index",
			"style": {
				"navigationBarTitleText": "SNAP-IV问卷",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/informationForm/index",
			"style": {
				"navigationBarTitleText": "信息表"
			}
		},
		{
			"path": "pages/webview/index",
			"style": {
				"navigationBarTitleText": "儿童测评报告"
			}
		},
		{
			"path": "pages/webview/url",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		// {
		// 	"path": "pages/quickAieve/index",
		// 	"style": {
		// 		"navigationBarTitleText": "三分钟专注力快测",
		// 		"navigationStyle": "custom"
		// 	}
		// },
		// {
		// 	"path": "pages/quickAieve/quickList",
		// 	"style": {
		// 		"navigationBarTitleText": "三分钟专注力快测",
		// 		"navigationStyle": "custom"

		// 	}
		// }, 
		{
			"path": "pages/evaluation/inhibitoryControl",
			"style": {
				"navigationBarTitleText": "不要被干扰",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/evaluation/numberBackwards",
			"style": {
				"navigationBarTitleText": "打地鼠",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/device/index",
			"style": {
				"navigationBarTitleText": "我的BCI设备"
			}
		},
		{
			"path": "pages/evaluation/introduce",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/relax/introduce",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/relax/relaxList",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/relax/index",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/relax/relaxVideo",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/collect/form",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/evaluation/iControlTeaching",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/train/trend",
			"style": {
				"navigationBarTitleText": "训练效果走势"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"rpxCalcMaxDeviceWidth": 2560, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		"rpxCalcIncludeWidth": 750 // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
		// "pageOrientation": "landscape"
	},
	"uniIdRouter": {},
	"tabBar": {
		"color": "#666666",
		"selectedColor": "#4587FF",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"height": "76px",
		"iconWidth": "34px",
		"fontSize": "16px",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "static/index.png",
				"selectedIconPath": "static/index-click.png",
				"text": "首页"
			}, {
				"pagePath": "pages/train/index",
				"iconPath": "static/train.png",
				"selectedIconPath": "static/train-click.png",
				"text": "训练"
			},
			{
				"pagePath": "pages/estimate/index",
				"iconPath": "static/estimate.png",
				"selectedIconPath": "static/estimate-click.png",
				"text": "评估"
			},
			{
				"pagePath": "pages/home/<USER>",
				"iconPath": "static/home.png",
				"selectedIconPath": "static/home-click.png",
				"text": "我的"
			}
		]
	}
}