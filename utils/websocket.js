// @/utils/websocket.js
import {
	isJSON
} from "@/common/method.js"
import {
	getStorageSync
} from "../common/uniTool";
import {
	getCsrfToken
} from "../service";
class WebSocketClass {
	constructor(url, address) {
		this.lockReconnect = false; // 是否开始重连
		this.wsUrl = ""; // ws 地址
		this.globalCallback = null; // 回调方法
		this.userClose = false; // 是否主动关闭
		this.address = address
		this.createWebSocket(url, address);
	}

	async createWebSocket(url, address = 'Dbay-') {
		if (typeof(uni.connectSocket) === 'undefined') {
			this.writeToScreen("不支持WebSocket，无法获取数据");
			return false
		}
		this.wsUrl = url;
		try {
			let result = await getCsrfToken()
			// 创建一个this.ws对象【发送、接收、关闭socket都由这个对象操作】
			this.ws = uni.connectSocket({
				url: this.wsUrl,
				header: {
					'tToken': getStorageSync('ttoken'),
					'linkType': address.substring(0, 4) === 'Dbay' ? 'aliDbayData' : 'brwDbayData',
					'sourceType': 'SMSCODE',
					'X-CSRF-TOKEN': result.data.csrfToken
				},
				success: (data) => {
					console.log(data, '----------data');
					this.initEventHandle()
				},
				fail: (e) => {
					console.log(e);
				}
			});
		} catch (e) {
			console.log(e);
			this.reconnect(url, address);
		}
	}

	// 初始化
	initEventHandle() {
		/**
		 * 监听WebSocket连接打开成功
		 */

		// #ifdef H5
		this.ws.onopen = (event) => {
			console.log("WebSocket连接打开");
		};
		// #endif

		// #ifdef APP-PLUS
		this.ws.onOpen(res => {
			console.log('WebSocket连接打开');
		});
		// #endif


		/**
		 * 连接关闭后的回调函数
		 */

		// #ifdef H5
		this.ws.onclose = (event) => {
			if (!this.userClose) {
				this.reconnect(this.wsUrl, this.address); //重连
			}
		};
		// #endif

		// #ifdef APP-PLUS
		this.ws.onClose((err) => {
			console.log(err);
			if (!this.userClose) {
				this.reconnect(this.wsUrl, this.address); //重连
			}
		});
		// #endif


		/**
		 * 报错时的回调函数
		 */

		// #ifdef H5
		this.ws.onerror = (event) => {
			if (!this.userClose) {
				this.reconnect(this.wsUrl, this.address); //重连
			}
		};
		// #endif

		// #ifdef APP-PLUS
		this.ws.onError(() => {
			console.log('onError');
			if (!this.userClose) {
				this.reconnect(this.wsUrl, this.address); //重连
			}
		});
		// #endif


		/**
		 * 收到服务器数据后的回调函数
		 */

		// #ifdef H5
		this.ws.onmessage = (event) => {
			if (isJSON(event.data)) {
				const jsonobject = JSON.parse(event.data)
				this.globalCallback && this.globalCallback(jsonobject)
			} else {
				this.globalCallback && this.globalCallback(event.data)
			}
		};
		// #endif

		// #ifdef APP-PLUS
		this.ws.onMessage(event => {
			if (isJSON(event.data)) {
				const jsonobject = JSON.parse(event.data)
				this.globalCallback && this.globalCallback(jsonobject)
			} else {
				this.globalCallback && this.globalCallback(event.data)
			}
		});
		// #endif
	}

	// 关闭ws连接回调
	reconnect(url, address) {
		if (this.lockReconnect) return;
		this.ws.close();
		this.lockReconnect = true; // 关闭重连，没连接上会一直重连，设置延迟避免请求过多
		setTimeout(() => {
			this.createWebSocket(url, address);
			this.userClose = false
			this.lockReconnect = false;
		}, 1000);
	}

	// 发送信息方法
	webSocketSendMsg(msg) {
		this.ws && this.ws.send({
			data: msg,
			success: () => {
				// console.log("消息发送成功");
			},
			fail: (err) => {
				// this.reconnect(this.wsUrl)
			}
		});
	}

	// 获取ws返回的数据方法
	getWebSocketMsg(callback) {
		this.globalCallback = callback
	}

	// 关闭ws方法
	closeSocket() {
		if (this.ws) {
			this.userClose = true;
			this.ws.close({
				success: (res) => {
					console.log("关闭成功", res)
				},
				fail: (err) => {
					console.log("关闭失败", err)
				}
			});
		}
	}

	writeToScreen(massage) {
		console.log(massage);
	}
}
export default WebSocketClass;