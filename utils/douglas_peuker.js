// 计算两点间距离
function calculationDistance(point1, point2) {
	// point1[0]此处的字段要与自己定义的一致，如果是对象，可以如下操作；如果是json数据按照 point1.latitude 的形式操作
	const x1 = point1[0];
	const y1 = point1[1];
	const x2 = point2[0];
	const y2 = point2[1];
	const xy = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
	return xy;
}

// 计算点pX到点pA和pB所确定的直线的距离
function distToSegment(start, end, center) {
	const a = Math.abs(calculationDistance(start, end));
	const b = Math.abs(calculationDistance(start, center));
	const c = Math.abs(calculationDistance(end, center));
	const p = (a + b + c) / 2.0;
	const s = Math.sqrt(Math.abs(p * (p - a) * (p - b) * (p - c)));
	return (s * 2.0) / a;
}

// 递归方式压缩轨迹
function compressLine(coordinate, result, start, end, dMax) {
	if (start < end) {
		let maxDist = 0;
		let currentIndex = 0;
		const startPoint = coordinate[start];
		const endPoint = coordinate[end];

		for (let i = start + 1; i < end; i++) {
			const currentDist = distToSegment(startPoint, endPoint, coordinate[i]);
			if (currentDist > maxDist) {
				maxDist = currentDist;
				currentIndex = i;
			}
		}

		if (maxDist >= dMax) {
			// 将当前点加入到过滤数组中
			// console.warn('maxDist'+maxDist);
			result.push(coordinate[currentIndex]);
			// 将原来的线段以当前点为中心拆成两段，分别进行递归处理
			compressLine(coordinate, result, start, currentIndex, dMax);
			compressLine(coordinate, result, currentIndex, end, dMax);
		}
	}
	return result;
}

// 供调用的抽稀入口函数
/**
 * coordinate  原始轨迹Array<{latitude,longitude}>
 * dMax  允许的最大误差距离；默认为10，同时此值需参考 currentDist 值进行设置，例如 currentDist = 0.00000134724232，则 dMax 设置为 0.000001 比较合适
 * resultLatLng  抽稀后的数据
 */

export default function douglasPeucker(coordinate, dMax = 10) {
	if (!coordinate || !(coordinate.length > 2)) {
		return null;
	}
	coordinate.forEach((item, index) => {
		item.id = index;
	});

	const result = compressLine(coordinate, [], 0, coordinate.length - 1, dMax);
	result.push(coordinate[0]);
	result.push(coordinate[coordinate.length - 1]);
	const resultLatLng = result.sort((a, b) => {
		if (a.id < b.id) {
			return -1;
		} else if (a.id > b.id) return 1;
		return 0;
	});
	resultLatLng.forEach((item) => {
		item.id = 0;
	});
	return resultLatLng;
}