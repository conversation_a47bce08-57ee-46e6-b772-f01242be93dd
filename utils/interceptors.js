/*
 * @Description:拦截器
 * @Author: 小雨
 * @Date: 2023-03-08 10:28:02
 * @LastEditTime: 2023-03-08 13:25:27
 * @LastEditors: 小雨
 */

import httpRequest from '@/utils/request.js';
import {
	hideLoading,
	navigateTo,
	showToast
} from '../common/uniTool';
import {
	pageToLogin
} from './abnormal';

// 请求拦截器
httpRequest.interceptors.request((request) => {
	// 获取登录后存储在本地的token信息
	let token = uni.getStorageSync('ttoken');
	let requestType = request.url.split('/');
	let typeVal = requestType[requestType.length - 1];
	if (typeVal === 'getSmsAuthCode' || typeVal === 'loginIn') {
		// 判断筛选出以上三个页面不需要为请求头设置token，根据自己的项目情况而定
		delete request.header.tToken
	} else {
		request.header.tToken = token;
	}
	return request;
});

export const HTTP_STATUS = {
	SUCCESS: 200,
	CREATED: 201,
	ACCEPTED: 202,
	CLIENT_ERROR: 400,
	AUTHENTICATE: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
};
// 响应拦截器
httpRequest.interceptors.response((response) => {
	// hideLoading()
	// 超时重新登录
	if (response.errMsg.indexOf('statusCode:-1') != -1) {
		showToast('当前没有网络无法进入', 2000)
		return
	}
	if (response.statusCode === HTTP_STATUS.SERVER_ERROR || response.statusCode === HTTP_STATUS.BAD_GATEWAY) {
		return Promise.reject({
			desc: '服务器错误'
		});
	} else if (response.statusCode === HTTP_STATUS.SUCCESS) {
		if (response.data.code === '0000') {
			return response.data;
		} else if (response.data.code == '9998') {
			//非法登录
			let jump = uni.getStorageSync('jump') //以下解决多次跳转登录页的重点
			if (!jump) {
				//以下做token失效的操作
				setTimeout(() => {
					showToast('登录过期，请重新点击');
					pageToLogin();
				}, 100)
				uni.setStorageSync('jump', 'true')
			}
			return Promise.reject(response.data);
		} else if (response.data.code == '2008') {
			showToast(response.data.desc)
		} else if (response.data.code == '9991') {
			navigateTo('/pages/order/index?type=300');
		} else if (response.data.code == '9995') {
			navigateTo('/pages/order/index?type=100');
		} else {
			return Promise.reject(response.data);
		}
	}
});

export default httpRequest;