/*
 * @Description: 异常处理
 * @Author: 小雨
 * @Date: 2023-03-08 13:20:48
 * @LastEditTime: 2023-03-08 13:25:36
 * @LastEditors: 小雨
 */

import {
	switchTab,
	reLaunch,
	clearStorage
} from "../common/uniTool";

/**
 * @description 获取当前页url
 */
export const getCurrentPageUrl = () => {
	let pages = getCurrentPages()
	if (pages.length > 0) {
		let currentPage = pages[pages.length - 1];
		let url = currentPage.route;
		return url;
	}

};

export const pageToLogin = () => {
	let path = getCurrentPageUrl();
	reLaunch('/pages/login/index')
};