export const BleController = {
	/**
	 * 蓝牙连接状态
	 */
	addConnectStateListen(callBack) {
		this.public.bleConnectStateCallBack = callBack
	},
	/**
	 * 蓝牙连接状态
	 */
	addDeviceListListen(callBack) {
		this.public.bleListCallBack = callBack
	},
	/**
	 * 传输数据监听
	 */
	addTransferDataListen(callBack) {
		this.public.bleProgressDataCallBack = callBack
	},
	/**
	 * 数据传输完成监听
	 */
	addLockDataListen(callBack) {
		this.public.bleLockDataCallBack = callBack
	},
	/**
	 * 特征值列表监听
	 */
	addCharacteristicsListListen(callBack) {
		this.public.bleCharacteristicsListCallBack = callBack
	},
	/**
	 * 数据接受中
	 */
	addDeviceAcceptListListen(callBack) {
		this.public.bleAcceptistCallBack = callBack
	},
	/**
	 * 写入特征值状态
	 */
	addWriteStateListen(callBack) {
		this.public.bleWriteStateCallBack = callBack
	},
	// 连接状态
	connectStateListen(params) {
		if (this.public.bleConnectStateCallBack) {
			this.public.bleConnectStateCallBack(params)
		}
	},
	// 设备列表
	deviceListListen(params) {
		if (this.public.bleListCallBack) {
			this.public.bleListCallBack(params)
		}
	},
	// 数据传输中
	transferDataListen(params) {
		if (this.public.bleTransferDataCallBack) {
			this.public.bleTransferDataCallBack(params)
		}
	},
	// 数据接受中
	acceptDataListen(params) {
		if (this.public.bleAcceptistCallBack) {
			this.public.bleAcceptistCallBack(params)
		}
	},
	// 测量完成
	lockDataListen(params) {
		if (this.public.bleLockDataCallBack) {
			this.public.bleLockDataCallBack(params)
		}
	},
	// 写入特征值状态
	writeStateListen(params) {
		if (this.public.bleWriteStateCallBack) {
			this.public.bleWriteStateCallBack(params)
		}
	},
	// 特征值列表
	characteristicsListListen(params) {
		if (this.public.bleCharacteristicsListCallBack) {
			this.public.bleCharacteristicsListCallBack(params)
		}
	},
	public: {
		bleConnectStateCallBack: null,
		bleTransferDataCallBack: null,
		bleLockDataCallBack: null,
		bleWriteStateCallBack: null,
		bleCharacteristicsListCallBack: null,
		bleAcceptistCallBack: null,
		bleListCallBack: null
	}
}

export const BLUE_STATE = {
	/**
	 * 蓝牙不可用
	 */
	UNAVAILABLE: {
		label: '请检查手机蓝牙是否开启',
		code: -1
	},
	/**
	 * 等待连接蓝牙
	 */
	READY: {
		label: '等待连接蓝牙',
		code: 10000
	},
	/**
	 * 等待连接蓝牙(重启蓝牙适配器)
	 */
	READYAGAIN: {
		label: '等待连接蓝牙',
		code: 10001
	},
	/**
	 * 正在搜索设备...
	 */
	SCANING: {
		label: '正在搜索设备...',
		code: 12000
	},
	/**
	 * 正在传输数据...
	 */
	TRANSMITDATA: {
		label: '正在传输数据...',
		code: 13000
	},
	/**
	 * 数据传输完成
	 */
	LOCKDATA: {
		label: '数据已锁定',
		code: 11000
	},
	/**
	 * 已连接蓝牙
	 */
	CONNECTSUCCESS: {
		label: '已连接蓝牙',
		code: 200
	},
	/**
	 * 写入特征已准备就绪
	 */
	WRITEREADY: {
		label: '写入特征已准备就绪',
		code: 201
	},
	/**
	 * 蓝牙连接已断开
	 */
	DISCONNECT: {
		label: '蓝牙连接已断开',
		code: 500
	},
	/**
	 * 连接失败, 请重试！
	 */
	CONNECTFAILED: {
		label: '连接失败, 请重试！查看是否打开蓝牙设备',
		code: -2
	},
	/**
	 * 无位置权限
	 */
	NOLOCATIONPERMISSION: {
		label: '您需要打开位置权限，才能进行蓝牙连接设备。请前往手机设置页打开权限后重试',
		code: 10007
	},
	/**
	 * 当前系统版本过低，请升级后重试！
	 */
	VERSIONLOW: {
		label: '当前系统版本过低，请升级后重试！',
		code: 10009
	},
	/**
	 * 系统异常，请稍后重试！
	 */
	SYSTEMERROR: {
		label: '系统异常，请稍后重试！',
		code: 10008
	}
}