import {
	BLUE_STATE
} from "./bluType"
import {
	BleController
} from '@/utils/bluType';
import {
	hideLoading,
	showToast
} from "@/common/uniTool";

let bleControl = null
let currentDevice = {}

let writeData = {
	device: {},
	serviceID: '',
	characteristicID: ''
}
export const Blue = {
	/**
	 * 蓝牙连接状态：200-> 已连接；-1 ->未连接
	 */
	connectState: -1,
	characteristics: [],
	bleConnectDeviceID: null,
	// 开始蓝牙设备扫描
	start() {
		this._lastLockData = ""
		bleControl = BleController
		if (this.bleConnectDeviceID) { // 设备已连接
			this.connectState = 200
			bleControl.connectStateListen(BLUE_STATE.CONNECTSUCCESS);
			this.startBluetoothDevicesDiscovery()
			return
		};
		bleControl.connectStateListen(BLUE_STATE.READY)
		// 打开蓝牙适配器
		uni.openBluetoothAdapter({
			success: (res) => {
				this.startBluetoothDevicesDiscovery()
			},
			fail: (res) => {
				console.error('打开蓝牙适配器失败：', res);
				if (res.code === 10001) {
					bleControl.connectStateListen(BLUE_STATE.UNAVAILABLE)
				}
				// Android 系统特有，系统版本低于 4.3 不支持 BLE
				if (res.code === 10009) {
					bleControl.connectStateListen(BLUE_STATE.VERSIONLOW)
				}
				if (res.code === 10008) {
					bleControl.connectStateListen(BLUE_STATE.SYSTEMERROR)
				}
			},
			complete: () => {
				// 监听蓝牙适配器状态
				// uni.onBluetoothAdapterStateChange(res => {
				// 	console.log('蓝牙适配器状态：', res);
				// 	if (res.available) {
				// 		this._isDiscovering = res.discovering
				// 		bleControl.connectStateListen(BLUE_STATE.READYAGAIN)
				// 		this.startBluetoothDevicesDiscovery()
				// 	} else {
				// 		// 蓝牙模块未开启
				// 		bleControl.connectStateListen(BLUE_STATE.UNAVAILABLE)
				// 	}
				// })
			}
		})
	},
	//关闭蓝牙连接
	closeBLEConnection(deviceId) {
		uni.closeBLEConnection({
			deviceId,
			success(res) {
				this.bleConnectDeviceID = null
			}
		})
	},
	// 关闭蓝牙连接，关闭状态监听，初始化状态
	stop() {
		// if (this.bleConnectDeviceID) {
		// 	this.closeBLEConnection(this.bleConnectDeviceID)
		// }
		this.bleConnectDeviceID = null
		this.connectState = -1
		uni.closeBluetoothAdapter()
		// uni.offBluetoothAdapterStateChange()
		// uni.offBLEConnectionStateChange()
		uni.stopBluetoothDevicesDiscovery()
	},
	// 停止蓝牙扫描
	stopBluetoothDevicesDiscovery() {
		uni.stopBluetoothDevicesDiscovery({
			success(res) {
				console.log('停止扫描');
			}
		})
	},
	// 开始蓝牙扫描
	startBluetoothDevicesDiscovery() {
		console.log('开启扫描');
		uni.startBluetoothDevicesDiscovery({
			allowDuplicatesKey: true,
			success: (res) => {
				bleControl.connectStateListen(BLUE_STATE.SCANING)
				this.onBluetoothDeviceFound()
			},
			fail: (res) => {
				console.log('位置失败--', res);
				if (res.code === 10000) {
					this.stop()
					bleControl.connectStateListen(BLUE_STATE.NOLOCATIONPERMISSION)
				}
			}
		})
	},
	// 搜索附近设备
	onBluetoothDeviceFound() {
		uni.onBluetoothDeviceFound(res => {
			res.devices.forEach(device => {
				// device = {
				// 	RSSI: -50,
				// 	advertisData: ArrayBuffer(19),
				// 	advertisServiceUUIDs: Array(2),
				// 	connectable: true,
				// 	deviceId: "EE59532DF324-4SFSD-34635D-BB893-76SGSDFV",
				// 	localName: "shebeimingcheng",
				// 	name: "shebeimingcheng", 
				// }
				// if (device.name && device.name.substring(0, 5) !== 'Dbay-') return

				// let abHex = this.ab2hex(device.advertisData)
				// let mac = this.ab2hex(device.advertisData.slice(2, 8), ':').toUpperCase()
				// device.macAddr = mac
				// 根据自己的设备修改
				// if (abHex.length == 38) {
				// 	this.stopBluetoothDevicesDiscovery()
				// 	this.createBLEConnection(device)
				// }
				if (device.name === "" && device.name === "未知设备") return
				if (device.name && device.name.substring(0, 4) === 'CT10') { //获取设备列表
					bleControl.deviceListListen(device)
					// this.stopBluetoothDevicesDiscovery()
					// this.createBLEConnection(device)
				}
			})
		})
	},
	// 创建蓝牙连接
	createBLEConnection(device) {
		if (this.bleConnectDeviceID == null) {
			this.bleConnectDeviceID = device.address
			uni.createBLEConnection({
				deviceId: device.address,
				success: res => {
					currentDevice = device
					this.connectState = 200
					// 蓝牙连接成功，上报设备信息
					bleControl.connectStateListen({
						...BLUE_STATE.CONNECTSUCCESS,
						deviceInfo: {
							...device
						}
					})
					setTimeout(() => {
						this.getBLEDeviceServices(device)
					}, 1500)
					//取消监听，uniapp无法取消监听会多次调用
					// this.onBLEConnectionStateChange()
				},
				fail: (err) => {
					hideLoading()
					console.log(err);
					bleControl.connectStateListen(BLUE_STATE.CONNECTFAILED)
				},
			})
		}
	},
	// 监听连接状态
	onBLEConnectionStateChange() {
		uni.onBLEConnectionStateChange(res => {
			console.log('蓝牙连接状态: ', res);
			if (!res.connected) {
				bleControl.connectStateListen(BLUE_STATE.DISCONNECT)
				// this.stop()
			}
		});
	},
	// 获取设备服务
	getBLEDeviceServices(device) {
		uni.getBLEDeviceServices({
			deviceId: device.address,
			success: res => {
				for (let i = 0; i < res.services.length; i++) {
					let uuid = res.services[i].uuid
					if (uuid.indexOf("6E40FF01") == 0 || uuid.indexOf("0000180F") == 0) {
						this.getBLEDeviceCharacteristics(device, uuid)
					}
				}
			},
		})
	},
	// 获取设备特征值
	getBLEDeviceCharacteristics(device, serviceID) {
		uni.getBLEDeviceCharacteristics({
			deviceId: device.address,
			serviceId: serviceID,
			success: res => {
				bleControl.characteristicsListListen(res)
				for (let i = 0; i < res.characteristics.length; i++) {
					let item = res.characteristics[i]
					// 该特征值是否支持 write 操作
					if (item.properties.write) {
						if (item.uuid.indexOf("6E40FF02") == 0) {
							writeData = {
								device: device,
								serviceID: serviceID,
								characteristicID: item.uuid
							}
							bleControl.connectStateListen(BLUE_STATE.WRITEREADY);
						}
					}
					// 该特征值是否支持 notify或indicate 操作
					if (item.properties.notify || item.properties.indicate) {
						// if (item.uuid.indexOf("6E40FF03") == 0 || item.uuid.indexOf("00002A19") == 0) {
						if (item.uuid.indexOf("6E40FF03") == 0) {
							this.notifyBLECharacteristicValueChange(device, serviceID, item.uuid);
						}
					}
				}
			}
		})
	},
	// 监听特征值变化（设备数据变化）
	notifyBLECharacteristicValueChange(device, serviceID, characteristicID) {
		uni.notifyBLECharacteristicValueChange({
			deviceId: device.address,
			serviceId: serviceID,
			characteristicId: characteristicID,
			state: true,
			success: res => {
				this.onBLECharacteristicValueChange()
			},
		})
	},
	onBLECharacteristicValueChange() {
		uni.onBLECharacteristicValueChange(res => {
			// let value = this.ab2hex(res.value)
			this.resolvingData(res.value)
		})
	},
	readBleValue(deviceId, serviceId, characteristicId) {
		uni.readBLECharacteristicValue({
			deviceId,
			serviceId,
			characteristicId,
			success(res) {
				console.log('读取指令发送成功')
				console.log(res)
			},
			fail(err) {
				console.log('读取指令发送失败')
				console.error(err)
			},
		})
	},
	// 发送指令给设备 改完名字蓝牙设备重启 名字前面加上0
	sendOrder(name) {
		let order = '0' + name
		const length = order.length;
		const buffer = new ArrayBuffer(length);
		const view = new Uint8Array(buffer);

		for (let i = 0; i < length; i++) {
			view[i] = order.charCodeAt(i) & 0xFF;
		}
		this.writeBLECharacteristicValue(buffer, writeData.device, writeData.serviceID, writeData.characteristicID)
	},
	// 写入特征值
	writeBLECharacteristicValue(order, device, serviceID, characteristicID) {
		uni.writeBLECharacteristicValue({
			deviceId: device.address,
			serviceId: serviceID,
			characteristicId: characteristicID,
			value: order,
			writeType: 'writeNoResponse',
			success: (res) => {
				console.log("特征值写入成功 --", res)
				bleControl.writeStateListen({
					code: res.code === 0 ? 200 : -1,
				})
			},
			fail: (res) => {
				console.log("特征值写入失败 --", res)
				bleControl.writeStateListen({
					code: -1,
				})
			}
		})
	},

	// ArrayBuffer转16进制字符串
	ab2hex(buffer, split = '') {
		var hexArr = Array.prototype.map.call(new Uint8Array(buffer), function (bit) {
			return ('00' + bit.toString(16)).slice(-2)
		})
		return hexArr.join(split)
	},
	// TODO 根据自己实际业务解析数据
	resolvingData(res) {
		if (this._lastLockData == res) return
		this._lastLockData = res
		const sig = res.slice(0, 4)
		const newBuffer = res.slice(4)
		const list1 = newBuffer.slice(0, 120) //AF7原始值
		const list2 = newBuffer.slice(120, 240) //AF8原始值
		let viewA7 = new Uint8Array(list1); //AF7
		let viewA8 = new Uint8Array(list2); //AF8
		let viewSig = new Uint8Array(sig); //sig
		const buf1 = new ArrayBuffer(160);
		const buf2 = new ArrayBuffer(160);
		const buf37 = new ArrayBuffer(160);
		const buf38 = new ArrayBuffer(160);
		let dataViewA7 = new Int32Array(buf1); // 创建DataView视图修改
		let dataView2A7 = new Uint32Array(buf1); // 创建DataView视图读取
		let dataView3A7 = new Float32Array(buf37); // 创建DataView视图读取
		let dataViewA8 = new Int32Array(buf2); // 创建DataView视图修改
		let dataView2A8 = new Uint32Array(buf2); // 创建DataView视图读取
		let dataView3A8 = new Float32Array(buf38); // 创建DataView视图读取
		for (let i = 0; i < 40; i++) {
			dataView2A7[i] = (viewA7[i * 3] * 0X1000000 + viewA7[i * 3 + 1] * 0X10000 + viewA7[i * 3 + 2] * 0X100)
			dataViewA7[i] = (dataViewA7[i] / 0x100)
			dataView3A7[i] = dataViewA7[i] * (4.84 / 2 ** 24) * 10 ** 6 / 6
			dataView2A8[i] = (viewA8[i * 3] * 0X1000000 + viewA8[i * 3 + 1] * 0X10000 + viewA8[i * 3 + 2] * 0X100)
			dataViewA8[i] = (dataViewA8[i] / 0x100)
			dataView3A8[i] = dataViewA8[i] * (4.84 / 2 ** 24) * 10 ** 6 / 6
		}
		let values = {
			type: 7,
			address: currentDevice.name,
			AF7Data: Object.values(dataView3A7),
			AF8Data: Object.values(dataView3A8),
			signal: viewSig[1] === 0 ? 0 : 1,
			ele: viewSig[3] <= 100 ? viewSig[3] : viewSig[3] === 128 ? '充电中' : '充电完成', //0X80充电中 0xcc充电完成 0->0x64电量
			bagIndex: viewSig[0]
		}
		bleControl.acceptDataListen(JSON.stringify(values))
	},
}