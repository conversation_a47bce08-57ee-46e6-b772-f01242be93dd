import {
	ref
} from 'vue';
import {
	subEvalBeginTime,
	subEvalEndTime,
	subEvalBeginTimeIva,
	subEvalEndTimeIva
} from '@/service/eegassess';
// 创建一个响应式的数据引用
const newLongTime = ref(null) //时间戳
const bagIndex = ref(null) //包序
const deviceType = ref(1) //1自研设备500  2深湾设备250

//自研设备设定时间戳
export function getFakeTime(state, eceuId, type, socket) {
	const ret = JSON.parse(state)
	let newTime = new Date().getTime()
	if (!newLongTime.value) {
		deviceType.value = 1
		newLongTime.value = newTime
		addRealTime(newTime, eceuId, type)
	} else if (ret.bagIndex - bagIndex.value > 0) { //包序循环0-255
		let num = ret.bagIndex - bagIndex.value
		if (num > 1) {
			for (let i = 1; i < num; i++) {
				socket.webSocketSendMsg(JSON.stringify({
					longTime: newLongTime.value + i * 80,
					...JSON.parse(state),
					realTime: newTime
				}))
			}
		}
		newLongTime.value = (ret.bagIndex - bagIndex.value) * 80 + newLongTime.value
	} else { //到顶255
		let num = ret.bagIndex - bagIndex.value + 256
		if (num > 1) {
			for (let i = 1; i < num; i++) {
				socket.webSocketSendMsg(JSON.stringify({
					longTime: newLongTime.value + i * 80,
					...JSON.parse(state),
					realTime: newTime
				}))
			}
		}
		newLongTime.value = (ret.bagIndex - bagIndex.value + 256) * 80 + newLongTime.value
	}
	bagIndex.value = ret.bagIndex
	let value = {
		longTime: newLongTime.value,
		...JSON.parse(state),
		realTime: newTime
	}
	// console.log(value, '--------value');
	return value
}

//深湾设备
export function getFakeTimeSW(state, eceuId, type) {
	const ret = JSON.parse(state)
	let newTime = new Date().getTime()
	if (!newLongTime.value && ret.type === 7) {
		deviceType.value = 2
		newLongTime.value = newTime
		addRealTime(newTime, eceuId, type)
	} else if (ret.type === 7 && newLongTime.value) {
		newLongTime.value = newTime
	}
	let value = {
		longTime: newTime,
		...JSON.parse(state),
		realTime: newTime
	}
	// console.log(value, '--------value');
	return value
}

const addRealTime = (beginTime, eceuId, type) => {
	console.log({
		beginTime,
		eceuId,
		type
	});
	// console.log(deviceType.value, '---------------deviceType.value');
	const samplingRate = deviceType.value == 1 ? 500 : 250
	if (type === 'ivacpt') {
		subEvalBeginTimeIva({
			beginTime,
			eceuId,
			samplingRate
		}).then(res => {
			console.log(res);
		}).catch(err => {
			console.log(err);
		})
	} else {
		subEvalBeginTime({
			beginTime,
			eceuId,
			samplingRate
		}).then(res => {
			console.log(res);
		}).catch(err => {
			console.log(err);
		})
	}
}

export function resetValue() {
	newLongTime.value = null
	bagIndex.value = null
	deviceType.value = 1
}

export function addRealEndTime(eceuId, type) {
	if (!eceuId) {
		resetValue()
		return
	}
	// console.log(deviceType.value, '---------------deviceType.value');
	// console.log(type, '---------type');
	// console.log(newLongTime.value);
	// console.log(eceuId);
	const samplingRate = deviceType.value == 1 ? 500 : 250
	if (type === 'ivacpt') {
		subEvalEndTimeIva({
			endTime: newLongTime.value,
			eceuId: eceuId,
			samplingRate
		}).then(res => {
			console.log(res);
			resetValue()
		}).catch(err => {
			resetValue()
			console.log(err);
		})
	} else {
		subEvalEndTime({
			endTime: newLongTime.value,
			eceuId: eceuId,
			samplingRate
		}).then(res => {
			console.log(res);
			resetValue()
		}).catch(err => {
			resetValue()
			console.log(err);
		})
	}

}