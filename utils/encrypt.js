import JSEncrypt from 'jsencrypt'

const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwtiGGX9v8yYWu/f+QwT9
L3d/+gtl6R8dxHtkcw0RR5CCNmxePXX1oHNOO/0y9Td1bQkm5C9WsoV8fWpSodpR
YR3ZcO+qVjOLPLaO8jnEzNTUukC7blmwJdIjWTvMMoFze4w92GYZVUXeR2jaIWAK
/oKnA/PRxOgPVuewz2X5Wv+RPG6txJ+zRZCWHNrjdlHb3HaaYPhvbkFcIwWahx3y
Q+BOnj0dQsAhM9M+uvzBglW0KHBQVbz0LwMbEO5wx2A/ILdlKan05u9h8JCoNOOa
dRMnjK4d4y+teVi0xT9JeUywsQ+mP9GZYw++MY66+cCP6Xuw81shzbiUWabpQrTG
LQIDAQAB
-----END PUBLIC KEY-----
`


const PRIVATE_KEY = `************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

//加密
const rsaEncrypt = (text) => {
	const encryptor = new JSEncrypt()
	encryptor.setPublicKey(PUBLIC_KEY)
	return encryptor.encrypt(text)
}

//解密
const rsaDecrypt = (encrypted) => {
	const decrypt = new JSEncrypt();
	decrypt.setPrivateKey(PRIVATE_KEY); // 设置私钥
	return decrypt.decrypt(encrypted); // 解密数据 
}

export {
	rsaEncrypt,
	rsaDecrypt
}