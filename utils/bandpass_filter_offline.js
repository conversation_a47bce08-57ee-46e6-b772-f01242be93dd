/*
 * @Description:
 * @Author: 小雨
 * @Date: 2023-05-11 09:58:22
 * @LastEditTime: 2023-05-11 10:46:40
 * @LastEditors: 小雨
 */



/**
 * @description: 滤波器系数生成函数
 * @return {*}滤波器系数
 * @param {*} :滤波器系数生成函数
Input:
    order： 滤波器的阶次
    samplerate: 信号的采样频率
    low：低频截止频率
    high：高频截止频率
Output:
    h: 
 */
const ClacImpulseResponse = (order, samplerate, low, high) => {
	let n = order;
	let n2 = parseInt(n / 2);
	let wc1;
	let wc2;

	wc1 = (2 * Math.PI * low) / samplerate; //wc1 =2π*low/samplerate
	wc2 = (2 * Math.PI * high) / samplerate; //wc2 =2π*fhigh/samplerate
	let h = new Array(n).fill(0);
	h[n2] = (wc2 - wc1) / Math.PI;
	for (let i = 0; i < n2; i++) {
		let s = i - n2;
		h[i] = (Math.sin(wc2 * s) - Math.sin(wc1 * s)) / (Math.PI * s); //h[i] = (sin(wc2*s) - sin(wcBs)) /(π*s)
	}

	//window
	for (let i = 0; i < n2; i++) {
		let w = 0.54 - 0.46 * Math.cos((2 * i * Math.PI) / (n - 1)); // w = 0.54 - 0.46cos(2π*i/(n-1))
		h[i] = h[i] * w;
		h[n - 1 - i] = h[i];
	}
	return h;
};

const average = (...args) => args.reduce((acc, current) => acc + current / args.length); //计算平均值
const sum = (...array) => array.reduce((total, current) => total + current, 0); // 求和
export const bandpass_filter_offline = (x, coef) => {
	let avg = average(...x);
	let arr = x.map((item) => item - avg);
	let nfact = parseInt(coef.length / 2) //取商
	let temp_len = x.length + coef.length
	let temp = new Array(temp_len).fill(0)
	//补点
	for (let i = 0; i < temp.length; i++) {
		if (i < nfact) {
			temp[i] = 2 * arr[0] - arr[nfact - i]
		} else if (i < (nfact + arr.length)) {
			temp[i] = arr[i - nfact]
		} else {
			temp[i] = 2 * arr[arr.length - 1] - arr[arr.length - 2 - (i - nfact - arr.length)]
		}
	}
	// Filter signals
	//Step2：卷积计算
	let y = xcorr(temp, coef);
	return y;
};

const xcorr = (x, h) => {
	let h2 = parseInt(h.length / 2); //取商
	let hr = parseInt(h.length % 2); //取余
	let sum_h = sum(...h);
	let sum_Input = 0;
	let avg_Input = 0;
	let r = new Array(x.length - h.length).fill(0);
	for (let i = 0; i < r.length; i++) {
		sum_Input = 0
		r[i] = 0
		for (let j = 0; j < h2; j++) {
			r[i] = r[i] + (x[i + j] + x[i + h.length - 1 - j]) * h[h.length - j - 1];
			sum_Input = sum_Input + x[i + j] + x[i + h.length - 1 - j];
		}
		if (hr > 0) {
			r[i] = r[i] + x[i + h2] * h[h2];
			sum_Input = sum_Input + x[i + h2];
		}
		avg_Input = sum_Input / h.length;
		r[i] = r[i] - avg_Input * sum_h;
	}
	return r;
};

export const getLine = (arr, num) => {
	const coef = ClacImpulseResponse(251, num, 1, 35);
	return bandpass_filter_offline(arr, coef);
}