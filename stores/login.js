import {
	defineStore
} from 'pinia';
import {
	getStorageSync,
	navigateTo,
	showToast,
	setStorage,
	switchTab
} from '../common/uniTool';
import {
	loginIn,
	getVisitorList,
	getQryActiveTrainee,
	getQryTraineePrescripList,
	getAppVersion
} from '../service';
import {
	getAllRecordList
} from '../service/evaluation';
import {
	getQryPoints
} from '../service/shopping';
export const useLoginStore = defineStore('login', {
	state: () => {
		return {
			//用户信息
			userInfo: {
				username: getStorageSync("username"),
				ttoken: getStorageSync("ttoken"),
				avatar: getStorageSync("avatar"),
				mobile: getStorageSync("mobile"),
			},
			//用户绑定的亲属信息
			visitorList: [],
			recordList: [], //测评记录
			prescriptionList: [], //处方记录
			qryActiveTrainee: '', //活跃亲属
			appVersion: {}, //线上app版本号
			qryPoints: 0, // 用户脑力值数
			locationAppVersion: '',
			network: true, //是否有网络连接
			evaluatId: '', //测评id
			round: '' //测评轮次
		};
	},

	actions: {
		//获取脑力值数
		async getPoints(traineeId) {
			try {
				const data = await getQryPoints({
					traineeId,
				})
				this.qryPoints = data.data.points
			} catch (error) {
				// showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//获取线上版本号
		async getAppVersionData(path) {
			try {
				const data = await getAppVersion({
					"appName": "NYQJ"
				})
				this.appVersion = data.data
				let selfVersionCode
				plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
					selfVersionCode = wgtinfo.versionCode
					this.locationAppVersion = wgtinfo.versionCode
					console.log(wgtinfo.versionCode, '--------');
					//线上版本号高于当前，进行在线升级
					if (selfVersionCode < data.data.version) {
						let platform = uni.getSystemInfoSync().platform //手机平台
						//安卓手机弹窗升级
						if (path === 'index' && data.data.upgradeType === "0") {
							return
						}
						if (path === 'index' && !data.data.isPopup) {
							return
						}

						if (platform === 'android') {
							navigateTo('/pages/index/upgrade')
						}
						//IOS无法在线升级提示到商店下载
						else {
							uni.showModal({
								title: '发现新版本 ',
								content: '请到App store进行升级',
								showCancel: false
							})
						}
					}
				});

			} catch (error) {
				// showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//登录
		async getLoginIn(phoneNumber, smsAuthCode) {
			try {
				const data = await loginIn({
					phoneNumber,
					smsAuthCode
				})
				setStorage("mobile", data.data.mobile);
				setStorage("ttoken", data.data.ttoken);
				setStorage("username", data.data.username);
				this.userInfo = data.data
				showToast('登录成功')
				switchTab('/pages/index/index')
			} catch (error) {
				showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//就诊人列表
		async getVisitor() {
			try {
				const data = await getVisitorList()
				this.visitorList = data.data
			} catch (error) {
				if (error.code === '2009') {
					this.visitorList = []
					return
				}
				// showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//测评记录
		async getRecordList(traineeId) {
			try {
				const data = await getAllRecordList({
					traineeId: traineeId
				})
				this.recordList = data.data.records
				return data.data.records
			} catch (error) {
				if (error.code === '2009') {
					this.recordList = []
					return
				}
				// showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//就诊人信息
		async getActiveTraineeInfo(traineeId) {
			try {
				const data = await getQryActiveTrainee()
				this.qryActiveTrainee = data.data.traineeId
				return data.data.traineeId
			} catch (error) {
				if (error.code === '2009') {
					this.qryActiveTrainee = ''
					return
				}
				showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
		//处方列表
		async getTraineePrescripList(traineeId) {
			try {
				const data = await getQryTraineePrescripList({
					traineeId: traineeId
				})
				this.prescriptionList = data.data
				return data.data.records
			} catch (error) {
				if (error.code === '2009') {
					this.prescriptionList = []
					return
				}
				// showToast(error.desc || '数据获取失败，请检查网络')
				// 让表单组件显示错误
				return error
			}
		},
	},
});