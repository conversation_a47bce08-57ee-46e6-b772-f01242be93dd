<script>
	import {
		BleController,
		BLUE_STATE
	} from '@/utils/bluType';
	export default {
		onLaunch: function () {
			console.log('App Launch')
			//应用初始化完成触发(只触发一次)
			const token = uni.getStorageSync('ttoken'); //获取token
			//#ifdef APP-PLUS
			if (token) {
				//存在则关闭启动页进入首页
				plus.navigator.closeSplashscreen();
			} else {
				//不存在则跳转至登录页
				uni.reLaunch({
					url: "/pages/login/index",
					success: () => {
						plus.navigator.closeSplashscreen();
					}
				})
			}
			// 监听蓝牙适配器状态
			uni.onBluetoothAdapterStateChange(res => {
				// console.log('蓝牙适配器状态：', res);
			})
			uni.onBLEConnectionStateChange(res => {
				// console.log('蓝牙连接状态: ', res);
				if (!res.connected) {
					BleController.connectStateListen(BLUE_STATE.DISCONNECT)
					// this.stop()
				}
			});
			//#endif
		},
		onShow: function () {
			console.log('App Show')
			//#ifdef APP-PLUS
			plus.navigator.setFullscreen(true);
			//#endif
		},
		onHide: function () {
			console.log('App Hide')
			//#ifdef APP-PLUS
			plus.navigator.setFullscreen(false);
			//#endif
		}
	}
</script>

<style lang="scss">
	@import "@/uni_modules/uview-plus/index.scss";

	/*每个页面公共css */
	/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
	@font-face {
		font-family: 'iconfont';
		/* Project id 3566902 */
		src: url('/static/fonts/iconfont.ttf') format('truetype');
	}

	@font-face {
		font-family: 'DINAlternate-Bold';
		src: url('/static/fonts/DIN.ttf');
	}

	@font-face {
		font-family: 'font-game';
		src: url('/static/fonts/GAME.ttf');
	}

	@font-face {
		font-family: 'YouSheBiaoTiHei';
		src: url('/static/fonts/YouSheBiaoTiHei.ttf');
	}

	view,
	text,
	scroll-view,
	input,
	button,
	image,
	cover-view {
		box-sizing: border-box;
	}

	image {
		width: 100%;
		height: auto;
	}

	page {
		position: relative;
	}

	.iconfont {
		font-family: "iconfont";
	}

	.DINAlternate-Bold {
		font-family: 'DINAlternate-Bold';
	}

	.YouSheBiaoTiHei {
		font-family: 'YouSheBiaoTiHei';
	}


	.font-game {
		font-family: 'font-game';
	}

	.center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.bgImg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: -1;
	}

	::v-deep .uni-progress-info {
		display: none !important;
	}
</style>