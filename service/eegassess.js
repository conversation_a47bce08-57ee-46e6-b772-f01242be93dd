import httpRequest from '@/utils/interceptors.js';


/**
 * @description: 提交采集信息
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvaluationInfo = (data) => {
	return httpRequest.post("screen/eegassess/v2.2.0/subEvaluationInfo", data);
};

/**
 * @description: 记录脑电采集事件真实开始时间
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalBeginTime = (data) => {
	return httpRequest.post("screen/interaction/v2.2.0/subEvalBeginTime", data);
};

/**
 * @description: 记录脑电采集事件真实开始时间ivacpt
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalBeginTimeIva = (data) => {
	return httpRequest.post("scientific/interaction/v2.2.0/subEvalBeginTime", data);
};

/**
 * @description: 记录脑电采集事件真实结束时间
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalEndTime = (data) => {
	return httpRequest.post("screen/interaction/v2.2.0/subEvalEndTime", data);
};

/**
 * @description: 记录脑电采集事件真实结束时间ivacpt
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalEndTimeIva = (data) => {
	return httpRequest.post("scientific/interaction/v2.2.0/subEvalEndTime", data);
};


/**
 * @description: 更新结束时间
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const uptEndDate = (data) => {
	return httpRequest.post("screen/eegassess/v1.2.2/uptEndDate", data);
};