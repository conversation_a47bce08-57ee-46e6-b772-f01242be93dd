/*
 * @Description:游戏接口请求
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';


/**
 * @description: v2.0.0初始化游戏
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const initTrainPlain = (data) => {
	return httpRequest.post('screen/course/v2.0.0/initTrainPlain', data);
};

/**
 * @description: 提交游戏数据v2.3.2版本
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const addCourseTrainData = (data) => {
	return httpRequest.post('screen/course/v2.3.2/addCourseTrainData', data, {
		hideLoading: true
	});
};

/**
 * @description: 亲属课程列表
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryTrainDayList = (data) => {
	return httpRequest.post('screen/course/qryTrainDayList', data);
};


/**
 * @description: 查询课程详情
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryCourseTrainDetail = (data) => {
	return httpRequest.post('screen/course/qryCourseTrainDetail', data);
};

/**
 * @description: v2.3.2查询游戏中载具皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getGameCarrierSkin = (data) => {
	return httpRequest.post('screen/carrier/v2.3.2/qryGameCarrierSkin', data);
};



/**
 * @description: 随机获取题库题目
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getRandomQuestion = (data) => {
	return httpRequest.post('screen/course/getRandomQuestion', data);
};



/**
 * @description: 上传奖励空间做题数据
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const addQuestionTrainData = (data) => {
	return httpRequest.post('screen/course/addQuestionTrainData', data);
};


/**
 * @description: v1.2.1查询奖励空间和课程训练详情
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryTrainDetail = (data) => {
	return httpRequest.post('screen/course/v1.2.1/qryTrainDetail', data, {
		hideLoading: true
	});
};

/**
 * @description: v2.0.0查询是否有新的皮肤碎片
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryNewSkinDebris = (data) => {
	return httpRequest.post('screen/course/v2.0.0/qryNewSkinDebris', data);
};

/**
 * @description: v2.3.2更新皮肤碎片消息已读
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const updateRead = (data) => {
	return httpRequest.post('screen/course/v2.3.2/updateRead', data);
};


/**
 * @description: v2.3.2查询默认载具和皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryDefaultSkinDebris = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryDefaultSkinDebris', data, {
		hideLoading: true
	});
};


/**
 * @description: v2.2.0查询获取皮肤碎片历史记录
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQrySkinDebrisHistory = (data) => {
	return httpRequest.post('screen/course/v2.2.0/qrySkinDebrisHistory', data);
};


/**
 * @description: v2.0.0兑换皮肤碎片
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const convertSkinDebris = (data) => {
	return httpRequest.post('screen/course/v2.0.0/convertSkinDebris', data);
};

/**
 * @description: v2.3.2切换皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const changeCarrierSkin = (data) => {
	return httpRequest.post('screen/course/v2.3.2/changeCarrierSkin', data);
};


/**
 * @description: v2.0.0查询距离下次获取皮肤的天数
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryNextSkinDay = (data) => {
	return httpRequest.post('screen/course/v2.0.0/qryNextSkinDay', data);
};


/**
 * @description: 记录课程训练开始时间（subCourseTrainBeginDate）
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const subCourseTrainBeginDate = (data) => {
	return httpRequest.post('screen/course/v2.2.0/subCourseTrainBeginDate', data);
};


/**
 * @description: 查询获取皮肤召唤记录（qrySkinSummonHistory）（新增） 等级路径
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqrySkinSummonHistory = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qrySkinSummonHistory', data, {
		hideLoading: true
	});
};



/**
 * @description: 查询是否有新的皮肤召唤卡（qryNews）（新增） 小宝箱
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryNews = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryNews', data);
};


/**
 * @description:查询距离下次等级的时长（qryNextLevelTime）（新增）距离等级还差
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryNextLevelTime = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryNextLevelTime', data);
};

/**
 * @description:获取广播（qryBroadcastList）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryBroadcastList = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryBroadcastList', data);
};