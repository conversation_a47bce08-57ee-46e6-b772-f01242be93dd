/*
 * @Description:主页面请求
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 获取用户信息
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getUserInfo = () => {
	return httpRequest.post('user/info');
};

/**
 * @description: 获取手机验证码
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const getSmsAuthCode = (data) => {
	return httpRequest.post('login/getSmsAuthCode', data);
};

/**
 * @description: 登录查询用户信息
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const loginIn = (data) => {
	return httpRequest.post('login/loginIn', data, {
		header: {
			'sourceType': 'SMSCODE',
			'mpType': 'a'
		},
	});
};

/**
 * @description: 查询亲属列表信息
 * @author: 小雨
 * @return {*}
 * @param {*} data 
 */
export const getVisitorList = (data) => {
	return httpRequest.post('trainee/qryTraineeInfoList', data);
};

/**
 * @description: 更改亲属信息
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const changeVisitorInfo = (data) => {
	return httpRequest.post('trainee/updateInfo', data);
};

/**
 * @description: 获取亲属信息详情
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getVisitorDetail = (data) => {
	return httpRequest.post('trainee/qryTraineeInfoDetail', data);
};

/**
 * @description: 删除亲属
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const deleteVisitorDetail = (data) => {
	return httpRequest.post('trainee/deleteInfo', data);
};


/**
 * @description: 获取活跃亲属
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryActiveTrainee = (data) => {
	return httpRequest.post('trainee/qryActiveTrainee', data, {
		hideLoading: true
	});
};

/**
 * @description: 更新活跃亲属
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const updateQryActiveTrainee = (data) => {
	return httpRequest.post('trainee/updateActiveTrainee', data, {
		hideLoading: true
	});
};

/**
 * @description: 获取最新一个报告结果
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getLastEvaluationResult = (data) => {
	return httpRequest.post('screen/free/getLastEvaluationResult', data, {
		hideLoading: true
	});
};


/**
 * @description: 查询亲属是否开处方
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryIsHavePres = (data) => {
	return httpRequest.post('screen/train/qryIsHavePres', data);
};


/**
 * @description: 查询是否有最新处方
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryNewPrescripTrain = (data) => {
	return httpRequest.post('screen/train/qryNewPrescripTrain', data);
};


/**
 * @description: 查询最新处方pdf
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryTraineePrescripPdf = (data) => {
	return httpRequest.post('screen/train/qryTraineePrescripPdf', data);
};


/**
 * @description: 查询亲属处方列表
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryTraineePrescripList = (data) => {
	return httpRequest.post('screen/train/qryTraineePrescripList', data);
};

/**
 * @description: 查询亲属训练效率列表
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryPresTrainEffList = (data) => {
	return httpRequest.post('screen/train/qryPresTrainEffList', data);
};

/**
 * @description: 查询亲属训练效率详情
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getQryPresTrainEffDetail = (data) => {
	return httpRequest.post('screen/train/v1.2.1/qryPresTrainEffDetail', data);
};


/**
 * @description: 获取app版本
 * @author: 小雨
 * @return {*}
 * @param {*} data  traineeId
 */
export const getAppVersion = (data) => {
	return httpRequest.post('version/getAppVersion', data);
};

/**
 * @description:  新增App版本升级记录
 * @author: 小雨
 * @return {*}
 * @param {*}
 */
export const addUpdateRecord = (data) => {
	return httpRequest.post('version/addUpdateRecord', data);
};

/**
 * @description: 走势图
 * @author: 小雨
 * @return {*}
 * @param {*}
 */
export const getQryTrainTendencyChart = (data) => {
	return httpRequest.post('screen/course/qryTrainTendencyChart', data);
};


/**
 * @description: 校验患者是否已经召唤生肖（checkExistCarrier）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*}
 */
export const checkExistCarrier = (data) => {
	return httpRequest.post('screen/course/v2.3.2/checkExistCarrier', data, {
		hideLoading: true
	});
};

/**
 * @description: 获取EEG设备连接时 X-CSRF-TOKEN（getCsrfToken）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*}
 */
export const getCsrfToken = (data) => {
	function generateBatchNum() {
		const timestamp = Date.now(); // 毫秒时间戳
		const random = Math.floor(1000 + Math.random() * 9000); // 4位随机数
		const raw = `EEG${timestamp}${random}`; // 拼接格式
		const encoded = btoa(raw); // Base64 编码
		return encoded;
	}
	return httpRequest.post('equipment/check/v2.5.2/getCsrfToken', {}, {
		header: {
			'batchNum': generateBatchNum(),
		},
	});
};