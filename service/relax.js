/*
 * @Description:主页面请求
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 获取休息课程结果
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const qryRelaxTrainData = (data) => {
	return httpRequest.post('screen/relax/qryRelaxTrainDataList', data);
};

/**
 * @description: 动画城扣除积分2.4.3
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const changeSubPoints = (data) => {
	return httpRequest.post('screen/points/2.4.3/subPoints', data);
};