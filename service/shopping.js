/*
 * @Description:商城接口请求
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';


/**
 * @description: 查询载具
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getTraineeCarrier = (data) => {
	return httpRequest.post('screen/carrier/qryTraineeCarrier', data);
};
/**
 * @description: 兑换载具
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const changebuyCarrier = (data) => {
	return httpRequest.post('screen/carrier/buyCarrier', data);
};

/**
 * @description: 切换载具
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const changeUseCarrier = (data) => {
	return httpRequest.post('screen/carrier/useCarrier', data);
};
/**
 * @description: 查询载具皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getTraineeCarrierSkin = (data) => {
	return httpRequest.post('screen/carrier/qryTraineeCarrierSkin', data);
};

/**
 * @description: 兑换载具皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const changeBuySkin = (data) => {
	return httpRequest.post('screen/carrier/buySkin', data);
};
/**
 * @description: 切换载具皮肤
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const changeUseSkin = (data) => {
	return httpRequest.post('screen/carrier/useSkin', data);
};
/**
 * @description: 查询用户积分
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getQryPoints = (data) => {
	return httpRequest.post('screen/points/qryPoints', data);
};


/**
 * @description: 查询获得脑力值记录
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryPointsRecordsList = (data) => {
	return httpRequest.post('screen/points/qryPointsRecordsList', data);
};


/**
 * @description: v2.3.2查询使用中载具皮肤图标
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryUseCarrierSkinIcon = (data) => {
	return httpRequest.post('screen/carrier/v2.3.2/qryUseCarrierSkinIcon', data);
};

/**
 * @description: 查询所有角色默认皮肤（qryAllDefaultSkinList）（新增）生肖库
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryAllDefaultSkinList = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryAllDefaultSkinList', data);
};

/**
 * @description: 获取角色下所有皮肤（qryCarrierAllSkinList）（新增）皮肤库
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getqryCarrierAllSkinList = (data) => {
	return httpRequest.post('screen/course/v2.3.2/qryCarrierAllSkinList', data);
};


/**
 * @description: 召唤皮肤（summonCarrierSkin）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const summonCarrierSkin = (data) => {
	return httpRequest.post('screen/course/v2.3.2/summonCarrierSkin', data);
};