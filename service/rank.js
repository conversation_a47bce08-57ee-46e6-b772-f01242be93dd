/*
 * @Description:排行榜请求接口
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 查询专注数据排行
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getFocusRanking = (data) => {
	return httpRequest.post('screen/ranking/qryFocusRanking', data);
};

/**
 * @description: 查询抑制数据排行榜
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getStopRanking = (data) => {
	return httpRequest.post('screen/ranking/qryStopRanking', data);
};
/**
 * @description: 查询反应数据排行榜
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getNormalRanking = (data) => {
	return httpRequest.post('screen/ranking/qryNormalRanking', data);
};

/**
 * @description: 查询跑动距离排行榜
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getRunDisRanking = (data) => {
	return httpRequest.post('screen/ranking/qryRunDisRanking', data);
};


/**
 * @description: 点赞+1
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const addThumbsUp = (data) => {
	return httpRequest.post('screen/ranking/addThumbsUp', data, {
		hideLoading: true
	});
};