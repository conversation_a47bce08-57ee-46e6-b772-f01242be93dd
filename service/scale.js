/*
 * @Description:量表
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';


/**
 * @description: 18道题提交
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const postQuestionnaire = (data) => {
	return httpRequest.post('screen/free/v2.5.0/addNewQuestionRec', data);
};

/**
 * @description: 个人信息提交
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const postInfo = (data) => {
	return httpRequest.post("trainee/addinfo", data);
};

/**
 * @description:查询pptv免费测评结果pdf
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const qryFreeEvaResultPDF = (data) => {
	return httpRequest.post('screen/ppvt/exportEvaLpdf', data);
};



/**
 * @description: 校验年龄
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const checkBirth = (data) => {
	return httpRequest.post("screen/ppvt/checkBirth", data);
};

/**
 * @description: 新增pptv免费测评信息
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const addEvaLInfo = (data) => {
	return httpRequest.post("screen/ppvt/addEvaLInfo", data);
};