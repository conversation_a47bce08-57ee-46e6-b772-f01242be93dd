import httpRequest from '@/utils/interceptors.js';
/**
 * @description: 追踪罗伯特图片
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const getRandomPic = (data) => {
	return httpRequest.get("screen/free/getRandomPic", data);
};


/**
 * @description: v2.5.0获取免费的当前伦茨的筛查的进度
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const getFreeScreenProgress = (data) => {
	return httpRequest.post("screen/free/v2.5.0/qryFreeScreenProgress", data);
};

/**
 * @description: 重置筛查测试结果
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const resetFreeAddRec = (data) => {
	return httpRequest.post("screen/free/newFreeRound", data);
};

/**
 * @description: 提交筛查测试结果
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const postFreeAddRec = (data) => {
	return httpRequest.post("screen/free/v2.5.0/addNewFreeRec", data);
};

/**
 * @description: 提交测试开始时间
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const postFreeAddRecTime = (data) => {
	return httpRequest.post("screen/free/v2.2.0/addAllStepDate", data);
};


/**
 * @description:v2.5.0获取测评记录列表
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const getAllRecordList = (data) => {
	return httpRequest.post("screen/free/v2.5.0/listAllRec", data);
};



/**
 * @description: 获取测评次数
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const getEnvalueNum = (data) => {
	return httpRequest.post("screen/free/getEnvalueTimes", data);
};


/**
 * @description: 获取划消随机数
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const getCancetRandNum = (data) => {
	return httpRequest.post("screen/free/v2.0.0/getCancetRandNum", data);
};

/**
 * @description: 手动结束测评任务（handStopFreeScreenProgress）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const handStopFreeScreenProgress = (data) => {
	return httpRequest.post("screen/free/v2.5.0/handStopFreeScreenProgress", data);
};