/*
 * @Description:全局变量
 * @Author: 小雨
 * @Date: 2023-03-09 17:06:49
 * @LastEditTime: 2023-03-09 17:07:51
 * @LastEditors: 小雨
 */

import {
	ref
} from "vue";

let baseUrl = '';
let reportUrl = '';
let wsUrl = '';
let pdfUrl = ''
let DbayLineNumFull = 1750
let CTLineNumFull = 3520
let DbayLineNum = 1500
let CTLineNum = 3000
let address = ref('')
console.log(process.env.NODE_ENV);
if (process.env.NODE_ENV == 'development') {
	// baseUrl = 'http://**************:9900/bw/';
	// reportUrl = 'http://**************:9900/report/' //报告
	// wsUrl = 'ws://**************:9900/bw/deviceLink' //websocket
	// pdfUrl = 'http://**************:9900/'
	// wsUrl = 'ws://*************:9901/deviceLink' //websocket
	// baseUrl = 'http://*************:9901/';
	// pdfUrl = 'http://*************:9900/'
	wsUrl = 'wss://*************:9900/bw/deviceLink' //websocket
	baseUrl = 'https://*************:9900/bw/';
	reportUrl = 'https://*************:9900/testReport/' //报告
	pdfUrl = 'https://*************:9900/'

} else {
	baseUrl = 'https://nf.brainwonder.cn/bw/';
	reportUrl = 'https://nf.brainwonder.cn/report/' //报告
	wsUrl = 'wss://nf.brainwonder.cn/bw/deviceLink' //websocket
	pdfUrl = 'https://nf.brainwonder.cn/'
}
const changeAddress = (value) => {
	address.value = value
}

export {
	baseUrl,
	reportUrl,
	wsUrl,
	pdfUrl,
	CTLineNum,
	DbayLineNum,
	CTLineNumFull,
	DbayLineNumFull,
	changeAddress,
	address
};