/*
 * @Description:
 * @Author: 小雨
 * @Date: 2022-08-11 10:08:56
 * @LastEditTime: 2022-11-16 14:43:36
 * @LastEditors: 小雨
 */

//随机1~9随机数
export const ram = () => Math.ceil((Math.random() * 1000000000) % 9);

//随机长度为range的数组范围1~length数组不重复
export const rangeRam = (range) => {
	const ramArr = [];
	while (ramArr.length < range) {
		let r = ram();
		if (ramArr.indexOf(r) === -1) {
			ramArr.push(r);
		}
	}
	return ramArr;
};
//随机长度为range的数组范围1~length数组不重复
export const rangeArrRam = (range, value) => {
	const ramArr = [];
	while (ramArr.length < range) {
		let r = value;
		if (ramArr.indexOf(r) === -1) {
			ramArr.push(r);
		}
	}
	return ramArr;
};
//生成从minNum到maxNum的随机数
export function randomNum(minNum, maxNum) {
	switch (arguments.length) {
		case 1:
			return parseInt(Math.random() * minNum + 1, 10);
			break;
		case 2:
			return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
			break;
		default:
			return 0;
			break;
	}
}

//js实现将秒数格式化为HH:MM:SS的形式
export function formatSeconds(value, isHour = true) {
	let secondTime = parseInt(value);
	let minuteTime = 0;
	let hourTime = 0;
	if (secondTime >= 60) {
		minuteTime = parseInt(secondTime / 60);
		secondTime = parseInt(secondTime % 60);
		if (minuteTime >= 60) {
			hourTime = parseInt(minuteTime / 60);
			minuteTime = parseInt(minuteTime % 60);
		}
	}
	// 补0
	hourTime = hourTime < 10 ? "0" + hourTime : hourTime;
	minuteTime = minuteTime < 10 ? "0" + minuteTime : minuteTime;
	secondTime = secondTime < 10 ? "0" + secondTime : secondTime;
	let res = hourTime + ":" + minuteTime + ":" + secondTime;
	if (!isHour) {
		res = minuteTime + ":" + secondTime;
	}
	return res;
}

//  秒数转化为时分秒
export function formatTextSeconds(value) {
	//  秒
	let second = parseInt(value);
	//  分
	let minute = 0;
	//  小时
	let hour = 0;
	//  天
	//  let day = 0
	//  如果秒数大于60，将秒数转换成整数
	if (second > 60) {
		//  获取分钟，除以60取整数，得到整数分钟
		minute = parseInt(second / 60);
		//  获取秒数，秒数取佘，得到整数秒数
		second = parseInt(second % 60);
		//  如果分钟大于60，将分钟转换成小时
		if (minute > 60) {
			//  获取小时，获取分钟除以60，得到整数小时
			hour = parseInt(minute / 60);
			//  获取小时后取佘的分，获取分钟除以60取佘的分
			minute = parseInt(minute % 60);
			//  如果小时大于24，将小时转换成天
			//  if (hour > 23) {
			//    //  获取天数，获取小时除以24，得到整天数
			//    day = parseInt(hour / 24)
			//    //  获取天数后取余的小时，获取小时除以24取余的小时
			//    hour = parseInt(hour % 24)
			//  }
		}
	}

	let result = "" + parseInt(second) + "秒";
	if (minute > 0) {
		result = "" + parseInt(minute) + "分" + result;
	}
	if (hour > 0) {
		result = "" + parseInt(hour) + "小时" + result;
	}
	//  if (day > 0) {
	//    result = '' + parseInt(day) + '天' + result
	//  }
	return result;
}

// 获取页面URL参数
export function getLocationParams(name) {
	//获取页面栈
	const pages = getCurrentPages();
	//获取路由参数
	const curPage = pages[pages.length - 1];
	return name ? curPage.options[name] : curPage.options;
}


// 判断字符串是否为JSON格式
export function isJSON(str) {
	if (typeof str == 'string') {
		try {
			var obj = JSON.parse(str);
			if (typeof obj == 'object' && obj) {
				return true;
			} else {
				return false;
			}

		} catch (e) {
			// console.log('error：'+str+'!!!'+e);
			return false;
		}
	}
	// console.log('It is not a string!')
}

//是否为手机号
export function isMobile(mobile) {
	return /^1[3,4,5,6,7,8,9][0-9]{9}$/.test(mobile)
}

//患者关系
export function getRela(rela) {
	switch (rela) {
		case 1:
			return '父子'
			break;
		case 2:
			return '父女'
			break;
		case 3:
			return '母子'
			break;
		case 4:
			return '母女'
			break;
		case 5:
			return '祖孙'
			break;
		case 6:
			return '其他'
			break;
		default:
			return '本人'
			break;
	}
}

//通过身份证号计算年龄、性别、出生日期
export const IdCard = (UUserCard, num) => {
	console.log(UUserCard, '------UUserCard');
	console.log(num, '------num');
	if (num == 1) {
		//获取出生日期
		return UUserCard.substring(6, 10) + "-" + UUserCard.substring(10, 12) + "-" + UUserCard.substring(12, 14);;
	}
	if (num == 2) {
		//获取性别
		if (parseInt(UUserCard.substr(16, 1)) % 2 == 1) {
			//男
			return 1;
		} else {
			//女
			return 2;
		}
	}
	if (num == 3) {
		//获取年龄
		var myDate = new Date();
		var month = myDate.getMonth() + 1;
		var day = myDate.getDate();
		var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1;
		if (UUserCard.substring(10, 12) < month || UUserCard.substring(10, 12) == month && UUserCard.substring(12, 14) <= day) {
			age++;
		}
		return age;
	}
}


export function debounce(fn, delay) {
	let time = null; //time用来控制事件的触发
	return function () {
		if (time !== null) {
			clearTimeout(time);
		}
		time = setTimeout(() => {
			fn.call(this);
			//利用call(),让this的指针从指向window 转成指向input
		}, delay)
	}
}

export function timestampToTime(timestamp, type) {
	var date = new Date(type ? timestamp : timestamp * 1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
	var Y = date.getFullYear() + '-';
	var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
	var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
	return Y + M + D;
}

export function throttle(func, wait) {

	var timer = null;
	var startTime = Date.now();

	return function () {
		var curTime = Date.now();
		var remaining = wait - (curTime - startTime);
		var context = this;
		var args = arguments;

		clearTimeout(timer);

		if (remaining <= 0) {
			func.apply(context, args);

			startTime = Date.now();

		} else {
			timer = setTimeout(fun, remaining); // 如果小于wait 保证在差值时间后执行
		}
	}
}

export function hex2int(hex) {
	let len = hex.length,
		a = new Array(len),
		code;
	for (let i = 0; i < len; i++) {
		code = hex.charCodeAt(i);
		if (48 <= code && code < 58) {
			code -= 48;
		} else {
			code = (code & 0xdf) - 65 + 10;
		}
		a[i] = code;
	}

	return a.reduce(function (acc, c) {
		acc = 16 * acc + c;
		return acc;
	}, 0);
}

//阿拉伯数字转大写，整数转大写
export const numToCapital = (num, type = '') => {
	if (!num) return 0
	const strNum = Number((num + '').replace(/[,，]*/g, '')) + '' // 记录字符
	num = parseInt(Number(strNum)) // 转为整数，
	let capitalAr = '零一二三四五六七八九十'
	let unitAr = ['十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千']
	if (type) {
		capitalAr = '零壹贰叁肆伍陆柒捌玖拾'
		unitAr = ['拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟'] // 单位
	}
	const resultAr = [] // 记录结果，后边json.in就可
	let index = strNum.length - 1 //记录位数
	let idx = 0 // 记录单位
	let percent = 10
	const turnNum = (num, percent, index) => {
		const unit = num / percent
		const capital = capitalAr[Number(strNum[index])]
		if (unit < 1) {
			resultAr.push(capital)
			// 出现11【一十一】这种情况
			if (Number(strNum[index]) === 1 && (strNum.length === 2 || strNum.length === 6 || strNum.length === 10)) {
				resultAr.pop()
			}
			return false //结束递归
		} else {
			if (capital === '零') {
				// 万和亿单位不删除
				if (!['万', '亿'].includes(resultAr[resultAr.length - 1])) {
					resultAr.pop()
				}
				// 前面有零在删掉一个零
				if (resultAr[resultAr.length - 1] === '零') {
					resultAr.pop()
				}
			}
			resultAr.push(capital)
			// 过滤存在【零万】【零亿】这种情况
			if (['万', '亿'].includes(resultAr[resultAr.length - 2]) && capital === '零') {
				resultAr.pop()
			}
			// 过滤【1亿万】这种情况
			if (resultAr[0] === '万' && resultAr[1] === '亿') {
				resultAr.shift()
			}
			// 末尾【零】删掉
			if (resultAr[0] === '零') {
				resultAr.pop()
			}
			resultAr.push(unitAr[idx++])
			turnNum(num, percent * 10, --index)
		}
	}
	turnNum(num, percent, index)
	return resultAr.reverse().join('')
}