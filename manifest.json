{
    "name" : "脑域奇迹",
    "appid" : "__UNI__4A6D4C7",
    "description" : "",
    "versionName" : "2.6.1",
    "versionCode" : 261,
    "transformPx" : false,
    "channel_list" : [
        {
            "id" : "C001",
            "name" : "脑域奇迹线上"
        },
        {
            "id" : "C001TX",
            "name" : "腾讯应用宝"
        },
        {
            "id" : "C001MI",
            "name" : "小米应用商店"
        },
        {
            "id" : "C002",
            "name" : "杭州师范大学附属医院"
        },
        {
            "id" : "C003",
            "name" : "上海东恩"
        },
        {
            "id" : "C004",
            "name" : "宁波童杏儿科"
        },
        {
            "id" : "C005",
            "name" : "雅恩健康"
        },
        {
            "id" : "C006",
            "name" : "邻家好医"
        },
        {
            "id" : "C007",
            "name" : "全纳儿童能力训练中心"
        },
        {
            "id" : "C008",
            "name" : "长沙叶义言儿科"
        },
        {
            "id" : "C009",
            "name" : "深圳真灵佑"
        },
        {
            "id" : "C010",
            "name" : "慈禄医疗"
        },
        {
            "id" : "C011",
            "name" : "曼朗心邻"
        },
        {
            "id" : "C012",
            "name" : "广东省第二人民医院"
        },
        {
            "id" : "C013",
            "name" : "童杏儿科"
        },
        {
            "id" : "C014",
            "name" : "浙江省立同德医院"
        }
    ],
    /* 5+App特有相关 */
    "app-plus" : {
        "runmode" : "liberate",
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true,
            "autoclose" : false,
            "delay" : 0
        },
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        /* 模块配置 */
        "modules" : {
            "VideoPlayer" : {},
            "Bluetooth" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            // 实现横屏
            "orientation" : [
                // 竖屏正方向
                "portrait-primary",
                // 竖屏反方向
                "portrait-secondary",
                // 横屏正方向
                "landscape-primary",
                // 横屏反方向
                "landscape-secondary"
            ],
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "minSdkVersion" : 22,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "targetSdkVersion" : 31
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common"
            }
        },
        "nativePlugins" : {}
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "2"
    },
    "vueVersion" : "3"
}
